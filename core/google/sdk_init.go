package google

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"strings"

	"cloud.google.com/go/profiler"
	"cloud.google.com/go/pubsub"
	"cloud.google.com/go/storage"
	"gitlab.com/uniqdev/backend/api-report/models"
)

var storageClient *storage.Client
var pubsubClient *pubsub.Client

func init() {
	credPath := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")
	fmt.Println(credPath)
	var err error
	ctx := context.Background()
	storageClient, err = storage.NewClient(ctx)
	if err != nil {
		fmt.Println("init storage clien err", err)
	}

	projectId := os.Getenv("PROJECT_ID")
	if projectId == "" {
		serviceAccount, err := ReadServiceAccount(credPath)
		if err == nil {
			projectId = serviceAccount.ProjectID
		} else {
			fmt.Println("reading credential file err: ", err)
		}
	}

	fmt.Println("project id: ", projectId)
	pubsubClient, err = pubsub.NewClient(ctx, projectId)
	if err != nil {
		fmt.Println("init pubsub client err ", err)
	}

	fmt.Println("sdk init finish...")
}

func Profiler() profiler.Config {
	// serviceAccountPath := "config/credentials/gcloud_service_account.json"
	// os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", serviceAccountPath)

	fmt.Println("init google sdk profiler...")
	serviceAccountPath := os.Getenv("GOOGLE_APPLICATION_CREDENTIALS")
	serviceAccount, err := ReadServiceAccount(serviceAccountPath)
	if err != nil {
		fmt.Println("read service account for profile err: ", err)
	}

	cfg := profiler.Config{
		Service:        strings.TrimSpace(fmt.Sprintf("api-report-%v", os.Getenv("ENV"))),
		ServiceVersion: "1.0.0",
		ProjectID:      serviceAccount.ProjectID,
	}
	return cfg
}

// read service account (json format) from file
func ReadServiceAccount(keyPath string) (models.ServiceAccount, error) {
	file, err := os.Open(keyPath)
	if err != nil {
		return models.ServiceAccount{}, err
	}
	defer file.Close()
	result, err := io.ReadAll(file)
	if err == nil {
		var serviceAccount models.ServiceAccount
		err = json.Unmarshal(result, &serviceAccount)
		return serviceAccount, err
	}
	return models.ServiceAccount{}, err
}
