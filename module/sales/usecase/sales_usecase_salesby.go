package usecase

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (s *salesUseCase) FetchSalesByMedia(user domain.User, request domain.SalesReportMediaRequest) (map[string]any, error) {
	if request.Offset > 0 && request.OffsetId == 0 {
		return map[string]any{"data": map[string]any{}}, nil
	}

	if timeZoneStr := cast.ToString(request.TimeZone); len(timeZoneStr) > 5 {
		request.TimeZone = cast.ToInt(timeZoneStr[:5])
	}

	//to make sure getting latest data, becuase we use cache
	if request.EndDate > time.Now().UnixMilli() {
		request.EndDate = time.Now().UnixMilli()
	}

	for i, method := range request.PaymentMethod {
		request.PaymentMethod[i] = strings.ToUpper(method)
	}

	log.Info("FetchSalesByMedia, request: %v", utils.SimplyToJson(request))

	// repo := s.pickRepo(request.EndDate)
	salesByMedia, err := s.repoPrimary.FetchSalesByMedia(user, request)
	if err != nil {
		return nil, err
	}

	// log.Info("salesByMedia: %v", utils.SimplyToJson(salesByMedia))

	if len(salesByMedia) == 0 {
		return map[string]any{"data": map[string]any{}}, nil
	}

	//fetching bank account
	banks, err := s.repoPrimary.FetchBank(user)
	if err != nil {
		return nil, err
	}
	bankMap := array.FlatMapArray(banks, "bank_id")

	//fetching outlet
	outletIdMap := make(map[string]any)
	for _, id := range request.Outlet {
		outletIdMap[cast.ToString(id)] = 0
	}
	for _, row := range salesByMedia {
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
	}

	log.Info("outlet: %v | outlets", outletIdMap)
	outletIds := array.GetKeys(outletIdMap)
	// log.Info("salesBy: %v", utils.SimplyToJson(salesByMedia))
	outlets, _ := s.repoPrimary.FetchOutletByIds(outletIds...)
	// outletsMap := array.FlatMapArray(outlets, "outlet_id")

	totalByOutlet, err := s.repoPrimary.FetchSalesTotalByOutlet(request.StartDate, request.EndDate, outletIds...)
	log.IfError(err)

	result := make([]map[string]any, 0)
	total := 0
	totalTransaction := 0
	totalAllMap := make(map[int]int)
	totalTransactionAllMap := make(map[int]int)
	dataByOutlet := make(map[string]map[string]any)

	//id 0 is for total all
	totalAllMap[0] = 0
	totalTransactionAllMap[0] = 0

	for _, row := range totalByOutlet {
		totalAllMap[cast.ToInt(row["outlet_fkid"])] = cast.ToInt(row["total"])
		totalTransactionAllMap[cast.ToInt(row["outlet_fkid"])] = cast.ToInt(row["total_transaction"])
		totalAllMap[0] += cast.ToInt(row["total"])
		totalTransactionAllMap[0] += cast.ToInt(row["total_transaction"])
	}

	defaultGroup := make(map[string]map[string]any)
	for _, row := range outlets {
		defaultGroup[cast.ToString(row["outlet_id"])] = map[string]any{
			"total":             0,
			"total_transaction": 0,
			"outlet_name":       row["name"],
			"outlet_fkid":       row["outlet_id"],
		}
		// totalAllMap[cast.ToInt(row["outlet_id"])] = 0
	}

	for k, v := range defaultGroup {
		dataByOutlet[k] = v
	}

	log.Info("default: %v", defaultGroup)

	offsetId := 0
	for i, row := range salesByMedia {
		//if bank_fkid present, its none cash, we set the payment as the bank name
		if bankdId := cast.ToString(cast.ToInt(row["bank_fkid"])); bankdId != "" && bankdId != "0" {
			log.Info("bankId %v -> %v", bankdId, bankMap[bankdId])
			salesByMedia[i]["payment"] = bankMap[bankdId]["name"]
		}
		if maxTimeCreated := cast.ToInt(row["max_time_created"]); maxTimeCreated > offsetId {
			offsetId = maxTimeCreated
		}
	}

	if request.GroupKey == "outlet" {
		for i, row := range salesByMedia {
			// if bankdId := cast.ToString(row["bank_fkid"]); bankdId != "" {
			// 	row["payment"] = bankMap[bankdId]["name"]
			// }

			total += cast.ToInt(row["total"])
			totalTransaction += cast.ToInt(row["total_transaction"])
			dataByOutlet[cast.ToString(cast.ToInt(row["outlet_fkid"]))] = array.TakeOnly(row, "total", "total_transaction", "outlet_name", "outlet_fkid")
			// totalAllMap[cast.ToInt(row["outlet_fkid"])] += cast.ToInt(row["total"])

			if i == len(salesByMedia)-1 || (row["created_at"] != salesByMedia[i+1]["created_at"] || row["bank_fkid"] != salesByMedia[i+1]["bank_fkid"]) {
				row["outlet"] = array.TakeValues(dataByOutlet)
				row["total"] = total
				row["total_transaction"] = totalTransaction
				result = append(result, array.TakeOnly(row, "payment", "created_at", "total", "total_transaction", "outlet", "bank_fkid"))

				//reset variable
				// totalAllMap[0] += total
				total = 0
				totalTransaction = 0
				for k, v := range defaultGroup {
					dataByOutlet[k] = v
				}
			}
		}
	} else {
		result = salesByMedia
	}

	//calculate percentage
	if request.GroupKey == "outlet" {
		for i, row := range result {
			// result[i]["percentage"] = fmt.Sprintf("%.2f", cast.ToFloat64(row["total"])*100/cast.ToFloat64(totalAllMap[0]))
			result[i]["percentage"] = utils.RoundUp(cast.ToFloat64(row["total"]) * 100 / cast.ToFloat64(totalAllMap[0])) // 0 key hold all outlet values
			result[i]["percentage_transaction"] = utils.RoundUp(cast.ToFloat64(row["total_transaction"]) * 100 / cast.ToFloat64(totalTransactionAllMap[0]))
			// result[i]["percentage_transaction"] = fmt.Sprintf("%.2f", cast.ToFloat64(row["total_transaction"])*100/cast.ToFloat64(totalAllMap[0]))

			// outletList := row["outlet"].([]any)
			for _, outletMap := range row["outlet"].([]any) {
				outletMap := outletMap.(map[string]any)
				outeltId := cast.ToInt(outletMap["outlet_fkid"])
				total := cast.ToFloat64(outletMap["total"])
				totalTransaction := cast.ToFloat64(outletMap["total_transaction"])
				percentage := 0.0
				percentageTransaction := 0.0
				if totalTransaction > 0 {
					percentageTransaction = utils.RoundUp(totalTransaction * 100 / cast.ToFloat64(totalTransactionAllMap[outeltId]))
				}
				if total > 0 {
					percentage = utils.RoundUp(total * 100 / cast.ToFloat64(totalAllMap[outeltId]))
				}
				outletMap["percentage"] = percentage
				outletMap["percentage_transaction"] = percentageTransaction
			}
		}
	}

	fmt.Println(len(result), "--data")
	log.Info("total All: %v", utils.SimplyToJson(totalAllMap))

	return map[string]any{"data": result, "offsetId": offsetId}, nil
}

func (s *salesUseCase) FetchSalesByMediaGroupPayment(user domain.User, request domain.SalesReportMediaRequest) (map[string]any, error) {
	repo := s.pickRepo(request.EndDate)
	salesByMedia, err := repo.FetchSalesByMedia(user, request)
	if err != nil {
		return nil, err
	}

	banks, err := s.repoPrimary.FetchBank(user)
	if err != nil {
		return nil, err
	}

	bankMap := array.FlatMapArray(banks, "bank_id")

	result := make([]map[string]any, 0)
	total := 0
	dataGroup := make([]map[string]any, 0)

	for i, row := range salesByMedia {
		if bankdId := cast.ToString(row["bank_fkid"]); bankdId != "" {
			row["payment"] = bankMap[bankdId]["name"]
		}

		total += cast.ToInt(row["total"])
		dataGroup = append(dataGroup, array.TakeOnly(row, "total", "payment"))

		if i == len(salesByMedia)-1 || (row["created_at"] != salesByMedia[i+1]["created_at"]) {
			row["payment"] = dataGroup
			row["total"] = total
			result = append(result, array.TakeOnly(row, "payment", "created_at", "total"))

			//reset variable
			total = 0
			dataGroup = make([]map[string]any, 0)
		}
	}

	fmt.Println(cast.ToStringJson(result))

	return map[string]any{"data": result}, nil
}

// sales-by
func (s *salesUseCase) FetchSalesByOrderType(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesByOrderTypeResponse, error) {
	repo := s.pickRepo(request.EndDate)
	salesTagOutlet, err := repo.FetchSalesTagByOutlet(user, request)
	log.IfError(err)

	if salesTagOutlet == nil || len(*salesTagOutlet) == 0 {
		log.Info("no data sales tag...")
		return nil, nil
	}

	// Extract sales_tag_ids from salesTagOutlet
	var tagIDs []int
	for _, outlet := range *salesTagOutlet {
		tagIDs = append(tagIDs, outlet.SalesTagFkid)
	}

	//getting tag from repo, passing ids above
	salesTagMaster, err := s.repoPrimary.FetchSalesTagByIds(tagIDs)
	log.IfError(err)

	fmt.Println("size tag: ", len(*salesTagMaster), "tagIds: ", tagIDs)

	result := transformData(salesTagOutlet, salesTagMaster)

	return result, nil
}

func transformData(data *[]models.SalesTagByOutlet, salesTag *[]models.SalesTagEntity) *[]models.SalesByOrderTypeResponse {
	resultMap := make(map[int]*models.SalesByOrderTypeResponse)

	// log.Info("data: %v", utils.SimplyToJson(data))

	// Populate the resultMap with sales tags as keys
	for _, tag := range *salesTag {
		response := models.SalesByOrderTypeResponse{
			Name:             tag.Name,
			Total:            0,
			TotalTransaction: 0,
			Outlet:           nil,
		}
		resultMap[tag.SalesTagID] = &response
	}

	// Group the data based on sales tag name and calculate the total
	for _, record := range *data {
		if response, ok := resultMap[record.SalesTagFkid]; ok {
			response.Total += record.Total
			response.TotalTransaction += record.TotalTransaction
			outletData := models.SalesByOrderTypeDetail{
				Name:             record.OutletName,
				Total:            record.Total,
				TotalTransaction: record.TotalTransaction,
			}
			response.Outlet = append(response.Outlet, outletData)
		}
	}

	// Convert the map to a slice
	var result []models.SalesByOrderTypeResponse
	for _, response := range resultMap {
		result = append(result, *response)
	}

	return &result
}

func (s *salesUseCase) FetchSalesByOrderTypeGroup(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesByOrderTypeGroupResponse, error) {
	repo := s.pickRepo(request.EndDate)
	result := make([]models.SalesByOrderTypeGroupResponse, 0)

	orderTypeGroups, err := repo.FetchSalesByOrderTypeGroup(user, request)
	if log.IfError(err) {
		return nil, err
	}

	if orderTypeGroups == nil || len(*orderTypeGroups) == 0 {
		return &result, nil
	}

	// Extract sales_tag_ids from salesTagOutlet
	var tagIDs []int
	for _, outlet := range *orderTypeGroups {
		tagIDs = append(tagIDs, outlet.SalesTagFkid)
	}

	//getting tag from repo, passing ids above
	salesTagMaster, err := s.repoPrimary.FetchSalesTagByIds(tagIDs)
	log.IfError(err)

	salesTagMap := make(map[int]string)
	for _, tag := range *salesTagMaster {
		salesTagMap[tag.SalesTagID] = tag.Name
	}

	// Group the data by sales tag and date
	groupedData := make(map[string]map[int][]models.SalesTagByDate)
	for _, record := range *orderTypeGroups {
		key := record.Date
		if _, ok := groupedData[key]; !ok {
			groupedData[key] = make(map[int][]models.SalesTagByDate)
		}
		groupedData[key][record.OutletFkid] = append(groupedData[key][record.OutletFkid], record)
	}
	// log.Info("groupedData: %v", utils.SimplyToJson(groupedDsata))
	// Create the result using the grouped data
	for date, outlets := range groupedData {
		// Create the SalesByOrderTypeGroupResponse
		groupResponse := models.SalesByOrderTypeGroupResponse{
			Date: date,
		}

		groupResponse.Total = make([]models.SalesByOrderTypeDetail, 0)
		groupResponse.Outlet = make([]models.SalesByOrderTypeDetailOutlet, 0)
		totalByTag := make(map[int]int)
		totalTransactionByTag := make(map[int]int)

		for _, outletList := range outlets {
			salesTagOutlet := make([]models.SalesByOrderTypeDetail, 0)
			for _, outlet := range outletList {
				totalByTag[outlet.SalesTagFkid] = totalByTag[outlet.SalesTagFkid] + outlet.Total
				totalTransactionByTag[outlet.SalesTagFkid] = totalTransactionByTag[outlet.SalesTagFkid] + outlet.TotalTransaction
				salesTagOutlet = append(salesTagOutlet, models.SalesByOrderTypeDetail{
					Name:             salesTagMap[outlet.SalesTagFkid],
					Total:            outlet.Total,
					TotalTransaction: outlet.TotalTransaction,
				})
			}
			groupResponse.Outlet = append(groupResponse.Outlet, models.SalesByOrderTypeDetailOutlet{
				Name:  outletList[0].OutletName,
				Total: salesTagOutlet,
			})
		}

		for tagId, total := range totalByTag {
			groupResponse.Total = append(groupResponse.Total, models.SalesByOrderTypeDetail{
				Name:             salesTagMap[tagId],
				Total:            total,
				TotalTransaction: totalTransactionByTag[tagId],
			})
		}

		result = append(result, groupResponse)
	}

	return &result, nil
}
