package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h *hppUseCase) recordTransferConfirm(transferId int) {
	log.Info("new transfer confirm: %v", transferId)
	transferConfirms, err := h.repo.FetchTransferConfirm(transferId)
	log.IfError(err)
	log.Info("transferConfirms size: %v", len(*transferConfirms))

	productOriginDetail := make([]models.StockOutProduct, 0)
	transferByProduct := make(map[int]models.TransferConfirm)

	for _, t := range *transferConfirms {
		fmt.Printf("from id: %v, to: %v, qty: %v (c: %v), ", t.ProductDetailFkid, t.ProductDetailDesFkid, t.Qty, t.QtyConfirm)
		productOriginDetail = append(productOriginDetail, models.StockOutProduct{
			ProductDetailId: t.ProductDetailFkid,
			Qty:             float32(t.QtyConfirm),
		})
		transferByProduct[t.ProductDetailFkid] = t
	}
	fmt.Println(" | ")

	stockPrices, err := h.logStockOut(&productOriginDetail, domain.TransferConfirm.String(), cast.ToString(transferId))
	if log.IfError(err) {
		return
	}

	log.Info("stock prices: %v", utils.SimplyToJson(stockPrices))

	// stockInProducts := stockInBasedOnFifo(stockPrices, transferByProduct)
	stockInProducts := stockInTransferConfirm(transferConfirms)

	h.logStockIn(&stockInProducts, domain.TransferConfirm.String(), cast.ToString(transferId))

	productDetailIds := make([]int, len(stockInProducts))
	for i, product := range stockInProducts {
		productDetailIds[i] = product.ProductDetailId
	}
	h.updateProductPriceBuy(productDetailIds...)
}

// using price from input user
func stockInTransferConfirm(transferConfirms *[]models.TransferConfirm) []models.StockInProduct {
	log.Info("StockInProduct of transferConfirm  ")
	stockInProducts := make([]models.StockInProduct, 0)
	for _, t := range *transferConfirms {
		markup := float32(t.Markup)
		if t.MarkupType == "percent" {
			markup = float32(t.Price) * markup
		}
		price := t.Price + markup - float32(t.Discount)

		stockInProducts = append(stockInProducts, models.StockInProduct{
			ProductDetailId: t.ProductDetailDesFkid,
			Qty:             (t.QtyConfirm),
			Price:           price,
			MetaData:        t.MetaData(),
		})
	}
	return stockInProducts
}

// using price based on fifo price outlet origin (sender)
func stockInBasedOnFifo(stockPrices *[]models.StockOutProductPrice, transferByProduct map[int]models.TransferConfirm) []models.StockInProduct {
	stockInProducts := make([]models.StockInProduct, 0)

	//set stockInProduct based on stock out in origin outlet
	for _, price := range *stockPrices {
		transfer := transferByProduct[price.ProductDetailId]
		if transfer.Markup > 0 {
			if transfer.MarkupType == "percent" {
				price.Price += price.Price * (float32(transfer.Markup) / 100)
			} else if transfer.MarkupType == "nominal" {
				price.Price += cast.ToFloat32(transfer.Markup)
			}
		}

		price.Price -= float32(transfer.Discount)
		stockInProducts = append(stockInProducts, models.StockInProduct{
			ProductDetailId: transfer.ProductDetailDesFkid,
			Qty:             price.Qty,
			Price:           price.Price,
			MetaData:        transfer.MetaData(),
		})
	}
	return stockInProducts
}
