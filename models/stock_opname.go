package models

import (
	"encoding/json"
	"fmt"
)

type StockOpnameEntity struct {
	OpnameID          int     `json:"opname_id"`
	ProductDetailFKID int     `json:"product_detail_fkid"`
	Opname            float32 `json:"opname"`
	Open              float32 `json:"open"`
	Purchase          float32 `json:"purchase"`
	Sales             float32 `json:"sales"`
	Spoil             float32 `json:"spoil"`
	Transfer          float32 `json:"transfer"`
	Closing           float32 `json:"closing"`
	Production        float32 `json:"production,omitempty"`
	TimeCreated       int64   `json:"time_created"`
	Retur             int     `json:"retur,omitempty"`
	Refund            float32 `json:"refund,omitempty"`
}

func (s StockOpnameEntity) String() string {
	resp, _ := json.Marshal(s)
	return string(resp)
}

func (s StockOpnameEntity) GetBalance() float32 {
	result := s.Opname - (s.Open + s.Transfer + s.Production + (s.Purchase - float32(s.<PERSON>tur)) - (s.Sales + s.Refund) - s.Spoil) // s.Closing
	// return s.Closing
	fmt.Printf("opnameId %v, diff: %v : %v \n", s.OpnameID, result, s.String())
	return result
}

func (s StockOpnameEntity) GetMetaData() map[string]interface{} {
	return map[string]interface{}{
		"balance": s.GetBalance(),
		"closing": s.Closing,
		"opname":  s.Opname,
	}
}
