package v1

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func getNetSalesByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	//get sales information
	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(CASE WHEN (s.status='Success' and s.data_status='on' and s.payment NOT IN ('COMPLIMENT','DUTY MEALS') ) THEN s.grand_total ELSE 0 END) AS sales
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY outlet_id
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func CashFlowByShift_map(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//render outletlist
	outletIdMap := map[string]interface{}{}

	// net sales
	chanSales := make(chan map[string]interface{})
	go func() {
		query := getNetSalesByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}

			//render map
			dataRender[id] = data["sales"]
		}
		chanSales <- dataRender
	}()

	// purchase
	chanPurchaseLunas := make(chan map[string]interface{})
	go func() {
		query := getPurchaseLunas(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_grandtotal"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseLunas <- dataRender
	}()

	// purchase dengan hutang (pakai DP / tanpa DP)
	chanPurchaseHutang := make(chan map[string]interface{})
	go func() {
		query := getPurchaseHutang(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_uangmuka"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseHutang <- dataRender
	}()

	// pembayaran hutang
	chanDebtPayment := make(chan map[string]interface{})
	go func() {
		query := getDebtPayment(adminId, timeStart, timeEnd, outletIds)

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = cast.ToInt64(dataRender[id]) + cast.ToInt64(data["debt"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanDebtPayment <- dataRender
	}()

	// hutang
	chanDebt := make(chan map[string]interface{})
	go func() {
		query := getDebt(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			debt := cast.ToInt64(0)
			if cast.ToInt64(data["hutang"]) > cast.ToInt64(data["hutang_dibayar"]) {
				debt = cast.ToInt64(data["hutang"]) - cast.ToInt64(data["hutang_dibayar"])
			}

			if dataRender[id] != nil {
				q1 := cast.ToStringMap(dataRender[id])
				debt += cast.ToInt64(q1["debt"])
			}

			//render map
			dataRender[id] = debt

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}

		chanDebt <- dataRender
	}()

	//hasil merge id
	sales := <-chanSales
	purchase_lunas := <-chanPurchaseLunas
	purchase_hutang := <-chanPurchaseHutang
	debt_payment := <-chanDebtPayment
	debt := <-chanDebt

	//render data
	//render outletlist
	dataRender := []map[string]interface{}{}
	for index, data := range outletIdMap {
		dataSales := sales[index]
		if dataSales == nil {
			dataSales = 0
		}
		dataPurchaseLunas := purchase_lunas[index]
		if dataPurchaseLunas == nil {
			dataPurchaseLunas = 0
		}
		dataPurchaseHutang := purchase_hutang[index]
		if dataPurchaseHutang == nil {
			dataPurchaseHutang = 0
		}
		dataDebtPayment := debt_payment[index]
		if dataDebtPayment == nil {
			dataDebtPayment = 0
		}
		dataDebt := debt[index]
		if dataDebt == nil {
			dataDebt = 0
		}

		// countable
		purchase := cast.ToInt64(dataPurchaseLunas) + cast.ToInt64(dataPurchaseHutang) + cast.ToInt64(dataDebtPayment)
		cashflow := cast.ToInt64(dataSales) - purchase

		//render
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail": map[string]interface{}{
				"sales":    dataSales,
				"purchase": purchase,
				"cashflow": cashflow,
				"debt":     dataDebt,
			},
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender
}

func CashFlowByShift_v1(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
		return
	}

	//proses
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := string(ctx.Request.Header.Peek("outlet_access"))
	//get post value
	timeStart := cast.ToInt64(myPost.TimeStart)
	timeEnd := cast.ToInt64(myPost.TimeEnd)
	timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])
	if outletIds == nil {
		oa := strings.Split(outletAccess, ",")
		names := make([]interface{}, len(oa))
		for i, v := range oa {
			names[i] = v
		}
		outletIds = names
	}

	//check min.date data yang bisa diambil
	role := string(ctx.Request.Header.Peek("role"))
	timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

	//proses pengambilan data
	data := CashFlowByShift_map(adminId, timeStart, timeEnd, outletIds)

	//output
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   data,
	})
}
