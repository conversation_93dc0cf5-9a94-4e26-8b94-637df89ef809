package models

type TaskTimeAnalysisResponse struct {
	BoardsID      int                    `json:"boards_id,omitempty"`
	Name          string                 `json:"name,omitempty"`
	Description   string                 `json:"description,omitempty"`
	TotalDuration float64                `json:"total_duration,omitempty"`
	Category      string                 `json:"category,omitempty"`
	Status        string                 `json:"status,omitempty"`
	Workflows     []TimeAnalysisWorkflow `json:"workflows,omitempty"`
}

type TimeAnalysisRepetition struct {
	TimeStart int64 `json:"time_start,omitempty"`
	TimeEnd   int64 `json:"time_end,omitempty"`
}

type TimeAnalysisWorkflow struct {
	WorkflowID      int                      `json:"workflow_id,omitempty"`
	Name            string                   `json:"name,omitempty"`
	Duration        float64                  `json:"duration,omitempty"`
	TimeStart       int64                    `json:"time_start,omitempty"`
	TimeEnd         int64                    `json:"time_end,omitempty"`
	TotalRepetition int                      `json:"total_repetition,omitempty"`
	Repetitions     []TimeAnalysisRepetition `json:"repetitions,omitempty"`
}

type TimeAnalysisBoardResponse struct {
	ID           int    `json:"id,omitempty"`
	Name         string `json:"name,omitempty"`
	Description  string `json:"description,omitempty"`
	Status       string `json:"status,omitempty"`
	CategoryName string `json:"category_name,omitempty"`
	CategoryId   int    `json:"category_id,omitempty"`
}
