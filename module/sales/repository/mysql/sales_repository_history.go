package mysql

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

// FetchSalesHistory implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSalesHistory(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `SELECT s.sales_id, s.employee_fkid, s.sales_tag_fkid, s.discount,
	s.display_nota AS nomor_nota, 
	s.display_nota, s.status, s.grand_total,
	s.time_created AS tanggal, 
	s.qty_customers AS pax, s.customer_name, s.member_fkid,
	s.voucher AS voucherSales, 
	s.voucher_info AS keterangan_voucherS, 
	s.discount_info AS keterangan_discountS,
	s.time_created, s.open_shift_fkid, o.name as outlet_name
	 from sales s 
	 join outlets o on o.outlet_id=s.outlet_fkid
	 where s.data_status='on'
	 and o.admin_fkid= @adminId 
	 `

	maxLimit := 150
	limitQuery := fmt.Sprintf(" LIMIT %v OFFSET %v ", maxLimit, maxLimit*request.Page)

	if len(request.Outlet) > 0 {
		sql += " and s.outlet_fkid in @outletId "
	}
	if request.OffsetId > 0 {
		sql += " and s.time_created > @offsetId "
		// request.StartDate = request.OffsetId
		limitQuery = fmt.Sprintf(" LIMIT %v ", maxLimit)
	}
	if (request.StartDate > 0 || request.EndDate > 0) && request.EndDate >= request.StartDate {
		sql += " and s.time_created BETWEEN @startDate AND @endDate "
	}
	if strings.ToLower(request.DataStatus) == "all" {
		request.DataStatus = ""
	}
	if request.DataStatus != "" {
		sql += " AND s.status = @status "
	}
	//by customer
	if request.Contact != "" {
		sql += " AND s.receipt_receiver = @contact "
	} else if request.Customer != "" {
		sql += " AND s.customer_name = @customerName "
	}

	//to avoid caching returning old data, if time end is greater than now, set to now
	if request.EndDate > time.Now().UnixMilli() {
		request.EndDate = time.Now().UnixMilli()
	}

	sql += " ORDER BY s.time_created  " + limitQuery
	sql, params := db.MapParam(sql, map[string]interface{}{
		"startDate":    request.StartDate,
		"endDate":      request.EndDate,
		"adminId":      cast.ToInt64(user.BusinessId),
		"outletId":     request.Outlet,
		"offsetId":     request.OffsetId,
		"promotionId":  request.PromotionId,
		"contact":      request.Contact,
		"customerName": request.Customer,
		"status":       request.DataStatus,
	})

	// log.Info("request param: %v | sql: %v", utils.SimplyToJson(request), sql)

	// return db.QueryArray(sql, params...)
	return s.QueryCache("sales-history", sql, params...).MapArray()
}

// FetchSumSalesDetailPromotion implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesDetailPromotion(groupBy domain.SalesGroupKey, ids ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
	any_value(sales_fkid) as sales_fkid,
	SUM(if(sales_detail_fkid is not null, promotion_value, 0)) as total,
	GROUP_CONCAT(DISTINCT promotion_fkid) promotion_fkids,
	any_value(sales_detail_fkid) as sales_detail_fkid
FROM
	sales_detail_promotion
WHERE
	$GROUP_KEY in @ids
	group by $GROUP_KEY`

	sql = strings.ReplaceAll(sql, "$GROUP_KEY", groupBy.ToString())
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	// return db.QueryArray(sql, params...)
	return s.QueryCache("sum-sales-detail-promotion", sql, params...).MapArray()
}

// FetchSumSalesDetailDiscount implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesDetailDiscount(groupBy domain.SalesGroupKey, discType []string, salesIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
	sum(total) as total, ANY_VALUE(sd.sales_fkid) as sales_fkid,
	ANY_VALUE(sd.sales_detail_id) as sales_detail_id
FROM
	sales_detail_discount sdd 
	join sales_detail sd on sd.sales_detail_id=sdd.sales_detail_fkid
WHERE
	sdd.type IN @type
	and sd.$GROUP_KEY in @salesIds
	group by sd.$GROUP_KEY `

	sql = strings.ReplaceAll(sql, "$GROUP_KEY", groupBy.ToString())
	sql, params := db.MapParam(sql, map[string]interface{}{
		"salesIds": salesIds,
		"type":     discType,
	})

	// return db.QueryArray(sql, params...)
	return s.QueryCache("sum-sales-detail-disc", sql, params...).MapArray()
}

// FetchSumSalesPromotion implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesPromotion(salesIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
	GROUP_CONCAT(DISTINCT voucher_code) as voucher_codes, 
	sum(promotion_value) as total,
	GROUP_CONCAT(promotion_fkid) as promotion_fkids,
	sales_fkid
FROM
	sales_promotion
WHERE
	sales_fkid in @salesIds 
	GROUP BY sales_fkid`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"salesIds": salesIds,
	})
	// return db.QueryArray(sql, params...)
	return s.QueryCache("sum-sales-promotion", sql, params...).MapArray()
}

// FetchSumSalesTaxPerCategory implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesTaxPerCategory(salesIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT sales_fkid,
	SUM(if(category = 'discount', sales_tax.total, 0)) as discount,
	SUM(if(category = 'tax', sales_tax.total, 0)) as tax,
	SUM(if(category = 'service', sales_tax.total, 0)) as service,
	SUM(if(category = 'voucher', sales_tax.total, 0)) as voucher
FROM
	sales_tax
WHERE
	sales_fkid in @salesIds 
	GROUP BY sales_fkid`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"salesIds": salesIds,
	})
	// return db.QueryArray(sql, params...)
	return s.QueryCache("sum-sales-tax-category", sql, params...).MapArray()
}

// FetchShiftByOpenShift implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchShiftByOpenShift(openShiftIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT s.shift_id, s.name, os.open_shift_id
	from open_shift os 
	join shift s on os.shift_fkid=s.shift_id
	where open_shift_id in @ids `

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": openShiftIds,
	})
	return db.QueryArray(sql, params...)
}

// FetchSumSalesVoid implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesVoid(groupBy domain.SalesGroupKey, ids ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
	sum(sub_total) as sub_total, sum(qty) as qty, 
	ANY_VALUE(sales_detail_fkid) as sales_detail_fkid, ANY_VALUE(sales_fkid) as sales_fkid
FROM
	sales_void sv
WHERE
	$GROUP_KEY in @ids 
	group by $GROUP_KEY `

	sql = strings.ReplaceAll(sql, "$GROUP_KEY", groupBy.ToString())
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})
	return db.QueryArray(sql, params...)
}

// FetchSumSalesDetail implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSumSalesDetail(salesIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT sales_fkid, sum(sd.sub_total) as sub_total, sum(sd.discount) as discount,
	GROUP_CONCAT(DISTINCT discount_info) as discount_info
	from sales_detail sd 
	where sales_fkid in @salesIds 
	group by sales_fkid `

	sql, params := db.MapParam(sql, map[string]interface{}{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}

// order sales (app transaction)
func (s salesRepository) FetchOrderTypeOfOrderSales(salesIds ...interface{}) (*[]domain.OrderTypeOfOrderSales, error) {
	var result []domain.OrderTypeOfOrderSales
	if len(salesIds) == 0 {
		return &result, nil
	}
	sql := `select order_sales_id, order_type from order_sales where order_sales_id in @ids`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": salesIds,
	})

	err := s.Query(sql, params...).Model(&result)
	return &result, err
}

// FetchSalesRefunds implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSalesRefunds(salesIds ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT sales_fkid, reason, time_created, employee_fkid 
            FROM sales_refund 
            WHERE sales_fkid IN @salesIds`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"salesIds": salesIds,
	})
	return db.QueryArray(sql, params...)
}
