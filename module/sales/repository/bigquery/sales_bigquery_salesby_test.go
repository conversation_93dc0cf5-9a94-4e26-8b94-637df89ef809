package bigquery

import (
	"fmt"
	"testing"

	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func Test_salesTagMapToStruct(t *testing.T) {
	data := []map[string]interface{}{
		{
			"outlet_fkid":    10,
			"total":          1000,
			"sales_tag_fkid": 9,
			"outlet_name":    "Babarsari",
		},
	}
	var result []models.SalesTagByOutlet
	type args struct {
		mapArray []map[string]interface{}
		result   *[]models.SalesTagByOutlet
	}
	tests := []struct {
		name string
		args args
	}{
		{"test1", args{data, &result}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			salesTagMapToStruct(tt.args.mapArray, tt.args.result)
			fmt.Println(">>>", utils.SimplyTo<PERSON>son(tt.args.result))
		})
	}
}
