package bq

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"google.golang.org/api/iterator"
)

var totalRead float64

type Repository struct {
	Client *bigquery.Client
}

type SqlResult struct {
	Data  interface{}
	Error error
}

func (r Repository) DbName() string {
	return fmt.Sprintf("%s.", os.Getenv("BQ_DB_NAME"))
}

func (r Repository) Query(sql string, params ...bigquery.QueryParameter) (result *SqlResult) {
	ctx := context.Background()
	result = &SqlResult{}

	if r.Client == nil {
		result.Error = fmt.Errorf("big query client is not initialized..")
		return
	}

	if r.DbName() == "" {
		result.Error = fmt.Errorf("BQ_DB_NAME not set")
		return
	}

	sql = strings.Replace(sql, "\n", " ", -1)
	q := r.Client.Query(sql)
	q.Location = os.Getenv("BQ_DB_LOCATION")

	if len(params) > 0 {
		q.Parameters = params
	}

	job, err := q.Run(ctx)
	if err != nil {
		fmt.Println("[bq] run job err: ", err)
		result.Error = err
		return
	}
	status, err := job.Wait(ctx)
	if log.IfError(err) {
		fmt.Println("[bq] wait err: ", err, "\n#query: ", parseQuery(sql, params...))
		result.Error = err
		return
	}
	fmt.Println("[bq] #query: ", parseQuery(sql, params...))
	if err := status.Err(); err != nil {
		fmt.Println("[bq] status err: ", err)
		result.Error = err
		return
	}

	timeStart := time.Now()
	it, err := job.Read(ctx)
	if log.IfError(err) {
		fmt.Println("[bq] job rad err: ", err, "\n#query: ", parseQuery(sql, params...))
		result.Error = err
		return
	}
	data := make([]map[string]interface{}, 0)
	for {
		var row map[string]bigquery.Value
		err := it.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			fmt.Println("[bq] fetching err: ", err)
			result.Error = err
			return
		}

		mapInterface := make(map[string]interface{})
		for k, v := range row {
			mapInterface[k] = v
		}
		data = append(data, mapInterface)
	}

	if os.Getenv("ENV") == "localhost" || time.Since(timeStart) > 60*time.Second {
		fmt.Println(parseQuery(sql, params...))
	} else {
		fmt.Println(strings.Replace(parseQuery(sql, params...), "\n", " ", -1))
	}

	totalRead += float64(status.Statistics.TotalBytesProcessed) / 1024 / 1024 //to get in mb size
	fmt.Printf("[bq] %d rows took: %v, size: %v, total: %v\n", it.TotalRows, time.Since(timeStart), status.Statistics.TotalBytesProcessed, totalRead)
	result.Data = data
	return
}

func parseQuery(sql string, params ...bigquery.QueryParameter) string {
	sqlParser := sql
	for _, param := range params {
		sqlParser = strings.Replace(sqlParser, fmt.Sprintf("@%s", param.Name), cast.ToString(param.Value), -1)
	}
	return sqlParser
}

func MapParam(params map[string]interface{}) []bigquery.QueryParameter {
	var result = make([]bigquery.QueryParameter, 0)
	for k, v := range params {
		result = append(result, bigquery.QueryParameter{
			Name:  k,
			Value: v,
		})
	}

	return result
}
