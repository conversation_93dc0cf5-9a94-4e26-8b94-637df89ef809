package mysql

import (
	"database/sql"
	"os"

	"gitlab.com/uniqdev/backend/api-report/domain"
)

// Repository struct
type Repository struct {
	Conn    *sql.DB
	sql     string
	args    []interface{}
	CacheDb domain.CacheInterface
}

func NewInstance(db *sql.DB) Repository {
	return Repository{
		Conn: db,
	}
}

type SqlResult struct {
	Data      interface{}
	Error     error
	SqlQuery  string
	sqlOrigin string
	args      []interface{}
	repo      *Repository
}

func (r Repository) DbName() string {
	// fmt.Println("-------------->>>>> db name: ", os.Getenv("db_name"))
	return os.Getenv("db_name")
}
