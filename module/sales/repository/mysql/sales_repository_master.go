package mysql

import "gitlab.com/uniqdev/backend/api-report/models"

// FetchReportNotification implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchReportNotification(adminId int, reportType string) ([]models.SettingReportEntity, error) {
	query := `SELECT sn.* FROM setting_notif sn 
	WHERE notif_type = ? AND admin_fkid = ? `
	var result []models.SettingReportEntity
	err := s.Query(query, reportType, adminId).Model(&result)
	return result, err
}
