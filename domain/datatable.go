package domain

import (
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

type RequestTransfer struct {
	OutletOrigin int
}

type DataTableRequest struct {
	StartDate int64
	EndDate   int64
	timeZone  int
	Outlet    []int
	Limit     int
	Offset    int
	Shift     []int
	TimeZone  int
	RequestTransfer
}

func GetDataTableRequest(ctx *fasthttp.RequestCtx) DataTableRequest {
	post := ctx.Request.PostArgs()
	outletIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("outlet")), ","))
	shiftIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("shift")), ","))
	request := DataTableRequest{
		StartDate: cast.ToInt64(post.Peek("startDate")),
		EndDate:   cast.ToInt64(post.Peek("endDate")),
		Limit:     cast.ToInt(post.Peek("limit")),
		Offset:    cast.ToInt(post.Peek("offset")),
		Outlet:    outletIds,
		Shift:     shiftIds,
		TimeZone:  cast.ToInt(post.Peek("timeZone")),
	}
	if request.EndDate > time.Now().UnixMilli() {
		request.EndDate = time.Now().UnixMilli()
	}
	return request
}
