package google

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"cloud.google.com/go/pubsub"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func Subscribe(subsId string, action func(data []byte) bool) error {
	client := pubsubClient
	if client == nil {
		fmt.Println("pubsub client has not been initialized")
		if env := os.Getenv("ENV"); env == "production" || env == "staging" {
			log.IfError(fmt.Errorf("pubsub client has not been initialized"))
		}
		return fmt.Errorf("pubsub client has not been initialized")
	}

	fmt.Printf("pubsub subscribed to id: '%s'\n", subsId)

	var mu sync.Mutex
	ctx := context.Background()
	sub := client.Subscription(strings.TrimSpace(subsId))
	cctx, _ := context.WithCancel(ctx)
	err := sub.Receive(cctx, func(ctx context.Context, msg *pubsub.Message) {
		mu.Lock()
		defer mu.Unlock()
		fmt.Printf("pubsub receive message, subs id: %s | id: %s | %s \n", subsId, msg.ID, string(msg.Data))
		startAt := time.Now()
		if action(msg.Data) {
			fmt.Printf("pubsub ack? : %v | id: %s | duration: %v, subsId: %v\n", true, msg.ID, time.Since(startAt), subsId)
			msg.Ack()
		}
	})

	fmt.Printf("subsribe to '%s' error: %v \n", subsId, err)
	return err
}

func PublishMessage(topicId string, data interface{}) error {
	client := pubsubClient
	if client == nil {
		return errors.New("pubsub client not initialized")
	}

	ctx := context.Background()
	t := client.Topic(topicId)
	pubsubMsg := ""
	if dataMap, ok := data.(map[string]interface{}); ok {
		j, _ := json.Marshal(dataMap)
		pubsubMsg = string(j)
	} else {
		pubsubMsg = cast.ToString(data)
	}

	result := t.Publish(ctx, &pubsub.Message{
		Data: []byte(pubsubMsg),
	})

	id, err := result.Get(ctx)
	if err != nil {
		fmt.Printf("pubsub err: %v\n", err)
		return err
	}

	fmt.Printf("pubsub published to topic: %s with id: %s\n", topicId, id)
	return nil
}
