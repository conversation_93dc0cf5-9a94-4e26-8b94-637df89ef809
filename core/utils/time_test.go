package utils

import (
	"testing"
	"time"
)

func TestCurrentMillis(t *testing.T) {
	tests := []struct {
		name string
		want int64
	}{
		{"now", 0},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CurrentMillis(); got != tt.want {
				t.<PERSON><PERSON>("CurrentMillis() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGetCurrentDayTimeMillis(t *testing.T) {
	// Test cases
	tests := []struct {
		name          string
		timeMillis    int64
		timeOffset    int64
		want          int64
		expectSameDay bool
	}{
		{
			"Time in the past, same day",
			time.Now().Add(-4*time.Hour).UnixNano() / int64(time.Millisecond), // 4 hours ago
			0, // No offset
			0, // Will be updated in the test
			true,
		},
		{
			"Time in the future, same day",
			time.Now().Add(4*time.Hour).UnixNano() / int64(time.Millisecond), // 4 hours later
			0, // No offset
			0, // Will be updated in the test
			true,
		},
		{
			"Time in the past, different day",
			time.Now().Add(-28*time.Hour).UnixNano() / int64(time.Millisecond), // Yesterday
			0, // No offset
			time.Now().Add(-28*time.Hour).UnixNano() / int64(time.Millisecond), // Should return original time
			false,
		},
		{
			"Time with offset, same day",
			time.Now().Add(-2*time.Hour).UnixNano() / int64(time.Millisecond), // 2 hours later
			7 * int64(time.Hour) / int64(time.Millisecond),                    // -7 hours offset
			0, // Will be updated in the test
			true,
		},
		{
			"Time with offset, different day",
			time.Now().Add(-2*24*time.Hour).UnixNano() / int64(time.Millisecond), // 2 hours ago
			7 * int64(time.Hour) / int64(time.Millisecond),                       // -7 hours offset
			time.Now().Add(-2*24*time.Hour).UnixNano() / int64(time.Millisecond), // Should return original time
			false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.expectSameDay {
				// If we expect the time to be on the same day, we need to update
				// the `want` value to be the current time since it's dynamically changing.
				tt.want = time.Now().UTC().Add(time.Duration(tt.timeOffset)*time.Millisecond).UnixNano() / int64(time.Millisecond)
			} else {
				tt.want = tt.timeMillis
			}
			got := AdjustTimeMillis(tt.timeMillis, tt.timeOffset)
			if got != tt.want {
				t.Errorf("getCurrentDayTimeMillis() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMillisToDateTime(t *testing.T) {
	// Test case: Convert current time in milliseconds to date-time string
	now := time.Now()
	millis := now.UnixMilli()
	// timeOffset := int64(25200 * 1000)

	expected := now.Format("2006-01-02 15:04")
	result := MillisToDate(millis, 25200)
	if result != expected {
		t.Errorf("Expected %s, got %s", expected, result)
	}

	// Test case: Convert a specific timestamp
	specificMillis := int64(1730174785323) // Example timestamp (Jan 1, 2023)
	expectedSpecific := "2024-10-29 11:06" // Adjust according to your timezone
	resultSpecific := MillisToDate(specificMillis, 25200)
	if resultSpecific != expectedSpecific {
		t.Errorf("Expected %s, got %s", expectedSpecific, resultSpecific)
	}
}
