package controller

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strconv"
	"strings"
	"sync"
)

var weekFormat = "w%d"

const (
	Category_CashIn  = "category#cash_in"
	Category_CashOut = "category#cash_out"
	Media_CashIn     = "media#cash_in"
	Media_CashOut    = "media#cash_out"
)

func GetReportCashFlowByMedia(ctx *fasthttp.RequestCtx) {
	var wg sync.WaitGroup
	outletId := ctx.UserValue("outletId")
	shiftId := ctx.UserValue("shiftId")
	date := ctx.UserValue("date")
	dateAdd := ctx.UserValue("dateAdd")
	category := utils.ToString(ctx.UserValue("category"))
	//version := ctx.UserValue("version")
	//shiftId := ctx.UserValue("shiftId")

	cashInChan := make(chan []map[string]interface{}, 1)
	cashOutChan := make(chan []map[string]interface{}, 1)

	cashIn := make([]map[string]interface{}, 0)
	cashOut := make([]map[string]interface{}, 0)
	summary := make(map[string]map[string]float64, 0)

	summary["total_cash_in"] = make(map[string]float64)
	summary["total_cash_out"] = make(map[string]float64)
	summary["total_cash_flow"] = make(map[string]float64)
	summary["accumulated_cash_flow"] = make(map[string]float64)

	wg.Add(1)
	go getCashInByMedia(&wg, category, outletId, shiftId, date, dateAdd, cashInChan)
	wg.Add(1)
	go getCashOutByMedia(&wg, category, outletId, shiftId, date, dateAdd, cashOutChan)
	wg.Wait()

	formatDataCashFlow("cash_in", category, <-cashInChan, &cashIn, summary)
	formatDataCashFlow("cash_out", category, <-cashOutChan, &cashOut, summary)

	close(cashInChan)
	close(cashOutChan)

	formatSummaryCashFlow(summary, category)

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(map[string]interface{}{
		"cash_in":  cashIn,
		"cash_out": cashOut,
		"summary":  summary,
	})
}

func GetReportCashFlowByCategory(ctx *fasthttp.RequestCtx) {
	var wg sync.WaitGroup
	outletId := ctx.UserValue("outletId")
	shiftId := ctx.UserValue("shiftId")
	date := ctx.UserValue("date")
	dateAdd := ctx.UserValue("dateAdd")
	category := utils.ToString(ctx.UserValue("category"))

	cashInChan := make(chan []map[string]interface{}, 1)
	cashOutChan := make(chan []map[string]interface{}, 1)

	cashIn := make([]map[string]interface{}, 0)
	cashOut := make([]map[string]interface{}, 0)
	summary := make(map[string]map[string]float64, 0)

	summary["total_cash_in"] = make(map[string]float64)
	summary["total_cash_out"] = make(map[string]float64)
	summary["total_cash_flow"] = make(map[string]float64)
	summary["accumulated_cash_flow"] = make(map[string]float64)

	wg.Add(1)
	go getCashInByCategory(&wg, cashInChan, category, outletId, shiftId, date, dateAdd)
	wg.Add(1)
	go getCashOutByCategory(&wg, cashOutChan, category, outletId, shiftId, date, dateAdd)
	wg.Wait()

	formatDataCashFlow("cash_in", category, <-cashInChan, &cashIn, summary)
	formatDataCashFlow("cash_out", category, <-cashOutChan, &cashOut, summary)

	close(cashInChan)
	close(cashOutChan)

	//--> GET SUMMARY
	formatSummaryCashFlow(summary, category)

	finalData := map[string]interface{}{
		"cash_in":  cashIn,
		"cash_out": cashOut,
		"summary":  summary,
	}

	ctx.SetContentType("application/json")
	err := json.NewEncoder(ctx).Encode(finalData)
	utils.CheckErr(err)
}

func GetReportCashFlowTotal(ctx *fasthttp.RequestCtx) {
	var wg sync.WaitGroup
	outletId := ctx.UserValue("outletId")
	shiftId := ctx.UserValue("shiftId")
	dateStart := ctx.UserValue("dateStart")
	dateEnd := ctx.UserValue("dateEnd")
	dateAdd := ctx.UserValue("dateAdd")
	dataType := ctx.UserValue("dataType").(string)

	cashInChan := make(chan []map[string]interface{}, 1)
	cashOutChan := make(chan []map[string]interface{}, 1)

	sqlOut := getQuery(fmt.Sprintf("%s#cash_out", dataType))
	sqlIn := getQuery(fmt.Sprintf("%s#cash_in", dataType))

	if utils.ToString(shiftId) != "0" {
		sqlOut = strings.Replace(sqlOut, "[add_condition]", " and shift_fkid = $shift_id ", -1)
		sqlOut = strings.Replace(sqlOut, "[add_condition_2]", " and dp.shift_fkid = $shift_id ", -1)
		sqlIn = strings.Replace(sqlIn, "[add_condition]", " and shift_fkid = $shift_id ", -1)
	} else {
		sqlOut = strings.Replace(sqlOut, "[add_condition]", "", -1)
		sqlOut = strings.Replace(sqlOut, "[add_condition_2]", "", -1)
		sqlIn = strings.Replace(sqlIn, "[add_condition]", "", -1)
	}

	sqlOut, argsOut := db.FormatArgs(sqlOut, map[string]interface{}{
		"date_start": dateStart,
		"date_end":   dateEnd,
		"date_add":   dateAdd,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})

	sqlIn, argsIn := db.FormatArgs(sqlIn, map[string]interface{}{
		"date_start": dateStart,
		"date_end":   dateEnd,
		"date_add":   dateAdd,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})

	wg.Add(1)
	go db.QueryArrayGo(&wg, cashOutChan, sqlOut, argsOut...)

	wg.Add(1)
	go db.QueryArrayGo(&wg, cashInChan, sqlIn, argsIn...)

	wg.Wait()

	cashIn := make([]map[string]interface{}, 0)
	cashOut := make([]map[string]interface{}, 0)
	summary := make(map[string]map[string]int, 0)

	summary["total_cash_in"] = make(map[string]int)
	summary["total_cash_out"] = make(map[string]int)
	summary["total_cash_flow"] = make(map[string]int)
	summary["accumulated_cash_flow"] = make(map[string]int)

	formatDataCashFlowTotal("cash_in", <-cashInChan, &cashIn, summary)
	formatDataCashFlowTotal("cash_out", <-cashOutChan, &cashOut, summary)

	//calculate summary
	result := summary["total_cash_in"]["total"] - summary["total_cash_out"]["total"]
	summary["total_cash_flow"]["total"] = result
	summary["accumulated_cash_flow"]["total"] = result

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"cash_in":  cashIn,
		"cash_out": cashOut,
		"summary":  summary,
	})

}

func getQuery(cashFlowType string) string {
	var result string
	switch cashFlowType {
	case Category_CashIn:
		result = `SELECT round(sum(sd.sub_total / sdt.totalSubTotal * sp.allTotal)) as grand_total,
       prc.name                                                   as uid_name,
       prc.purchase_report_category_id                            as uid,
       'sales'                                                    as tbl
from sales s
       left JOIN sales_detail sd on sd.sales_fkid = s.sales_id
       left JOIN products p on p.product_id = sd.product_fkid
       left JOIN purchase_report_category prc on prc.purchase_report_category_id = p.purchase_report_category_fkid
       left join open_shift o on s.open_shift_fkid = o.open_shift_id
       left join (select sum(total) as allTotal, min(sales_fkid) as sales_fkid
                  from sales_payment
                  group by sales_fkid) sp on sp.sales_fkid = s.sales_id
       left join (select sum(sdd.sub_total) as totalSubTotal,
                         sdd.sales_fkid
                  from sales_detail sdd
                  group by sdd.sales_fkid) sdt on sdt.sales_fkid = s.sales_id
WHERE s.outlet_fkid =  $outlet_id  
  and s.status = 'success'
  and s.payment != 'compliment'
  and from_unixtime(o.time_open / 1000 +  $date_add , '%Y-%m-%d' ) between $date_start and $date_end [add_condition] 
group by  prc.purchase_report_category_id
union
select sum((select tc.total/tc.sub_total*tc.grand_total)) as grand_total, 
       category.name as uid_name,
       category.product_category_id as uid,
	   'transfer'
from transfer_confirm tc
       join transfer_products tp on tc.transfer_product_fkid = tp.transfer_product_id
       join transfer t on tp.transfer_fkid = t.transfer_id
       join products_detail detail on tp.product_detail_des_fkid = detail.product_detail_id
       join products p2 on detail.product_fkid = p2.product_id
       join products_category category on p2.product_category_fkid = category.product_category_id
where t.outlet_origin_fkid =  $outlet_id  
  and from_unixtime(tc.data_created/1000 +  $date_add , '%Y-%m-%d' ) between $date_start and $date_end [add_condition] 
group by category.product_category_id
order by uid`

	case Category_CashOut:
		result = `
select prc.purchase_report_category_id as uid, prc.name as uid_name, sum(pp.tot_dis) as grand_total, 'purchase' as tbl
from purchase_products pp
         join purchase p2 on pp.purchase_fkid = p2.purchase_id
         join products_detail pd on pp.products_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         join purchase_report_category prc on p.purchase_report_category_fkid = prc.purchase_report_category_id
where p2.outlet_fkid=  $outlet_id  and from_unixtime(p2.data_created/1000 +  $date_add , '%Y-%m-%d') between  $date_start and $date_end  [add_condition] 
group by prc.purchase_report_category_id

union

select oc.prc_category_fkid, c.name, sum(total), 'operational_cost'
from operationalcost oc
         join purchase_report_category c on oc.prc_category_fkid = c.purchase_report_category_id
where oc.outlet_fkid=  $outlet_id  and from_unixtime(oc.time_created/1000 +  $date_add , '%Y-%m-%d') between  $date_start and $date_end [add_condition] 
group by oc.prc_category_fkid

union

select prc2.purchase_report_category_id, prc2.name, sum(total), 'transfer'
from transfer_products tp
         join transfer t on tp.transfer_fkid = t.transfer_id
         join products_detail d on tp.product_detail_des_fkid = d.product_detail_id
         join products p3 on d.product_fkid = p3.product_id
         join purchase_report_category prc2 on p3.purchase_report_category_fkid = prc2.purchase_report_category_id
where t.outlet_destination_fkid =  $outlet_id  and from_unixtime(t.date_created/1000 +  $date_add , '%Y-%m-%d') between  $date_start and $date_end [add_condition] 
group by  prc2.purchase_report_category_id

union

select  prc3.purchase_report_category_id, prc3.name, sum(pp2.tot_dis/(select sum(tot_dis) from purchase_products where purchase_fkid = p4.purchase_id) * dp.nominal), 'debt'
from debt_payment dp
         join purchase p4 on dp.purchase_fkid = p4.purchase_id
         join purchase_products pp2 on p4.purchase_id = pp2.purchase_fkid
         join products_detail pd2 on pp2.products_fkid = pd2.product_detail_id
         join products p5 on pd2.product_fkid = p5.product_id
         join purchase_report_category prc3 on p5.purchase_report_category_fkid = prc3.purchase_report_category_id
where p4.outlet_fkid=  $outlet_id  and from_unixtime(dp.time_created/1000 +  $date_add , '%Y-%m-%d') between $date_start and $date_end [add_condition_2] 
group by prc3.purchase_report_category_id

order by uid`

	case Media_CashIn:
		result = "select min(method) as uid_name_2, min(name) as uid_name,  min(bank_fkid) as uid, sum(total) as grand_total, 'sales' as tbl " +
			"from sales_payment sp " +
			"join sales s on sp.sales_fkid = s.sales_id " +
			"join open_shift os on os.open_shift_id = s.open_shift_fkid " +
			"left join sales_payment_bank spb on sp.payment_id = spb.sales_payment_fkid " +
			"left join payment_media_bank pmb on spb.bank_fkid = pmb.bank_id " +
			"where sp.method != 'COMPLIMENT'  and s.status='success' and s.outlet_fkid = $outlet_id and " +
			"from_unixtime(os.time_open/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition]  " +
			"group by spb.bank_fkid " +
			"union " +
			"select min(payment), min(pmb.name),  min(bank_origin), sum(sub_total), 'transfer' as tbl " +
			"from transfer " +
			"left JOIN payment_media_bank pmb on transfer.bank_origin = pmb.bank_id " +
			"where outlet_origin_fkid = $outlet_id and from_unixtime(date_created/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition]  " +
			"group by bank_origin " +
			"order by uid"

	case Media_CashOut:
		result = "select min(pay_type) as uid_name_2, min(pmb.name) as uid_name, min(pmb.bank_id) as uid, sum(grand_total) as grand_total, 'purchase' as tbl from purchase p " +
			"left join payment_media_bank pmb on pmb.bank_id=p.payment_media_bank_fkid " +
			"where outlet_fkid = $outlet_id and from_unixtime(p.data_created/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition]   " +
			"group by payment_media_bank_fkid " +
			"union " +
			"select min(payment) as method, min(pmb.name) as bank, min(payment_bank_fkid) as bankId,sum(total) as grandTotal, 'operational_cost' as tbl " +
			"from operationalcost oc " +
			"left join payment_media_bank pmb on oc.payment_bank_fkid = pmb.bank_id " +
			"where outlet_fkid = $outlet_id and from_unixtime(time_created/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition]   " +
			"group by payment_bank_fkid " +
			"union " +
			"select min(payment) as method, min(pmb.name) as bank, min(bank_destination) as bankId, sum(sub_total) as grandTotal, 'transfer' as tbl " +
			"from transfer " +
			"left JOIN payment_media_bank pmb on transfer.bank_destination = pmb.bank_id " +
			"where outlet_destination_fkid = $outlet_id and from_unixtime(date_created/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition]   " +
			"group by bank_destination " +
			"union " +
			"select min(payment) as method, min(pmb.name) as bank, min(payment_bank_fkid) as bankId, sum(nominal) as grandTotal, 'debt_payment' as tbl " +
			"from debt_payment dp " +
			"left join payment_media_bank pmb on pmb.bank_id = dp.payment_bank_fkid " +
			"left join purchase p on dp.purchase_fkid = p.purchase_id " +
			"where p.outlet_fkid = $outlet_id and from_unixtime(time_created/1000 + $date_add, '%Y-%m-%d') between $date_start and $date_end [add_condition_2]  " +
			"group by payment_bank_fkid  " +
			"order by uid"
	}

	return result
}

func formatDataCashFlowTotal(dataType string, dataArr []map[string]interface{}, result *[]map[string]interface{}, summary map[string]map[string]int) {
	total := 0
	totalAll := 0
	for index, data := range dataArr {
		if index == (len(dataArr)-1) || dataArr[index+1]["uid"] != data["uid"] {
			total += int(utils.ToFloat(data["grand_total"]))
			totalAll += total
			overview := data["uid_name"]
			if overview == nil {
				overview = data["uid_name_2"]
			}
			*result = append(*result, map[string]interface{}{
				"overview": overview,
				"total":    total,
				"type":     dataType,
			})
			total = 0
		} else {
			total += int(utils.ToFloat(data["grand_total"]))
		}
	}
	summary["total_"+dataType]["total"] = totalAll
}

func formatSummaryCashFlow(summary map[string]map[string]float64, reportCategory string) {
	accumulatedCashFlow := 0.0
	totalAccumulatedCashFlow := 0.0
	grandTotalCashFlow := 0.0

	startDate := 1
	maxDate := 31
	isUseWeek := true
	isUseDay := true
	dayFormat := "d%02d"

	if reportCategory == "monthly" {
		maxDate = 12
		isUseWeek = false
		dayFormat = "m%02d"
	} else if reportCategory == "weekly" {
		isUseDay = false
	} else if reportCategory == "yearly" {
		dayFormat = "%02d"
		startDate = 9999

		for key := range summary["total_cash_in"] {
			yearInt, err := strconv.Atoi(key)
			if err == nil {
				if yearInt > maxDate {
					maxDate = yearInt
				}
				if yearInt < startDate {
					startDate = yearInt
				}
			}
		}
	}

	for day := startDate; day <= maxDate; day++ {
		result := summary["total_cash_in"][fmt.Sprintf(dayFormat, day)] - summary["total_cash_out"][fmt.Sprintf(dayFormat, day)]
		grandTotalCashFlow += result

		if isUseDay {
			summary["total_cash_flow"][fmt.Sprintf(dayFormat, day)] = float64(int(result))
		}

		accumulatedCashFlow += result
		totalAccumulatedCashFlow += accumulatedCashFlow

		if isUseDay {
			summary["accumulated_cash_flow"][fmt.Sprintf(dayFormat, day)] = float64(int(accumulatedCashFlow))
		}

		if day%7 == 0 && isUseWeek {
			result = summary["total_cash_in"][fmt.Sprintf(weekFormat, day/7)] - summary["total_cash_out"][fmt.Sprintf(weekFormat, day/7)]
			summary["total_cash_flow"][fmt.Sprintf(weekFormat, day/7)] = float64(int(result))

			summary["accumulated_cash_flow"][fmt.Sprintf(weekFormat, day/7)] = float64(int(accumulatedCashFlow))
		}
	}
	summary["total_cash_flow"]["total"] = float64(int(grandTotalCashFlow))
	summary["accumulated_cash_flow"]["total"] = float64(int(totalAccumulatedCashFlow))
}

func formatDataCashFlow(dataType, reportType string, dataArr []map[string]interface{}, result *[]map[string]interface{}, summary map[string]map[string]float64) {
	dataPerItem := make(map[string]interface{})
	lastUniqueId := "[first]"
	startDate := 0
	totalPerWeek := 0.0
	grandTotalPerCol := 0.0

	maxDate := 31
	dateIndexStart := 8
	isUseWeek := true
	isUseDay := true
	dayFormat := "d%02d"

	if reportType == "monthly" {
		maxDate = 12
		dateIndexStart = 5
		isUseWeek = false
		dayFormat = "m%02d"
	} else if reportType == "weekly" {
		isUseDay = false
	} else if reportType == "yearly" {
		isUseWeek = false
		dayFormat = "%02d"
		dateIndexStart = 0
		startDate = 9999

		for _, data := range dataArr {
			date := utils.ToString(data["tgl"])[dateIndexStart:]
			dateInt, _ := strconv.Atoi(date)

			if dateInt > maxDate {
				maxDate = dateInt
			}
			if dateInt < startDate {
				startDate = dateInt
			}
		}
	}

	lastDay := startDate
	if len(dataArr) == 0 {
		return
	}

	for index, data := range dataArr {
		date := utils.ToString(data["tgl"])[dateIndexStart:]
		dateInt, _ := strconv.Atoi(date)

		if index == 0 {
			lastUniqueId = utils.ToString(data["uid"])
			dataPerItem["overview"] = utils.ToString(data["uid_name"])
			dataPerItem["type"] = strings.Replace(dataType, "_", " ", -1)
			if data["uid_name"] == nil {
				dataPerItem["overview"] = utils.ToString(data["uid_name_2"])
			}
		} else {
			if lastUniqueId != utils.ToString(data["uid"]) {
				for lastDay < maxDate {
					if isUseDay {
						dataPerItem[fmt.Sprintf(dayFormat, lastDay+1)] = 0
						summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)] = summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)]
					}

					if (lastDay)%7 == 0 && isUseWeek {
						dataPerItem[fmt.Sprintf("w%d", lastDay/7)] = float64(int(totalPerWeek))
						summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] = summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] + float64(int(totalPerWeek))
						totalPerWeek = 0
					}
					lastDay++
				}
				dataPerItem["total"] = float64(int(grandTotalPerCol))
				*result = append(*result, dataPerItem)
				dataPerItem = make(map[string]interface{})
				dataPerItem["overview"] = utils.ToString(data["uid_name"])
				dataPerItem["type"] = strings.Replace(dataType, "_", " ", -1)

				summary["total_"+dataType]["total"] = summary["total_"+dataType]["total"] + float64(int(grandTotalPerCol))
				lastDay = startDate
				grandTotalPerCol = 0
			}
		}

		for lastDay < (dateInt - 1) {
			if isUseDay {
				dataPerItem[fmt.Sprintf(dayFormat, lastDay+1)] = 0
				summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)] = summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)]
			}

			if lastDay > 6 && (lastDay)%7 == 0 && isUseWeek {
				dataPerItem[fmt.Sprintf(weekFormat, lastDay/7)] = float64(int(totalPerWeek))
				summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] = summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] + float64(int(totalPerWeek))
				totalPerWeek = 0
			}
			lastDay++
		}

		var grandTotal float64 = 0
		if data["grand_total"] != nil {
			var err error
			grandTotal, err = strconv.ParseFloat(utils.ToString(data["grand_total"]), 64)
			if err != nil {
				fmt.Println("Converting grand total from db error ", err)
				grandTotal = 0
			}
		}

		perItem := dataPerItem[fmt.Sprintf(dayFormat, dateInt)]
		grandTotalBefore, err := strconv.ParseFloat(utils.ToString(perItem), 64)
		if err != nil && perItem != nil {
			fmt.Println("Converting to float error, data : ", perItem, " -- ", err)
		}

		if isUseDay {
			dataPerItem[fmt.Sprintf(dayFormat, dateInt)] = int(grandTotal + grandTotalBefore)
		}

		if dateInt > 7 && (dateInt-1)%7 == 0 && isUseWeek {
			dataPerItem[fmt.Sprintf(weekFormat, lastDay/7)] = float64(int(totalPerWeek))
			summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] = summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] + float64(int(totalPerWeek))
			totalPerWeek = 0
		}

		totalPerWeek += grandTotal + grandTotalBefore
		grandTotalPerCol += grandTotal + grandTotalBefore

		if isUseDay {
			summary["total_"+dataType][fmt.Sprintf(dayFormat, dateInt)] = summary["total_"+dataType][fmt.Sprintf(dayFormat, dateInt)] + float64(int(grandTotal))
		}

		lastUniqueId = utils.ToString(data["uid"])
		lastDay = dateInt
	}

	for lastDay < maxDate {
		if isUseDay {
			dataPerItem[fmt.Sprintf(dayFormat, lastDay+1)] = 0
			summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)] = summary["total_"+dataType][fmt.Sprintf(dayFormat, lastDay+1)]
		}
		if (lastDay)%7 == 0 && isUseWeek {
			dataPerItem[fmt.Sprintf("w%d", lastDay/7)] = float64(int(totalPerWeek))
			summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] = summary["total_"+dataType][fmt.Sprintf(weekFormat, lastDay/7)] + float64(int(totalPerWeek))
			totalPerWeek = 0
		}
		lastDay++
	}
	dataPerItem["total"] = float64(int(grandTotalPerCol))
	summary["total_"+dataType]["total"] = summary["total_"+dataType]["total"] + float64(int(grandTotalPerCol))
	*result = append(*result, dataPerItem)
}

func getCashInByMedia(wg *sync.WaitGroup, reportCategory, outletId, shiftId, date, dateAdd interface{}, result chan []map[string]interface{}) {
	defer wg.Done()
	sql := "select min(method) as uid_name_2, min(name) as uid_name,  min(bank_fkid) as uid, sum(total) as grand_total, from_unixtime(os.time_open/1000 + $date_add, '%Y-%m-%d') as tgl, 'sales' as tbl " +
		"from sales_payment sp " +
		"join sales s on sp.sales_fkid = s.sales_id " +
		"join open_shift os on os.open_shift_id = s.open_shift_fkid " +
		"left join sales_payment_bank spb on sp.payment_id = spb.sales_payment_fkid " +
		"left join payment_media_bank pmb on spb.bank_fkid = pmb.bank_id " +
		"where sp.method != 'COMPLIMENT'  and s.status='success' and s.outlet_fkid = $outlet_id and " +
		"from_unixtime(os.time_open/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by spb.bank_fkid, tgl " +
		"union " +
		"select min(payment), min(pmb.name),  min(bank_origin), sum(sub_total), from_unixtime(date_created/1000 + $date_add, '%Y-%m-%d') as tgl, 'transfer' as tbl " +
		"from transfer " +
		"left JOIN payment_media_bank pmb on transfer.bank_origin = pmb.bank_id " +
		"where outlet_origin_fkid = $outlet_id and from_unixtime(date_created/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by bank_origin, tgl " +
		"order by uid, tgl"

	startDate := ""
	if reportCategory == "monthly" {
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y-%m'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", "=")
		sql = replacer.Replace(sql)
	} else if reportCategory == "yearly" {
		dateStr := utils.ToString(date)
		startDate = dateStr
		if strings.Contains(dateStr, "-") {
			years := strings.Split(dateStr, "-")
			startDate = years[0]
			date = years[1]
		}
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", fmt.Sprintf(" between $date_start and "))
		sql = replacer.Replace(sql)
	} else {
		replacer := strings.NewReplacer("[date_operator]", "=")
		sql = replacer.Replace(sql)
	}

	if utils.ToString(shiftId) != "0" {
		sql = strings.Replace(sql, "[add_condition]", " and shift_fkid = $shift_id ", -1)
	} else {
		sql = strings.Replace(sql, "[add_condition]", "", -1)
	}

	data, err := db.QueryArrayFun(sql, map[string]interface{}{
		"date_add":   dateAdd,
		"date":       date,
		"date_start": startDate,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})

	if log.IfError(err) {
		fmt.Println("Query get getCashInByMedia error - ", err, "\n", sql)
	}
	result <- data
}

func getCashOutByMedia(wg *sync.WaitGroup, reportCategory, outletId, shiftId, date, dateAdd interface{}, result chan []map[string]interface{}) {
	defer wg.Done()
	sql := "select min(pay_type) as uid_name_2, min(pmb.name) as uid_name, min(pmb.bank_id) as uid, sum(grand_total) as grand_total, from_unixtime(p.data_created/1000 + $date_add, '%Y-%m-%d') as tgl, 'purchase' as tbl from purchase p " +
		"left join payment_media_bank pmb on pmb.bank_id=p.payment_media_bank_fkid " +
		"where outlet_fkid = $outlet_id and from_unixtime(p.data_created/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by payment_media_bank_fkid, tgl " +
		"union " +
		"select min(payment) as method, min(pmb.name) as bank, min(payment_bank_fkid) as bankId,sum(total) as grandTotal, from_unixtime(time_created/1000 + $date_add, '%Y-%m-%d') as tgl, 'operational_cost' as tbl " +
		"from operationalcost oc " +
		"left join payment_media_bank pmb on oc.payment_bank_fkid = pmb.bank_id " +
		"where outlet_fkid = $outlet_id and from_unixtime(time_created/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by payment_bank_fkid, tgl " +
		"union " +
		"select min(payment) as method, min(pmb.name) as bank, min(bank_destination) as bankId, sum(sub_total) as grandTotal, from_unixtime(date_created/1000 + $date_add, '%Y-%m-%d') as tgl, 'transfer' as tbl " +
		"from transfer " +
		"left JOIN payment_media_bank pmb on transfer.bank_destination = pmb.bank_id " +
		"where outlet_destination_fkid = $outlet_id and from_unixtime(date_created/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by bank_destination, tgl " +
		"union " +
		"select min(payment) as method, min(pmb.name) as bank, min(payment_bank_fkid) as bankId, sum(nominal) as grandTotal, from_unixtime(time_created/1000 + $date_add, '%Y-%m-%d') as tgl, 'debt_payment' as tbl " +
		"from debt_payment dp " +
		"left join payment_media_bank pmb on pmb.bank_id = dp.payment_bank_fkid " +
		"left join purchase p on dp.purchase_fkid = p.purchase_id " +
		"where p.outlet_fkid = $outlet_id and from_unixtime(time_created/1000 + $date_add, '%Y-%m') [date_operator] $date [add_condition] " +
		"group by payment_bank_fkid,tgl " +
		"order by uid, tgl"

	startDate := ""
	if reportCategory == "monthly" {
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y-%m'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", "=")
		sql = replacer.Replace(sql)
	} else if reportCategory == "yearly" {
		dateStr := utils.ToString(date)
		startDate = dateStr
		if strings.Contains(dateStr, "-") {
			years := strings.Split(dateStr, "-")
			startDate = years[0]
		}
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", fmt.Sprintf(" between $date_start and "))
		sql = replacer.Replace(sql)
	} else {
		replacer := strings.NewReplacer("[date_operator]", "=")
		sql = replacer.Replace(sql)
	}

	if utils.ToString(shiftId) != "0" {
		sql = strings.Replace(sql, "[add_condition]", " and shift_fkid = $shift_id ", -1)
	} else {
		sql = strings.Replace(sql, "[add_condition]", "", -1)
	}

	data, err := db.QueryArrayFun(sql, map[string]interface{}{
		"date_add":   dateAdd,
		"date":       date,
		"date_start": startDate,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})
	if log.IfError(err) {
		fmt.Println("Query get getCashOutByMedia error - ", err)
	}
	result <- data
}

func getCashInByCategory(wg *sync.WaitGroup, result chan []map[string]interface{}, reportCategory, outletId, shiftId, date, dateAdd interface{}) {
	defer wg.Done()
	sql := `SELECT round(sum(sd.sub_total / sdt.totalSubTotal * sp.allTotal)) as grand_total,
       from_unixtime(o.time_open / 1000 + $date_add, '%Y-%m-%d')      as tgl,
       prc.name                                                   as uid_name,
       prc.purchase_report_category_id                            as uid,
       'sales'                                                    as tbl
from sales s
       left JOIN sales_detail sd on sd.sales_fkid = s.sales_id
       left JOIN products p on p.product_id = sd.product_fkid
       left JOIN purchase_report_category prc on prc.purchase_report_category_id = p.purchase_report_category_fkid
       left join open_shift o on s.open_shift_fkid = o.open_shift_id
       left join (select sum(total) as allTotal, min(sales_fkid) as sales_fkid
                  from sales_payment
                  group by sales_fkid) sp on sp.sales_fkid = s.sales_id
       left join (select sum(sdd.sub_total) as totalSubTotal,
                         sdd.sales_fkid
                  from sales_detail sdd
                  group by sdd.sales_fkid) sdt on sdt.sales_fkid = s.sales_id
WHERE s.outlet_fkid = $outlet_id 
  and s.status = 'success'
  and s.payment != 'compliment'
  and from_unixtime(o.time_open / 1000 + $date_add, [date_converter] ) [date_operator] $date [add_condition] 
group by tgl, prc.purchase_report_category_id
union
select sum((select tc.total/tc.sub_total*tc.grand_total)) as grand_total,
       from_unixtime(tc.data_created/1000 + $date_add,'%Y-%m-%d') as tgl,
       category.name as uid_name,
       category.product_category_id as uid,
	   'transfer'
from transfer_confirm tc
       join transfer_products tp on tc.transfer_product_fkid = tp.transfer_product_id
       join transfer t on tp.transfer_fkid = t.transfer_id
       join products_detail detail on tp.product_detail_des_fkid = detail.product_detail_id
       join products p2 on detail.product_fkid = p2.product_id
       join products_category category on p2.product_category_fkid = category.product_category_id
where t.outlet_origin_fkid = $outlet_id 
  and from_unixtime(tc.data_created/1000 + $date_add, [date_converter] ) [date_operator] $date  [add_condition]  
group by category.product_category_id, tgl
order by uid, tgl`

	fmt.Println("Date : ", date)
	startDate := ""
	if reportCategory == "monthly" {
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y-%m'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", "=")
		sql = replacer.Replace(sql)
	} else if reportCategory == "yearly" {
		dateStr := utils.ToString(date)
		startDate = dateStr
		if strings.Contains(dateStr, "-") {
			years := strings.Split(dateStr, "-")
			startDate = years[0]
			date = years[1]
		}
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", fmt.Sprintf(" between $date_start and "))
		sql = replacer.Replace(sql)
	} else if reportCategory == "daily" && strings.Contains(utils.ToString(date), "--") {
		dateRange := strings.Split(utils.ToString(date), "--")
		startDate = dateRange[0]
		date = dateRange[1]
		replacer := strings.NewReplacer("[date_converter]", "'%Y-%m-%d'",
			"[date_operator]", " between $date_start and ")
		sql = replacer.Replace(sql)
	} else {
		replacer := strings.NewReplacer("[date_operator]", "=")
		sql = replacer.Replace(sql)
	}

	replacer := strings.NewReplacer("[date_converter]", "'%Y-%m'")
	sql = replacer.Replace(sql)

	if utils.ToString(shiftId) != "0" {
		sql = strings.Replace(sql, "[add_condition]", " and shift_fkid = $shift_id ", -1)
	} else {
		sql = strings.Replace(sql, "[add_condition]", "", -1)
	}

	data, err := db.QueryArrayFun(sql, map[string]interface{}{
		"date_add":   dateAdd,
		"date":       date,
		"date_start": startDate,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})
	log.IfError(err)

	result <- data
}

func getCashOutByCategory(wg *sync.WaitGroup, result chan []map[string]interface{}, reportCategory, outletId, shiftId, date, dateAdd interface{}) {
	defer wg.Done()
	sql := `select prc.purchase_report_category_id as uid, prc.name as uid_name, from_unixtime(p2.data_created/1000 + $date_add, '%Y-%m-%d') as tgl, sum(pp.tot_dis) as grand_total, 'purchase' as tbl
    from purchase_products pp
      join purchase p2 on pp.purchase_fkid = p2.purchase_id
      join products_detail pd on pp.products_fkid = pd.product_detail_id
      join products p on pd.product_fkid = p.product_id
      join purchase_report_category prc on p.purchase_report_category_fkid = prc.purchase_report_category_id
    where p2.outlet_fkid= $outlet_id and from_unixtime(p2.data_created/1000 + $date_add, [date_converter]) [date_operator] $date [add_condition]
    group by prc.purchase_report_category_id, tgl

		union

    select oc.prc_category_fkid, c.name, from_unixtime(oc.time_created/1000 + $date_add, '%Y-%m-%d') as tgl, sum(total), 'operational_cost'
    from operationalcost oc
      join purchase_report_category c on oc.prc_category_fkid = c.purchase_report_category_id
    where oc.outlet_fkid= $outlet_id and from_unixtime(oc.time_created/1000 + $date_add, [date_converter]) [date_operator] $date [add_condition]
    group by oc.prc_category_fkid, tgl

		union

    select prc2.purchase_report_category_id, prc2.name, from_unixtime(t.date_created/1000 + $date_add, '%Y-%m-%d') as tgl, sum(total), 'transfer'
    from transfer_products tp
      join transfer t on tp.transfer_fkid = t.transfer_id
      join products_detail d on tp.product_detail_des_fkid = d.product_detail_id
      join products p3 on d.product_fkid = p3.product_id
      join purchase_report_category prc2 on p3.purchase_report_category_fkid = prc2.purchase_report_category_id
    where t.outlet_destination_fkid = $outlet_id and from_unixtime(t.date_created/1000 + $date_add, [date_converter])[date_operator] $date [add_condition] 
    group by  prc2.purchase_report_category_id, tgl

		union

    select  prc3.purchase_report_category_id, prc3.name, from_unixtime(dp.time_created/1000 + $date_add, '%Y-%m-%d') as tgl, sum(pp2.tot_dis/(select sum(tot_dis) from purchase_products where purchase_fkid = p4.purchase_id) * dp.nominal), 'debt'
    from debt_payment dp
      join purchase p4 on dp.purchase_fkid = p4.purchase_id
      join purchase_products pp2 on p4.purchase_id = pp2.purchase_fkid
      join products_detail pd2 on pp2.products_fkid = pd2.product_detail_id
      join products p5 on pd2.product_fkid = p5.product_id
      join purchase_report_category prc3 on p5.purchase_report_category_fkid = prc3.purchase_report_category_id
    where p4.outlet_fkid= $outlet_id and from_unixtime(dp.time_created/1000 + $date_add, [date_converter]) [date_operator] $date [add_condition_2] 
    group by prc3.purchase_report_category_id, tgl

		order by uid, tgl`

	startDate := ""
	if reportCategory == "monthly" {
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y-%m'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", "=")
		sql = replacer.Replace(sql)
	} else if reportCategory == "yearly" {
		dateStr := utils.ToString(date)
		startDate = dateStr
		if strings.Contains(dateStr, "-") {
			years := strings.Split(dateStr, "-")
			startDate = years[0]
		}
		replacer := strings.NewReplacer("'%Y-%m-%d'", "'%Y'",
			"'%Y-%m'", "'%Y'",
			"[date_operator]", fmt.Sprintf(" between $date_start and "))
		sql = replacer.Replace(sql)
	} else if reportCategory == "daily" && strings.Contains(utils.ToString(date), "--") {
		dateRange := strings.Split(utils.ToString(date), "--")
		startDate = dateRange[0]
		date = dateRange[1]
		replacer := strings.NewReplacer("[date_converter]", "'%Y-%m-%d'",
			"[date_operator]", " between $date_start and ")
		sql = replacer.Replace(sql)
	} else {
		replacer := strings.NewReplacer("[date_operator]", "=")
		sql = replacer.Replace(sql)
	}

	replacer := strings.NewReplacer("[date_converter]", "'%Y-%m'")
	sql = replacer.Replace(sql)

	if utils.ToString(shiftId) != "0" {
		sql = strings.Replace(sql, "[add_condition]", " and shift_fkid = $shift_id ", -1)
		sql = strings.Replace(sql, "[add_condition_2]", " and dp.shift_fkid = $shift_id ", -1)
	} else {
		sql = strings.Replace(sql, "[add_condition]", "", -1)
		sql = strings.Replace(sql, "[add_condition_2]", "", -1)
	}

	query, agrs := db.FormatArgs(sql, map[string]interface{}{
		"date_add":   dateAdd,
		"date":       date,
		"date_start": startDate,
		"outlet_id":  outletId,
		"shift_id":   shiftId,
	})
	data, err := db.QueryArray(query, agrs...)
	log.IfError(err)

	result <- data
}
