package db

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	"github.com/joho/godotenv"
	"github.com/redis/go-redis/v9"
)

var db *sql.DB

func init() {
	initDb()
}

func initDb() {
	fmt.Println("__________Init database connection!____________")
	e := godotenv.Load() //Load .env file
	if e != nil {
		fmt.Println(e)
	}

	username := os.Getenv("db_user")
	password := os.Getenv("db_password")
	dbName := os.Getenv("db_name")
	dbHost := os.Getenv("db_host")
	dbComm := os.Getenv("db_comm")
	dbPort := os.Getenv("db_port")

	if username == "" && password == "" && dbHost == "" && dbName == "" {
		if strings.HasSuffix(os.Args[0], ".test") {
			fmt.Println("--- db config skip on testing... ")
			return
		}
		panic(fmt.Errorf("database config not provided in env file"))
	}

	if dbPort == "" {
		dbPort = "3306"
	}

	var err error
	source := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, dbHost, dbPort, dbName)
	if dbComm == "unix" {
		source = fmt.Sprintf("%s:%s@unix(/cloudsql/%s)/%s", username, password, dbHost, dbName)
	}
	db, err = sql.Open("mysql", source)
	if err != nil {
		log.Fatalf("Could not open Db: %v", err)
	}
	db.SetMaxIdleConns(0)

	err = db.Ping()
	if err != nil {
		fmt.Println("ping db err: ", err)
	} else {
		fmt.Println("--- database successfully initialized ---")
		fmt.Printf("use db: %v, at: %v ", dbName, dbHost)
	}
}

func GetConn() *sql.DB {
	if db == nil {
		initDb()
	}
	if db == nil {
		fmt.Println("---- ERROR: Database is Not Initialized! ---")
	}
	return db
}

var redisClient *redis.Client

func GetRedisClient() *redis.Client {
	if redisClient == nil {
		redisClient = redis.NewClient(&redis.Options{
			Addr:        os.Getenv("REDIS_ENDPOINT"),
			Password:    os.Getenv("REDIS_PASSWORD"),
			Username:    os.Getenv("REDIS_USERNAME"),
			DialTimeout: 10 * time.Second,
			DB:          0, // use default DB
		})

		status := redisClient.Ping(context.Background())
		ping, err := status.Result()
		fmt.Printf(">>> Ping Redis DB: %v | %v\n", ping, err)
	}

	return redisClient
}
