package usecase

import (
	"fmt"
	"os"
	"testing"
)

// TestGenerateGraph tests the generateGraph function for successful graph generation.
func TestGenerateGraph(t *testing.T) {
	// Simulate input data
	data := []map[string]interface{}{
		{"date": "10/12/2024", "total": 100.0},
		{"date": "11/12/2024", "total": 150.0},
		{"date": "12/12/2024", "total": 120.0},
		{"date": "13/12/2024", "total": 170.0},
		{"date": "14/12/2024", "total": 110.0},
		{"date": "15/12/2024", "total": 115.0},
		{"date": "16/12/2024", "total": 155.0},
	}

	// Call the function under test
	filePath, err := generateGraph2(data)
	if err != nil {
		t.Fatalf("generateGraph returned an error: %v", err)
	}

	fmt.Println("generate: ", filePath)
	// Check if the file was created
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Fatalf("expected file %s to exist, but it does not", filePath)
	}

	// Cleanup: remove the generated file after the test
	// defer os.Remove(filePath)
}
