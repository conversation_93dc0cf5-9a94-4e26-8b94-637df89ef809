package utils

import (
	"reflect"
	"testing"
)

func TestFilterAccessibleOutlets(t *testing.T) {
	type args struct {
		outletRequests []string
		outletAccess   []int
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{"test", args{[]string{"1","2"}, []int{1}}, []string{"1"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := FilterAccessibleOutlets(tt.args.outletRequests, tt.args.outletAccess); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("FilterAccessibleOutlets() = %v, want %v", got, tt.want)
			}
		})
	}
}
