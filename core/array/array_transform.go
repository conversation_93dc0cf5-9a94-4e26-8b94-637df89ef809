package array

import (
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func TransformStringToInt(data []string) []int {
	result := make([]int, 0)
	for _, row := range data {
		if strings.TrimSpace(row) != "" {
			if data := cast.ToInt(row); data > 0 {
				result = append(result, data)
			}
		}
	}
	return result
}

func TransformIntToInterface(data []int) []interface{} {
	result := make([]interface{}, 0)
	for _, row := range data {
		if row > 0 {
			result = append(result, row)
		}
	}
	return result
}

func TransformToInterface(dataArray interface{}) []interface{} {
	result := make([]interface{}, 0)
	if arrayInt, ok := dataArray.([]int); ok {
		for _, raw := range arrayInt {
			result = append(result, raw)
		}
	} else if arrayStr, ok := dataArray.([]string); ok {
		for _, raw := range arrayStr {
			result = append(result, raw)
		}
	} else if sliceData, ok := dataArray.([]int64); ok {
		for _, raw := range sliceData {
			result = append(result, raw)
		}
	}
	return result
}

func IsIn(data interface{}, dataArray interface{}) bool {
	if arrayInt, ok := dataArray.([]int); ok {
		for _, raw := range arrayInt {
			if data == raw {
				return true
			}
		}
	} else if arrayStr, ok := dataArray.([]string); ok {
		for _, raw := range arrayStr {
			if data == raw {
				return true
			}
		}
	}
	return false
}

func RemoveEmpty(data []string) []string {
	result := make([]string, 0)
	for _, row := range data {
		if strings.TrimSpace(row) != "" {
			result = append(result, row)
		}
	}
	return result
}

func ToMapIndex(data []string) map[string]int {
	result := make(map[string]int)
	for i, row := range data {
		result[row] = i
	}
	return result
}

func RemoveElements[T comparable](original []T, toRemove []T) []T {
	var result []T
	removeMap := make(map[T]bool)

	for _, v := range toRemove {
		removeMap[v] = true
	}

	for _, v := range original {
		if _, found := removeMap[v]; !found {
			result = append(result, v)
		}
	}
	return result
}
