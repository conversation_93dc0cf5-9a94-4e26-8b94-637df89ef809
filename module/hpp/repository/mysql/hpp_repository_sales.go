package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h hppRepository) FetchSalesDetailBySalesId(salesId string) (*[]models.SalesDetail, error) {
	sql := `
SELECT
	sd.sales_detail_id,
	ANY_VALUE(sd.product_detail_fkid) AS product_detail_fkid,
	(ANY_VALUE(sd.qty) - sum(coalesce(sv.qty, 0))) AS qty
FROM
	sales_detail sd
	LEFT JOIN sales_void sv ON sd.sales_detail_id = sv.sales_detail_fkid
WHERE
	sd.sales_fkid = ? 
GROUP BY
	sd.sales_detail_id `
	var result []models.SalesDetail
	err := h.db.Query(sql, salesId).Model(&result)
	return &result, err
}

func (h hppRepository) FetchSalesBreakdown(salesId string) (*[]models.SalesBreakdown, error) {
	sql := `SELECT
		sales_detail_fkid,
		group_concat(sales_breakdown_id) as sales_breakdown_ids,
		any_value(sb.product_detail_fkid) product_detail_fkid,
		sum(sb.qty_total * if(status = 'void', - 1, 1)) AS qty,
		ANY_VALUE(sb.price_buy) AS price_buy
	FROM
		sales_breakdown sb
		JOIN sales_detail sd ON sb.sales_detail_fkid = sd.sales_detail_id
	WHERE
		sd.sales_fkid = ?
	GROUP BY
		sb.sales_detail_fkid, sb.product_detail_fkid`

	// 	sql := `SELECT
	// 	sales_detail_fkid,
	// 	(sales_breakdown_id) as sales_breakdown_id,
	// 	(sb.product_detail_fkid) product_detail_fkid,
	// 	(sb.qty_total * if(status = 'void', - 1, 1)) AS qty,
	// 	(sb.price_buy) AS price_buy
	// FROM
	// 	sales_breakdown sb
	// 	JOIN sales_detail sd ON sb.sales_detail_fkid = sd.sales_detail_id
	// WHERE
	// 	sd.sales_fkid = ?`
	var result []models.SalesBreakdown
	err := h.db.Query(sql, salesId).Model(&result)
	return &result, err
}

func (h hppRepository) UpdatePriceBuySalesBreakdown(updates []models.PriceBuyUpdate) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		for _, price := range updates {
			tx.Update("sales_breakdown", map[string]interface{}{
				"price_buy": price.PriceBuy,
			}, "sales_breakdown_id = ?", price.Id)
		}
		return nil
	})
	return err
}
