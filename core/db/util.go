package db

import (
	"fmt"
	"strings"
)

func FormatArgs(sql string, data map[string]interface{})  (string, []interface{}) {
	result := make([]interface{},0)
	for true {
		indexStart := strings.Index(sql, "$")
		if indexStart >= 0 {
			indexEnd := 0

			for i := indexStart+1; i < len(sql); i++ {
				found := sql[indexStart:i+1]
				if strings.Contains(found, ",") || strings.Contains(found, " ") ||
					strings.Contains(found, ")")|| strings.Contains(found, "(") ||
					strings.Contains(found, "'") {
					indexEnd = i
					break
				}
			}

			if indexEnd == 0 {
				indexEnd = len(sql)
			}

			key := sql[indexStart+1 : indexEnd]
			key = strings.TrimSpace(key)
			if data[key] != nil {
				result = append(result, data[key])
			}else{
				fmt.Printf("Map Data with key '%s' Not Found! \n", key)
			}
			sql = strings.Replace(sql, fmt.Sprintf("$%s", key), " ? ", 1)
		}else{
			break
		}
	}

	return sql, result
}
