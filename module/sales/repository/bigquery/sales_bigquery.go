package bigquery

import (
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
)

type salesRepository struct {
	bq.Repository
}

// FetchSalesTotalByOutlet implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSalesTotalByOutlet(startDate int64, endDate int64, outletIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesDetail implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSalesDetail(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesHistory implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSalesHistory(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchShiftByOpenShift implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchShiftByOpenShift(openShiftIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesDetail implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesDetail(salesIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesDetailDiscount implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesDetailDiscount(groupBy domain.SalesGroupKey, discType []string, salesIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesDetailPromotion implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesDetailPromotion(groupBy domain.SalesGroupKey, ids ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesDetailTaxPerCategory implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesDetailTaxPerCategory(ids ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesPromotion implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesPromotion(salesIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesTaxPerCategory implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesTaxPerCategory(salesIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSumSalesVoid implements domain.SalesRepositoryReplica.
func (s *salesRepository) FetchSumSalesVoid(groupBy domain.SalesGroupKey, salesIds ...interface{}) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

func NewBigQuerySalesRepository(client *bigquery.Client) domain.SalesRepositoryReplica {
	return &salesRepository{bq.Repository{Client: client}}
}

func (s salesRepository) FetchSalesHistoryTransaction(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `
SELECT s.sales_id AS sales_id, any_value(s.display_nota) AS nomor_nota, any_value(s.display_nota) AS display_nota, any_value(s.time_created) AS tanggal, any_value(s.status) AS status, any_value(s.qty_customers) AS pax, 
any_value(sp.promotion_fkid) as promotion_fkid, any_value(sales_tag_fkid) as  sales_tag_fkid,
(SELECT ARRAY_AGG(DISTINCT promotion_fkid) from [DB].sales_detail_promotion where sales_fkid = s.sales_id) as promotion_fkids,
(select string_agg(DISTINCT voucher_code) from [DB].sales_promotion where sales_fkid = s.sales_id) as voucher_codes, 
SUM(s.discount) AS discountSales, SUM(sd.discount) AS discountSd, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'discount' AND sales_fkid = s.sales_id), 0) AS discountTax, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'service' AND sales_fkid = s.sales_id), 0) AS service, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'tax' and sales_fkid = s.sales_id), 0) AS tax, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'voucher' AND sales_fkid = s.sales_id), 0) AS voucherTax,
         any_value(s.grand_total) AS grand_total, any_value(s.voucher) AS voucherSales, any_value(s.voucher_info) AS keterangan_voucherS,any_value( s.discount_info) AS keterangan_discountS, MIN(sd.discount_info) AS keterangan_discountSd, sum(sd.sub_total) AS sub_total, MIN(o.name) AS outlet_name, any_value(s.time_created) AS time_created, (
            SELECT
                sum(sub_total)
            from
                [DB].sales_void sv
            where
                sv.sales_fkid = s.sales_id) as sub_void, sum((select sum(total) FROM [DB].sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales, (ifnull((select SUM(promotion_value) FROM [DB].sales_promotion where sales_fkid = s.sales_id), 0)+ifnull((select SUM(promotion_value) FROM [DB].sales_detail_promotion where sales_fkid=s.sales_id and sales_detail_fkid is not null), 0)) as promo,
         ANY_VALUE(os.shift_fkid) AS shift_fkid, ANY_VALUE(s.employee_fkid) AS employee_fkid
FROM [DB].sales s
LEFT JOIN [DB].outlets o ON s.outlet_fkid = o.outlet_id
LEFT JOIN [DB].open_shift os ON s.open_shift_fkid = os.open_shift_id
LEFT JOIN [DB].sales_detail sd ON sd.sales_fkid = s.sales_id
LEFT JOIN [DB].sales_promotion sp on sp.sales_fkid=s.sales_id
WHERE o.admin_fkid = @adminId
AND s.data_status = 'on'
{{WHERE}}
GROUP BY s.sales_id
ORDER BY tanggal `

	maxLimit := 100
	whereQuery := ""
	limitQuery := fmt.Sprintf(" LIMIT %v OFFSET %v ", maxLimit, maxLimit*request.Page)

	if len(request.Outlet) > 0 {
		whereQuery += " and s.outlet_fkid in UNNEST(@outletId) "
	}
	if request.OffsetId > 0 {
		whereQuery += " and s.time_created > @offsetId "
		limitQuery = fmt.Sprintf(" LIMIT %v ", maxLimit)
	}
	if (request.StartDate > 0 || request.EndDate > 0) && request.EndDate >= request.StartDate {
		whereQuery += " AND s.time_created BETWEEN @startDate AND @endDate "
	}
	if request.PromotionId > 0 {
		whereQuery += " AND (sp.promotion_fkid=@promotionId  OR s.sales_id in (select sales_fkid from [DB].sales_detail_promotion where promotion_fkid=@promotionId )) "
	}

	//by customer
	if request.Contact != "" {
		whereQuery += " AND s.receipt_receiver = @contact "
	} else if request.Customer != "" {
		whereQuery += " AND s.customer_name = @customerName "
	}

	// if whereQuery != "" {
	// 	whereQuery = " AND " + whereQuery
	// }

	sql = strings.Replace(sql, "{{WHERE}}", whereQuery, 1)
	sql = strings.ReplaceAll(sql, "[DB].", s.DbName())
	sql += limitQuery

	sqlParams := bq.MapParam(map[string]interface{}{
		"startDate":    request.StartDate,
		"endDate":      request.EndDate,
		"adminId":      cast.ToInt64(user.BusinessId),
		"outletId":     request.Outlet,
		"offsetId":     request.OffsetId,
		"promotionId":  request.PromotionId,
		"contact":      request.Contact,
		"customerName": request.Customer,
	})

	return s.Query(sql, sqlParams...).MapArray()
}

func (s salesRepository) FetchSalesRankingPlain(request domain.SalesRankingRequest, user domain.User) ([]map[string]interface{}, error) {
	sql := `SELECT
        sd.product_fkid AS id,
        sum(sd.price) AS price,
        sum(sd.qty) AS qty,     
        sum(sd.sub_total) AS sub_total,
        sum(s.qty_customers) AS pax,    
        string_agg(DISTINCT cast(IFNULL(s.outlet_fkid, 0) AS string)) AS outlet_ids,
        string_agg(DISTINCT cast(IFNULL(os.shift_fkid, 0) AS string)) AS shift_ids
FROM $DB.sales_detail sd
        LEFT JOIN $DB.sales s ON s.sales_id = sd.sales_fkid
        LEFT JOIN $DB.outlets o ON s.outlet_fkid = o.outlet_id
        LEFT JOIN $DB.open_shift os ON s.open_shift_fkid = os.open_shift_id 
WHERE
	o.admin_fkid = ?
	AND s.status = 'Success'
	AND s.data_status = 'on'
	AND s.time_created BETWEEN ? AND ? `

	params := make([]interface{}, 0)
	params = append(params, user.BusinessId, request.StartDate, request.EndDate)

	if len(request.Shift) > 0 {
		sql += fmt.Sprintf(" AND os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ","))
		for _, id := range request.Shift {
			params = append(params, id)
		}
	}

	//if len(request.Category) > 0 {
	//	sql += fmt.Sprintf(" AND pc.product_category_id in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Category)), ","))
	//	for _, id := range request.Category {
	//		params = append(params, id)
	//	}
	//}

	if len(request.Outlet) > 0 {
		sql += fmt.Sprintf(" AND s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ","))
		for _, id := range request.Outlet {
			params = append(params, id)
		}
	}

	//if len(request.SubCategory) > 0 {
	//	sql += fmt.Sprintf(" AND ps.product_subcategory_id in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.SubCategory)), ","))
	//	for _, id := range request.SubCategory {
	//		params = append(params, id)
	//	}
	//}

	if len(request.ProductId) > 0 {
		sql += fmt.Sprintf(" AND sd.product_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.ProductId)), ","))
		params = append(params, request.ProductId...)
	}

	sql += " GROUP by sd.product_fkid "

	sql = strings.Replace(sql, "$DB.", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisOutletByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid) as product_fkid,
       any_value(s.outlet_fkid)   as outlet_fkid,
       sum(s.grand_total)         as total
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         join [db].outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ?
` + whereQuery + `
group by sd.product_detail_fkid
order by product_fkid `

	sql = `select sd.product_detail_fkid,
       any_value(sd.product_fkid)                                                   as product_fkid,
       any_value(s.outlet_fkid)                                                     as outlet_fkid,
       sum(s.grand_total)                                                           as total_grandtotal,
       sum(sd.price * (sd.qty - coalesce(sv.qty, 0)))                               as subtotal,
       sum(sd.discount - coalesce(sv.discount, 0) + coalesce(sdd.total, 0))         as discount,
       sum(if(sdt.category = 'tax' or sdt.category = 'service', sdt.total, 0))      as gratuity_tax,
       sum(if(sdt.category = 'voucher' or sdt.category = 'discount', sdt.total, 0)) as gratuity_discount
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         join [db].outlets o on s.outlet_fkid = o.outlet_id
         left join (
    select sum(total) as total, sales_detail_fkid from [db].sales_detail_discount group by sales_detail_fkid
) sdd on sd.sales_detail_id = sdd.sales_detail_fkid
         left join (
    select sum(total) as total, category, sales_detail_fkid from [db].sales_detail_tax group by sales_detail_fkid, category
) sdt on sd.sales_detail_id = sdt.sales_detail_fkid
         left join (select sum(qty) as qty, sum(discount) as discount, sales_detail_fkid
                    from [db].sales_void
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and s.data_status = 'on'
  and o.admin_fkid = ?
  ` + whereQuery + `
group by sd.product_detail_fkid
order by product_fkid`

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisOutletByQty(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from [db].sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join [db].outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ? 
  and s.status = 'Success' 
  and o.admin_fkid = ? 
` + whereQuery + ` 
group by sd.product_detail_fkid
order by product_fkid `

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisByNominal(groupBy string, request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.TimeZone*1000, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	dateFormat := ""
	if groupBy == "hour" {
		dateFormat = "%H"
	} else if groupBy == "week" {
		dateFormat = "%v"
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid) as product_fkid,
       any_value(s.outlet_fkid)   as outlet_fkid,
       sum(s.grand_total)         as total,
		FORMAT_DATE("` + dateFormat + `", TIMESTAMP_MILLIS(s.time_created + ?)) as day
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         join [db].outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day `

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisByQty(groupBy string, request domain.DataTableRequest, adminId interface{}) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.TimeZone*1000, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	dateFormat := ""
	if groupBy == "hour" {
		dateFormat = "%H"
	} else if groupBy == "week" {
		dateFormat = "%v"
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total,       
       FORMAT_DATE("` + dateFormat + `", TIMESTAMP_MILLIS(s.time_created + ?)) as day
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from [db].sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join [db].outlets o on s.outlet_fkid = o.outlet_id 
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day `

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisDayByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.TimeZone*1000, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid) as product_fkid,
       any_value(s.outlet_fkid)   as outlet_fkid,
       sum(s.grand_total)         as total,
       EXTRACT(DAYOFWEEK FROM TIMESTAMP_MILLIS(s.time_created + ?))-1 as day       
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         join [db].outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day `

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesAnalysisDayByQty(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]interface{}{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]interface{}, 0)
	params = append(params, request.TimeZone*1000, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join [db].open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total,       
       EXTRACT(DAYOFWEEK FROM TIMESTAMP_MILLIS(s.time_created + ?))-1 as day 
from [db].sales s
         join [db].sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from [db].sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join [db].outlets o on s.outlet_fkid = o.outlet_id 
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day `

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	fmt.Println(sql)
	return s.Query(sql).MapArray()
}

func (s salesRepository) FetchSalesByMedia(user domain.User, request domain.SalesReportMediaRequest) ([]map[string]interface{}, error) {
	sql := `SELECT
	sp.method as payment,
	SUM(sp.total) AS total,
	count(distinct s.sales_id) as total_transaction,
	spb.bank_fkid,
	FORMAT_DATE(@dateFormat, (timestamp_seconds(CAST(any_value(s.time_created) / 1000 + @timeZone AS INT64)))) as created_at,
	max(s.time_created) as max_time_created,
	any_value(o.name) as outlet_name, any_value(s.outlet_fkid) as outlet_fkid,
	any_value(s.display_nota) as display_nota,
	any_value(spb.account_number) as account_number
  FROM
	[db].sales_payment sp
  JOIN
	[db].sales s
  ON
	s.sales_id=sp.sales_fkid
  JOIN
  [db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os  
	ON os.open_shift_id=s.open_shift_fkid
  LEFT JOIN
  [db].sales_payment_bank spb
  ON
	spb.sales_payment_fkid=sp.payment_id	
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND [dateOption] BETWEEN @startDate AND @endDate
	AND s.time_created > @offsetId
	[where]
  GROUP BY
  [group]
  ORDER BY created_at, sp.method, spb.bank_fkid`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery += " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.BankId) > 0 {
		request.PaymentMethod = append(request.PaymentMethod, "CARD") //to make sure the query also filter by method card
		whereQuery += " AND (spb.bank_fkid IS NULL or spb.bank_fkid in UNNEST(@bankIds)) "
	}

	//should run after checking paymentMethod
	if len(request.PaymentMethod) > 0 {
		whereQuery += " AND sp.method in UNNEST(@paymentMethods) "
	}

	groupFields := make([]string, 0)
	if request.GroupBy == "media" && request.GroupKey != "outlet" {
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid")
	} else if request.GroupBy == "transaction" {
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid", "s.sales_id")
	} else {
		//FORMAT_DATE(@dateFormat, date(timestamp_seconds(s.time_created / 1000 + @timeZone)))
		// groupFields = append(groupFields, "sp.method", "spb.bank_fkid", "FROM_UNIXTIME(s.time_created / 1000 + @timeZone, @dateFormat)")
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid", "FORMAT_DATE(@dateFormat, date(timestamp_seconds(CAST(s.time_created / 1000 + @timeZone AS INT64))))")

		if request.GroupKey == "outlet" {
			groupFields = append(groupFields, "s.outlet_fkid")
		}
	}

	selectTime := `FROM_UNIXTIME(ANY_VALUE(s.time_created) / 1000 + @timeZone, @dateFormat)`
	dateFormat := "%d-%m-%Y"
	if request.DateGroup == "monthly" {
		dateFormat = "%m-%Y"
	} else if request.DateGroup == "yearly" {
		dateFormat = "%Y"
	} else if request.DateGroup == "hourly" {
		dateFormat = "%H"
	} else if request.DateGroup == "quarterly" {
		dateFormat = "%Q"
	}

	dateOption := "os.time_open"
	if request.DataType == 1 {
		dateOption = "s.time_created"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)
	sql = strings.Replace(sql, "[group]", strings.Join(groupFields, ","), 1)
	sql = strings.Replace(sql, "[dateOption]", dateOption, 1)
	sql = strings.Replace(sql, "[SELECT_TIME]", selectTime, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate":      request.StartDate,
		"endDate":        request.EndDate,
		"adminId":        cast.ToInt(user.BusinessId),
		"outletIds":      request.Outlet,
		"timeZone":       request.TimeZone,
		"dateFormat":     dateFormat,
		"offsetId":       request.OffsetId,
		"bankIds":        request.BankId,
		"paymentMethods": request.PaymentMethod,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSummarySales(user domain.User, param domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `SELECT
	SUM(discount) AS discount,
	SUM(voucher) AS voucher,
	SUM(qty_customers) AS pax,
	COUNT(*) AS total_bill,
	SUM(grand_total) AS grand_total,
	status
  FROM
	[db].sales s
  JOIN
    [db].outlets o  
  ON s.outlet_fkid=o.outlet_id
  JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid   
  WHERE
    os.time_open BETWEEN @startDate AND @endDate
	AND o.admin_fkid = @adminId 
	[where]
  GROUP BY status`

	whereQuery := ""
	if len(param.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(param.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": param.StartDate,
		"endDate":   param.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": param.Outlet,
		"shiftIds":  param.Shift,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSummaryItem(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	sql := `SELECT
	SUM(sd.discount - COALESCE((
		SELECT
		  SUM(discount) AS total
		FROM
		  pos_staging_db.sales_void
		WHERE
		  sales_detail_fkid=sd.sales_detail_id), 0)) AS discount
  FROM
	pos_staging_db.sales_detail sd
  JOIN
	pos_staging_db.sales s
  ON
	s.sales_id=sd.sales_fkid
  JOIN
	pos_staging_db.outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid   
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open BETWEEN @startDate AND  @endDate
	[where] `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return s.Query(sql, params...).Map()
}

func (s salesRepository) FetchSummaryPromo(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	sql := `SELECT
	SUM(sp.promotion_value) as total
  FROM
	[db].sales_promotion sp
  JOIN
	[db].sales s
  ON
	s.sales_id = sp.sales_fkid
  JOIN
	[db].outlets o
  ON
	o.outlet_id=s.outlet_fkid
  JOIN
	[db].open_shift os
  ON
	os.open_shift_id=s.open_shift_fkid
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open between @startDate and @endDate 
	[where]`
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return s.Query(sql, params...).Map()
}

func (s salesRepository) FetchSummaryPromoItem(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	sql := `WITH promo_item AS (
		SELECT
			SUM(sdp.promotion_value) as total
		FROM
			[db].sales_detail_promotion sdp
		JOIN
			[db].sales s
		ON
			s.sales_id = sdp.sales_fkid
		JOIN
			[db].outlets o
		ON
			o.outlet_id=s.outlet_fkid
		JOIN
			[db].open_shift os
		ON
			os.open_shift_id=s.open_shift_fkid
		WHERE
			s.status = 'Success'
			AND o.admin_fkid = @adminId 
			AND os.time_open between @startDate and @endDate 
			[where]
		GROUP BY
			sales_fkid
	)
	SELECT
	SUM(total) AS total
	FROM
	promo_item `
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return s.Query(sql, params...).Map()
}

func (s salesRepository) FetchSummaryGratuity(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `SELECT
	SUM(total) AS total,
	category
  FROM
	[db].sales_tax st
  JOIN
	[db].sales s
  ON
	s.sales_id=st.sales_fkid
  JOIN
	[db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid  
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open between @startDate and @endDate
	[where]
  GROUP BY
	st.category `
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSummaryPayment(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `SELECT
	sp.method,
	SUM(sp.total) AS total,
	spb.bank_fkid
  FROM
	[db].sales_payment sp
  JOIN
	[db].sales s
  ON
	s.sales_id=sp.sales_fkid
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid  
  JOIN
	[db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
  LEFT JOIN
	[db].sales_payment_bank spb
  ON
	spb.sales_payment_fkid=sp.payment_id
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open between @startDate and @endDate 
	[where]
  GROUP BY
	sp.method,
	spb.bank_fkid `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in UNNEST(@shiftIds) "
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchBank(user domain.User) ([]map[string]interface{}, error) {
	return nil, nil
}

func (s salesRepository) FetchSalesDetailDiscount(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `select sum(cast(sdd.total as numeric)) as total, sdd.type, product_detail_fkid
	from [db].sales_detail_discount sdd 
	join [db].sales_detail sd on sd.sales_detail_id=sdd.sales_detail_fkid
	join [db].sales s on s.sales_id=sd.sales_fkid
	join [db].open_shift os on os.open_shift_id=s.open_shift_fkid
	join [db].outlets o on s.outlet_fkid=o.outlet_id
	where s.status = 'Success'
	and o.admin_fkid =  @adminId
	and os.time_open between  @startDate and @endDate
	[where]
	group by sdd.type, sd.product_detail_fkid
	order by sd.product_detail_fkid `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchDiscountSalesDetail(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := ``

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSalesPromotion(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `WITH
	sd AS (
	SELECT
	  sd.sales_detail_id,
	  sd.product_detail_fkid,
	  sd.discount,
	  s.sales_id,
	  s.outlet_fkid,
	  sd.time_created,
	  s.customer_name,
	  s.employee_fkid AS sales_employee_fkid,
	  (
	  SELECT
		AS STRUCT SUM(discount) discount,
		SUM(qty) qty
	  FROM
		[db].sales_void
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) sv,
	  (
	  SELECT
		AS STRUCT SUM(promotion_value) promo_value,
		ARRAY_AGG(promotion_fkid) promo_id
	  FROM
		[db].sales_detail_promotion
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) AS sdp,
	  (
	  SELECT
		AS STRUCT SUM(
		IF
		  (type='promotion', total, 0)) promo,
		SUM(
		IF
		  (type='discount', total, 0)) discount,
		SUM(
		IF
		  (type='voucher', total, 0)) voucher,
	  FROM
		[db].sales_detail_discount
	  WHERE
		type='promotion'
		AND sales_detail_fkid=sd.sales_detail_id) sdd,
	  (
	  SELECT
		AS STRUCT ARRAY_AGG(COALESCE(voucher_code, '')) voucher_code,
		ARRAY_AGG(promotion_fkid) promo_id
	  FROM
		[db].sales_promotion
	  WHERE
		sales_fkid = s.sales_id) sp,
	  (
	  SELECT
		AS STRUCT SUM(
		IF
		  (category = 'tax', total, 0)) tax,
		SUM(
		IF
		  (category = 'service', total, 0)) service,
		SUM(
		IF
		  (category = 'discount', total, 0)) discount,
		SUM(
		IF
		  (category = 'voucher', total, 0)) voucher
	  FROM
		[db].sales_detail_tax
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) tx,
	  sd.employee_fkid,
	  sd.price,
	  sd.qty,
	FROM
	  [db].sales_detail sd
	JOIN
	  [db].sales s
	ON
	  s.sales_id=sd.sales_fkid
	JOIN
	  [db].outlets o
	ON
	  s.outlet_fkid=o.outlet_id
	WHERE
	  s.status = 'Success'
	  AND s.time_created BETWEEN @startDate and @endDate 
		[where] )
  SELECT
	sd.sales_id,
	sd.sales_detail_id,
	sd.product_detail_fkid,
	sd.sales_employee_fkid,
	COALESCE(sd.employee_fkid, sd.sales_employee_fkid) AS employee_fkid,
	sd.price,
	(sd.qty - COALESCE(sd.sv.qty, 0)) AS qty,
	(sd.discount - COALESCE(sd.sv.discount, 0) + sd.tx.discount + sd.sdd.discount) AS discount,
	sd.tx.tax,
	sd.tx.service,
	(sd.tx.voucher + sd.sdd.voucher) AS voucher,
	(COALESCE(sd.sdp.promo_value, 0) + COALESCE(sd.sdd.promo, 0)) AS total_promo,
	COALESCE(sd.sp.promo_id, sd.sdp.promo_id) AS promo_fkid,
	sd.outlet_fkid,
	sd.time_created,
  FROM
	sd
  WHERE
	(COALESCE(sd.sdp.promo_value, 0) + COALESCE(sd.sdd.promo, 0)) > 0
  ORDER BY
	sd.sales_detail_id
	LIMIT 100 `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSalesAnalysisPromotionByQty(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	sql := `
select any_value(o.outlet_id) as outlet_id, string_agg(distinct sp.voucher_code) as voucher_code_list, 
COUNT(DISTINCT s.sales_id) as total, ANY_VALUE(sp.promotion_fkid) AS promotion_fkid
 from [db].sales_promotion sp
join [db].sales s on s.sales_id=sp.sales_fkid
join [db].outlets o on o.outlet_id=s.outlet_fkid
where o.admin_fkid = @adminId
and s.status='Success'
and s.time_created between @startDate and @endDate
[where]
group by sp.promotion_fkid, s.outlet_fkid 
order by total, sp.promotion_fkid desc`

	// new query
	sql = `SELECT COALESCE(any_value(sp.promotion_fkid), any_value(sdp.promotion_fkid)) as promotion_fkid,s.outlet_fkid as outlet_id,
string_agg(DISTINCT sp.voucher_code) as voucher_code_list, COUNT(DISTINCT s.sales_id) as total
	from [db].sales s 
	join [db].outlets o on o.outlet_id=s.outlet_fkid
	left join [db].sales_promotion sp on sp.sales_fkid =s.sales_id 
	left join [db].sales_detail_promotion sdp on sdp.sales_fkid =s.sales_id 
	where o.admin_fkid=@adminId
	and s.status='Success'
	and s.time_created  BETWEEN @startDate and @endDate
	and (sp.sales_promotion_id is not null or sdp.sales_detail_promotion_id  is not null)
	[where]
	group by COALESCE(sp.promotion_fkid, sdp.promotion_fkid), s.outlet_fkid
	order by promotion_fkid desc`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSalesAnalysisPromotionByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	sql := `select sp.promotion_fkid, s.outlet_fkid as outlet_id, 
string_agg(distinct sp.voucher_code) as voucher_code_list, 
sum(sp.promotion_value) as total
from [db].sales_promotion sp
join [db].sales s on s.sales_id=sp.sales_fkid
join [db].outlets o on o.outlet_id=s.outlet_fkid                  
where o.admin_fkid=@adminId
and s.status='Success'
and s.time_created between @startDate and @endDate
[where]
group by sp.promotion_fkid, s.outlet_fkid
order by sp.promotion_fkid desc`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

func (s salesRepository) FetchSalesAnalysisPromotionDetailByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	sql := `SELECT sdp.promotion_fkid as promotion_fkid, s.outlet_fkid as outlet_id,
	sum(sdp.promotion_value) as total
	from [db].sales s 
	join [db].outlets o on o.outlet_id=s.outlet_fkid
	join [db].sales_detail_promotion sdp on sdp.sales_fkid =s.sales_id 
	where o.admin_fkid=@adminId
	and s.status='Success'
	and s.time_created  BETWEEN  @startDate and @endDate
	[where]
	group by sdp.promotion_fkid, s.outlet_fkid
	order by promotion_fkid desc`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in UNNEST(@outletIds)"
	}

	sql = strings.Replace(sql, "[db].", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return s.Query(sql, params...).MapArray()
}

// FetchSalesReportByProductSubtotal implements domain.SalesRepository.
func (s *salesRepository) FetchSalesReportByProductSubtotal(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesReportByProductVoid implements domain.SalesRepository.
func (s *salesRepository) FetchSalesReportByProductVoid(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesReportByProductDiscount implements domain.SalesRepository.
func (s *salesRepository) FetchSalesReportByProductDiscount(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesReportByProductPromotion implements domain.SalesRepository.
func (s *salesRepository) FetchSalesReportByProductPromotion(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

// FetchSalesReportByProductTax implements domain.SalesRepository.
func (s *salesRepository) FetchSalesReportByProductTax(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	panic("unimplemented")
}
