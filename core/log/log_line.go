package log

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"
)

type logLine struct {
	logs    strings.Builder
	startAt time.Time
}

func Line() *logLine {
	return &logLine{startAt: time.Now()}
}

func (l *logLine) AddLog(message string, args ...interface{}) {
	_, _, line, _ := runtime.Caller(1)

	l.logs.WriteString(fmt.Sprintf("[%v] %s\n", line, fmt.Sprintf(message, args...)))
}

func (l *logLine) String() string {
	return l.logs.String()
}

func (l *logLine) Print() {
	_, file, _, _ := runtime.Caller(1)
	fmt.Printf(">>>>>%s\n%s\n%v\n>>>>>\n", file, l.logs.String(), time.Since(l.startAt))
	l.logs.Reset()
}

func (l *logLine) PrintIfError(err error) bool {
	if IfError(err) {
		_, file, _, _ := runtime.Caller(1)
		fmt.Printf(">>>>>%s\n%s\n%v\n>>>>>\n", file, l.logs.String(), time.Since(l.startAt))
		l.logs.Reset()
	}
	return err != nil
}

func (l *logLine) Reset() {
	l.logs.Reset()
}

func (l *logLine) PrintOnLocal() {
	if os.Getenv("ENV") == "localhost" {
		l.Print()
	}
}

func (l *logLine) PrintDebug() {
	_, file, _, _ := runtime.Caller(1)
	Debug(fmt.Sprintf("###### %v\n%v\n#######", file, l.String()))
}
