package v1

import (
	"encoding/json"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strings"
)

func sqlSalesByCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) string {
	rawSql := ""
	//jsonPost := ctx.PostBody()
	//post, err := djson.DecodeObject(jsonPost)
	//utils.CheckErr(err)
	//
	////get value
	//adminId := post["admin_id"]
	//timeStart := cast.ToInt64(post["time_start"])
	//timeEnd := cast.ToInt64(post["time_end"])
	//outletIds := cast.ToSlice(post["outlet_ids"])

	//create where clause outlet_id IN (...)
	outletId := ""
	whereInClauseItemVoid := ""
	whereInClauseItemSold := ""
	if outletIds != nil {
		outletId = strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInClauseItemVoid = " AND ssv.outlet_fkid IN ( " + outletId + " )"
		whereInClauseItemSold = " AND s.outlet_fkid IN (" + outletId + ")"
	}

	queryTableItemVoid := db.GetSQLRaw(""+
		"SELECT "+
		"IF(pdsv.variant_fkid IS NULL, pdsv.product_fkid, concat(pdsv.product_fkid,'-',pdsv.variant_fkid)) AS item_id,"+
		"sum(sv.qty) AS qty,"+
		"sum(sv.price) AS price,"+
		"sum((sv.qty * sv.price) + sv.discount) AS sub_total_void "+
		"FROM sales_void sv "+
		"LEFT JOIN products_detail pdsv ON sv.product_detail_fkid = pdsv.product_detail_id "+
		"LEFT JOIN sales ssv ON sv.sales_fkid = ssv.sales_id "+
		"WHERE "+
		"sv.time_created >= ? AND "+
		"sv.time_created <= ? AND "+
		"ssv.status = 'success' AND "+
		"ssv.data_status = 'on' AND "+
		"ssv.payment NOT IN ('COMPLIMENT','DUTY MEALS') "+whereInClauseItemVoid+" "+
		"GROUP BY pdsv.product_fkid, pdsv.variant_fkid", timeStart, timeEnd)

	queryTableItemSold := db.GetSQLRaw("SELECT "+
		"IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) AS item_id,"+
		"IF(pd.variant_fkid IS NULL, p.name, concat(p.name,' (',v.variant_name,')')) AS item_name,"+
		"pt.products_type_id AS type_id,"+
		"pt.name AS type_name,"+
		"pc.product_category_id AS category_id,"+
		"pc.name AS category_name,"+
		"pcs.product_subcategory_id AS subcategory_id,"+
		"pcs.name AS subcategory_name,"+
		"sum(sd.qty) AS qty,"+
		"sum((sd.qty * sd.price) - sd.discount) AS sub_total_sold,"+
		"IFNULL(jsv.qty,0) AS qty_void,"+
		"IFNULL(jsv.sub_total_void,0) AS sub_total_void,"+
		"sum(sd.qty) - IFNULL(jsv.qty,0) AS qty_net,"+
		"sum((sd.qty * sd.price) - sd.discount) + IFNULL(jsv.sub_total_void,0) AS sub_total_net "+
		"FROM sales_detail sd "+
		"LEFT JOIN sales s ON s.sales_id = sd.sales_fkid "+
		"LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id "+
		"LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id "+
		"LEFT JOIN products p ON p.product_id = pd.product_fkid "+
		"LEFT JOIN products_detail_variant v ON pd.variant_fkid = v.variant_id "+
		"LEFT JOIN products_type pt ON pt.products_type_id = p.product_type_fkid "+
		"LEFT JOIN products_category pc ON pc.product_category_id = p.product_category_fkid "+
		"LEFT JOIN products_subcategory pcs ON pcs.product_subcategory_id = p.product_subcategory_fkid "+
		"LEFT JOIN ("+queryTableItemVoid+") jsv ON jsv.item_id = IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) "+
		"WHERE "+
		"o.admin_fkid=? AND "+
		"s.time_created >= ? AND s.time_created <= ? AND "+
		"s.status='success' AND "+
		"s.data_status='on' AND "+
		"s.payment NOT IN ('COMPLIMENT','DUTY MEALS') "+whereInClauseItemSold+
		"GROUP BY pd.product_fkid, pd.variant_fkid, jsv.sub_total_void,jsv.qty", adminId, timeStart, timeEnd)

	rawSql = queryTableItemSold
	return rawSql
}

func SalesByAllCategory(ctx *fasthttp.RequestCtx) {
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	outletIds := cast.ToSlice(post["outlet_ids"])

	rawSqlSalesByCategory := sqlSalesByCategory(adminId, timeStart, timeEnd, outletIds)
	chanSalesByType := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT type_id AS id, type_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByType <- query
	}()

	chanSalesByCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT category_id AS id, category_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByCategory <- query
	}()

	chanSalesBySubCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT subcategory_id AS id, subcategory_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesBySubCategory <- query
	}()

	dataSalesType := <-chanSalesByType
	dataSalesCategory := <-chanSalesByCategory
	dataSalesSubCategory := <-chanSalesBySubCategory

	response := map[string]interface{}{
		"type":        dataSalesType,
		"category":    dataSalesCategory,
		"subcategory": dataSalesSubCategory,
	}

	//output
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}

func SalesByAllCategory_map(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) map[string]interface{} {
	rawSqlSalesByCategory := sqlSalesByCategory(adminId, timeStart, timeEnd, outletIds)
	chanSalesByType := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT type_id AS id, type_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByType <- query
	}()

	chanSalesByCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT category_id AS id, category_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByCategory <- query
	}()

	chanSalesBySubCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT subcategory_id AS id, subcategory_name AS name, sum(sub_total_net) AS qty " +
			"FROM (" + rawSqlSalesByCategory + ") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesBySubCategory <- query
	}()

	dataSalesType := <-chanSalesByType
	dataSalesCategory := <-chanSalesByCategory
	dataSalesSubCategory := <-chanSalesBySubCategory

	response := map[string]interface{}{
		"type":        dataSalesType,
		"category":    dataSalesCategory,
		"subcategory": dataSalesSubCategory,
	}

	return response
}

func SalesByAllCategory_HTTP(ctx *fasthttp.RequestCtx) {
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	outletIds := cast.ToSlice(post["outlet_ids"])

	response := SalesByAllCategory_map(adminId, timeStart, timeEnd, outletIds)

	//output
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}
