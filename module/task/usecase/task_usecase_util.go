package usecase

import (
	"math"
	"sort"
)

func calculateAverage(numbers []float64) float64 {
	total := float64(0)
	for _, number := range numbers {
		total += number
	}
	return (total) / float64(len(numbers))
}

func calculateMedian(numbers []float64) float64 {
	sort.Slice(numbers, func(i, j int) bool {
		return numbers[i] < numbers[j]
	})

	n := len(numbers)
	mid := n / 2

	if n%2 == 0 {
		return float64(numbers[mid-1]+numbers[mid]) / 2.0
	}

	return float64(numbers[mid])
}

func calculateStandardDeviation(numbers []float64, mean float64) float64 {
	var sumSquares float64

	for _, number := range numbers {
		deviation := float64(number) - mean
		sumSquares += deviation * deviation
	}

	variance := sumSquares / float64(len(numbers))
	return math.Sqrt(variance)
}

func calculateAverageInDays(numbers []int64) float64 {
	total := int64(0)
	for _, number := range numbers {
		total += number
	}
	return (float64(total) / (float64(len(numbers)))) * 24 * 60 * 60 // Convert seconds to days
}

func calculateMedianInDays(numbers []float64) float64 {
	medianInSeconds := calculateMedian(numbers)
	return medianInSeconds / (24 * 60 * 60) // Convert seconds to days
}

func calculateStandardDeviationInDays(numbers []float64, mean float64) float64 {
	stdDevInSeconds := calculateStandardDeviation(numbers, mean)
	return stdDevInSeconds / (24 * 60 * 60) // Convert seconds to days
}
