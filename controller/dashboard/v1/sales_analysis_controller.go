package v1

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"reflect"
	"strings"

	"github.com/a8m/djson"
	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gopkg.in/go-playground/validator.v9"
	en_translations "gopkg.in/go-playground/validator.v9/translations/en"
)

type ValidationDashboardRequest struct {
	TimeStart string   `json:"time_start,omitempty" validate:"required,gte=0" label:"Time Start"`
	TimeEnd   string   `json:"time_end,omitempty" validate:"required,gte=0" label:"Time End"`
	TimeDiff  string   `json:"time_diff,omitempty" validate:"required,gte=0" label:"Time Diff"`
	OutletIds []string `json:"outlet_ids,omitempty" validate:"" label:"Outlets"`
	Format    string   `json:"format,omitempty"`
	//AdminID   string   `json:"admin_id"`
}

func SalesAnalysis_v1(ctx *fasthttp.RequestCtx) {
	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
	} else {
		jsonPost := ctx.PostBody()
		post, err := djson.DecodeObject(jsonPost)
		utils.CheckErr(err)

		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(post["time_start"])
		timeEnd := cast.ToInt64(post["time_end"])
		timeDiff := cast.ToInt64(post["time_diff"]) //timezone
		outletIdsParam := cast.ToString(post["outlet_ids"])
		format := cast.ToString(post["format"])

		outletIds := make([]interface{}, 0)
		if outletIdsParam == "" {
			outletIdsParam = outletAccess
		}

		oa := strings.Split(outletIdsParam, ",")
		for _, v := range oa {
			outletIds = append(outletIds, v)
		}

		//set legend
		timeStartTime := utils.MillisToTime(timeStart + timeDiff)
		timeEndTime := utils.MillisToTime(timeEnd + timeDiff)
		timeStartPreviousTime := utils.MillisToTime(timeStart + timeDiff - (timeEnd - timeStart) - 1)
		legendNowText := ""
		legendPreviousText := ""
		if timeStartTime.Format("02-01-2006") == timeEndTime.Format("02-01-2006") {
			legendNowText = timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		} else {
			legendNowText = timeStartTime.Format("02-01-2006") + " to " + timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartPreviousTime.Format("02-01-2006") + " to " + timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		var response map[string]interface{}
		if strings.TrimSpace(format) != "" {
			fmt.Println("dashboard analysis format: ", format)
			response = SalesAnalysis_mapByPeriod(adminId, timeStart, timeEnd, timeDiff, outletIds, format)
		} else {
			response = SalesAnalysis_map(adminId, timeStart, timeEnd, timeDiff, outletIds)
		}
		// response = SalesAnalysis_map(adminId, timeStart, timeEnd, timeDiff, outletIds)

		response["legend"] = map[string]interface{}{
			"now":  legendNowText,
			"prev": legendPreviousText,
		}

		//output
		ctx.SetStatusCode(http.StatusOK)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: true,
			Code:   200,
			Data:   response,
		})
	}

}

func MyValidation(dataStruct interface{}) map[string]interface{} {
	//translation
	translator := en.New()
	uni := ut.New(translator, translator)

	// this is usually known or extracted from http 'Accept-Language' header
	// also see uni.FindTranslator(...)
	trans, found := uni.GetTranslator("en")
	if !found {
		log.Fatal("translator not found")
	}

	v := validator.New()
	if err := en_translations.RegisterDefaultTranslations(v, trans); err != nil {
		log.Fatal(err)
	}

	// register function to get tag name from json tags.
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	err := v.Struct(dataStruct)
	errList := map[string]interface{}{}
	if err != nil {
		for _, e := range err.(validator.ValidationErrors) {
			//fmt.Println(e.Field())
			errTxt := e.Translate(trans)

			key := e.Field()
			errList[key] = errTxt
		}
	}
	//fmt.Println(utils.SimplyToJson(errList)) //check error list of form
	return errList
}
