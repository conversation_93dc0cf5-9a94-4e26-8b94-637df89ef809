package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	repoSales "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type salesUseCase struct {
	repoPrimary    domain.SalesRepositoryPrimary
	repoReplica    domain.SalesRepositoryReplica
	repoProduct    domain.ProductRepository
	repoMiddleware domain.SalesRepositoryMiddleware
}

func NewSalesUseCase(repoPrimary domain.SalesRepositoryPrimary, repoReplica domain.SalesRepositoryReplica, repoProduct domain.ProductRepository, repoMiddleware domain.SalesRepositoryMiddleware) domain.SalesUseCase {
	return &salesUseCase{repoPrimary: repoPrimary, repoReplica: repoReplica, repoProduct: repoProduct, repoMiddleware: repoMiddleware}
}

func (s *salesUseCase) pickRepo(endDate int64) domain.SalesRepository {
	var repo domain.SalesRepository
	if endDate < repoSales.GetMaxTimeSync() {
		repo = s.repoReplica
	} else {
		repo = s.repoPrimary
	}
	return repo
}

func (s *salesUseCase) FetchTransferAnalysis(request domain.DataTableRequest, dataType string, user domain.User) (map[string]interface{}, error) {
	if request.TimeZone == 0 {
		request.TimeZone = 25200
	}

	log.Info("fetch transfer analysis: %s", cast.ToStringJson(request))

	//fetching qty in transfer
	transferOut, err := s.repoPrimary.FetchTransferOutByQty(request, cast.ToInt(user.BusinessId))
	if err != nil {
		return nil, err
	}
	log.Info("total transfer out : %d", len(transferOut))

	//fetching qty out transfer
	transferIn, err := s.repoPrimary.FetchTransferInByQty(request, cast.ToInt(user.BusinessId))
	if err != nil {
		return nil, err
	}
	log.Info("total transfer in : %d", len(transferIn))

	if len(transferOut) == 0 && len(transferIn) == 0 {
		return map[string]interface{}{"data": []interface{}{}}, nil
	}

	///--- fetching outlet data
	outletIdMap := make(map[string]interface{})
	//if user specify the outlet, make sure its returned
	for _, id := range request.Outlet {
		outletIdMap[cast.ToString(id)] = 0
	}
	for _, row := range transferOut {
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
	}
	for _, row := range transferIn {
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
	}

	outletIds := make([]interface{}, 0)
	outletIds = append(outletIds, request.OutletOrigin)
	outletIds = append(outletIds, array.FromMapKey(outletIdMap)...)

	outlets, err := s.repoPrimary.FetchOutletByIds(outletIds...)
	outletsMap := make(map[string]map[string]interface{})
	for _, row := range outlets {
		outletsMap[cast.ToString(row["outlet_id"])] = row
	}

	total := float32(0)
	totalByOutlet := array.Copy(outletIdMap)
	totalTransferGroup := make(map[string]map[string]float32)
	productsMap := make(map[string]map[string]interface{})
	for i, row := range transferIn {
		total += cast.ToFloat32(row["total"])

		//key := fmt.Sprintf("%v#%v", row["outlet_fkid"], row["product_detail_fkid"])
		if totalTransferGroup[cast.ToString(row["product_detail_fkid"])] == nil {
			totalTransferGroup[cast.ToString(row["product_detail_fkid"])] = make(map[string]float32)
		}
		totalTransferGroup[cast.ToString(row["product_detail_fkid"])][cast.ToString(row["outlet_fkid"])] = total
		totalByOutlet[cast.ToString(row["outlet_fkid"])] = cast.ToFloat32(row["total"]) + cast.ToFloat32(totalByOutlet[cast.ToString(row["outlet_fkid"])])

		if i+1 == len(transferIn) || row["product_detail_fkid"] != transferIn[i+1]["product_detail_fkid"] {
			outletGroup := make([]map[string]interface{}, 0)

			//transform outlet_id to name
			for k, v := range totalByOutlet {
				if outlet, ok := outletsMap[k]; ok {
					outletGroup = append(outletGroup, map[string]interface{}{
						"name":   cast.ToString(outlet["name"]),
						dataType: v,
					})
					//delete(totalByOutlet, k)
				}

			}

			//totalByOutlet["outlet"] = outletGroup
			//totalByOutlet["total"] = total
			//totalByOutlet["product_name"] = row["product_name"]
			//totalByOutlet["category"] = row["category"]
			//totalByOutlet["subcategory"] = row["subcategory"]
			//data = append(data, totalByOutlet)
			//totalByOutlet = array.Copy(outletIdMap)

			productsMap[cast.ToString(row["product_detail_fkid"])] = map[string]interface{}{
				"product_name": row["product_name"],
				"category":     row["category"],
				"subcategory":  row["subcategory"],
			}
			total = 0
		}
	}

	fmt.Println(">>> ", cast.ToStringJson(totalTransferGroup))

	outletOriginName := outletsMap[cast.ToString(request.OutletOrigin)]["name"]
	data := make([]map[string]interface{}, 0)
	totalByOutlet = array.Copy(outletIdMap)
	total = float32(0)
	for i, row := range transferOut {
		total += cast.ToFloat32(row["total"])
		totalByOutlet[cast.ToString(row["outlet_fkid"])] = cast.ToFloat32(row["total"]) + cast.ToFloat32(totalByOutlet[cast.ToString(row["outlet_fkid"])])

		if i+1 == len(transferOut) || row["product_detail_fkid"] != transferOut[i+1]["product_detail_fkid"] {
			outletGroup := make([]map[string]interface{}, 0)

			//transform outlet_id to name
			for k, v := range totalByOutlet {
				if outlet, ok := outletsMap[k]; ok {
					in := cast.ToFloat32(totalTransferGroup[cast.ToString(row["product_detail_fkid"])][k])

					outletGroup = append(outletGroup, map[string]interface{}{
						"name":   cast.ToString(outlet["name"]),
						dataType: in - cast.ToFloat32(v),
					})

					delete(totalTransferGroup, cast.ToString(row["product_detail_fkid"]))
					delete(totalByOutlet, k)
				}
			}

			totalByOutlet["outlet"] = outletGroup
			totalByOutlet["total"] = total * -1
			totalByOutlet["product_name"] = row["product_name"]
			totalByOutlet["category"] = row["category"]
			totalByOutlet["subcategory"] = row["subcategory"]
			totalByOutlet["outlet_origin"] = outletOriginName
			data = append(data, totalByOutlet)

			totalByOutlet = array.Copy(outletIdMap)
			total = 0
		}
	}

	fmt.Println("total transfer group remain :", len(totalTransferGroup))
	fmt.Println("data :", totalTransferGroup)

	totalByOutlet = array.Copy(outletIdMap)
	for prodId, v := range totalTransferGroup {
		total = 0
		for outletId, qty := range v {
			totalByOutlet[outletId] = qty
			total += qty
		}

		outletGroup := make([]map[string]interface{}, 0)

		//transform outlet_id to name
		for k, v := range totalByOutlet {
			if outlet, ok := outletsMap[k]; ok {
				outletGroup = append(outletGroup, map[string]interface{}{
					"name":   cast.ToString(outlet["name"]),
					dataType: v,
				})
				delete(totalByOutlet, k)
			}
		}

		product := productsMap[prodId]
		totalByOutlet["outlet"] = outletGroup
		totalByOutlet["total"] = total
		totalByOutlet["product_name"] = product["product_name"]
		totalByOutlet["category"] = product["category"]
		totalByOutlet["subcategory"] = product["subcategory"]
		totalByOutlet["outlet_origin"] = outletOriginName
		data = append(data, totalByOutlet)
		totalByOutlet = array.Copy(outletIdMap)
	}

	fmt.Println("result --> ", cast.ToStringJson(data))
	return map[string]interface{}{"data": data}, nil
}
