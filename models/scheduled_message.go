package models

import (
	"reflect"
	"time"
)

type ScheduledMessageEntity struct {
	ID                 int64  `json:"id"`
	Title              string `json:"title"`
	Message            string `json:"message"`
	TimeDeliver        int64  `json:"time_deliver"`
	TimeSent           int64  `json:"time_sent"`
	DataCreated        int64  `json:"data_created"`
	Media              string `json:"media"`
	Receiver           string `json:"receiver"`
	Status             string `json:"status"`
	AdminFKID          int    `json:"admin_fkid"`
	IdentifierID       string `json:"identifier_id"`
	SentVia            string `json:"sent_via"`
	Attachments        string `json:"attachments"`         // Assuming attachments are stored as JSON string
	NotificationDetail string `json:"notification_detail"` // Assuming notification_detail is stored as JSON string
}

func (sm *ScheduledMessageEntity) ToMap() map[string]interface{} {
	result := make(map[string]interface{})
	val := reflect.ValueOf(sm).Elem() // Get the reflect.Value of the struct

	if sm.TimeDeliver == 0 {
		sm.TimeDeliver = time.Now().Unix() * 1000
	}
	if sm.DataCreated == 0 {
		sm.DataCreated = time.Now().Unix() * 1000
	}

	for i := 0; i < val.NumField(); i++ {
		field := val.Type().Field(i)      // Get the reflect.Type of the field
		value := val.Field(i).Interface() // Get the value of the field

		// Check if the value is not empty
		if value != reflect.Zero(val.Field(i).Type()).Interface() {
			result[field.Tag.Get("json")] = value // Add field value to the map
		}
	}

	return result
}
