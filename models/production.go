package models

// enum('ingridient','ingredient','endproduct','residual')
const (
	ProductionTypeIngredient = "ingredient"
	ProductionTypeEndProduct = "endproduct"
	ProductionTypeResidual   = "residual"
)

type ProductionEntity struct {
	ProductionID      int64  `json:"production_id"`
	ItemBreakdownFKID int64  `json:"itembreakdown_fkid"`
	ProductDetailFKID int64  `json:"product_detail_fkid"`
	OutletFKID        int64  `json:"outlet_fkid"`
	QtyRecipe         int    `json:"qty_recipe"`
	QtyPrimary        int    `json:"qty_primary"`
	EmployeeFKID      int64  `json:"employee_fkid"`
	AdminFKID         int64  `json:"admin_fkid"`
	DateInput         string `json:"date_input"`
	DataCreated       int64  `json:"data_created"`
	DataModified      int64  `json:"data_modified"`
	DataDeleteAt      string `json:"data_delete_at"`
	Transferred       int    `json:"transferred"`
}

type ProductionDetailEntity struct {
	ProductionDetailID int64   `json:"productiondetail_id,omitempty"`
	ProductionFKID     int64   `json:"production_fkid,omitempty"`
	ProductFKID        int64   `json:"product_fkid,omitempty"`
	ProductDetailFKID  int64   `json:"product_detail_fkid,omitempty"`
	Qty                float32 `json:"qty,omitempty"`
	PriceBuy           float32 `json:"price_buy,omitempty"`
	DetailType         string  `json:"detail_type,omitempty"`
	DataCreated        int64   `json:"data_created,omitempty"`
	DataModified       int64   `json:"data_modified,omitempty"`
}

type ProductionCostEntity struct {
	ID                         int64 `json:"id"`
	ProductionFKID             int64 `json:"production_fkid"`
	PurchaseReportCategoryFKID int64 `json:"purchase_report_category_fkid"`
	Nominal                    int   `json:"nominal"`
}
