# Grand Total Calculation Example

## Formula
```
grand_total = sub_total - discount - voucher - promotion
average = grand_total / qty
```

## Example Calculation

### Input Data:
- **sub_total**: 200,000
- **qty**: 8
- **discount**: 10,000
- **voucher**: 5,000
- **promotion**: 3,000

### Calculation:
```
grand_total = 200,000 - 10,000 - 5,000 - 3,000 = 182,000
average = 182,000 / 8 = 22,750
```

### Expected JSON Response:
```json
{
  "data": [
    {
      "sub_total": 200000,
      "product_name": "Sample Product",
      "sku": "SKU001",
      "qty": 8,
      "category": "Sample Category",
      "avg": 22750,
      "discount": 10000,
      "voucher": 5000,
      "promotion": 3000,
      "grand_total": 182000
    }
  ]
}
```

## Notes:
- The `grand_total` represents the final amount after all deductions
- The `avg` (average) is calculated using `grand_total / qty` instead of `sub_total / qty`
- All void transactions are already subtracted from the base values before this calculation
