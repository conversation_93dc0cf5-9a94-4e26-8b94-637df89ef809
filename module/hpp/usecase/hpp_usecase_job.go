package usecase

import (
	"fmt"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
)

// CheckAnomalyHpp implements hpp.UseCase.
func (h *hppUseCase) CheckAnomalyHpp() {
	// check if stock_out more than stock_in
	overStockOut, err := h.repo.FetchAnomalyOverStockOut()
	log.IfError(err)

	//check if stock_out in stock_price not equal with total stock_out in stock_price_detail
	missmatchStockOut, err := h.repo.FetchAnomalyMismatchStockOut()
	log.IfError(err)

	if overStockOut.Count == 0 && len(*missmatchStockOut) == 0 {
		log.Info("--- no anomaly hpp found!")
		return
	}

	msg := `
		# Anomaly HPP Found #

		[Over Stock Out] 
		%v

		[Miss Match Stock Out]
		Total: %v
		%v
	`

	log.IfError(fmt.Errorf(msg, utils.Simply<PERSON><PERSON><PERSON><PERSON>(overStockOut), len(*missmatchStockOut), utils.TakeMax(utils.SimplyToJson((*missmatchStockOut)), 5)))
}
