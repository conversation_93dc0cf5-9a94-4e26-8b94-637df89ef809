package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"cloud.google.com/go/profiler"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	googleSdk "gitlab.com/uniqdev/backend/api-report/core/google"
	log2 "gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/jobs"

	// crm
	"gitlab.com/uniqdev/backend/api-report/module/cache"
	httpCrm "gitlab.com/uniqdev/backend/api-report/module/crm/delivery/http"
	bigqueryCrm "gitlab.com/uniqdev/backend/api-report/module/crm/repository/bigquery"
	mysqlCrm "gitlab.com/uniqdev/backend/api-report/module/crm/repository/mysql"
	usecaseCrm "gitlab.com/uniqdev/backend/api-report/module/crm/usecase"

	// dashboard
	httpDashboard "gitlab.com/uniqdev/backend/api-report/module/dashboard/delivery/http"
	repositoryDashboard "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
	usecaseDashboard "gitlab.com/uniqdev/backend/api-report/module/dashboard/usecase"

	// hpp
	httpHpp "gitlab.com/uniqdev/backend/api-report/module/hpp/delivery/http"
	mysqlHpp "gitlab.com/uniqdev/backend/api-report/module/hpp/repository/mysql"
	usecaseHpp "gitlab.com/uniqdev/backend/api-report/module/hpp/usecase"

	// product
	mysqlProduct "gitlab.com/uniqdev/backend/api-report/module/product/repository/mysql"

	// purchase
	mysqlPurchase "gitlab.com/uniqdev/backend/api-report/module/purchase/repository/mysql"

	// sales
	httpSales "gitlab.com/uniqdev/backend/api-report/module/sales/delivery/http"
	repositorySales "gitlab.com/uniqdev/backend/api-report/module/sales/repository"
	bqSales "gitlab.com/uniqdev/backend/api-report/module/sales/repository/bigquery"
	mysqlSales "gitlab.com/uniqdev/backend/api-report/module/sales/repository/mysql"
	usecaseSales "gitlab.com/uniqdev/backend/api-report/module/sales/usecase"

	// stockcard
	httpStockcard "gitlab.com/uniqdev/backend/api-report/module/stock_card/delivery/http"
	repoStockcard "gitlab.com/uniqdev/backend/api-report/module/stock_card/repository"
	bqStockcard "gitlab.com/uniqdev/backend/api-report/module/stock_card/repository/bigquery"
	mysqlStockcard "gitlab.com/uniqdev/backend/api-report/module/stock_card/repository/mysql"
	usecaseStockcard "gitlab.com/uniqdev/backend/api-report/module/stock_card/usecase"

	// sync
	syncMysql "gitlab.com/uniqdev/backend/api-report/module/sync/repository/mysql"
	syncUseCase "gitlab.com/uniqdev/backend/api-report/module/sync/usecase"
	"gitlab.com/uniqdev/backend/api-report/routes"

	httpTask "gitlab.com/uniqdev/backend/api-report/module/task/delivery/http"
	taskRepo "gitlab.com/uniqdev/backend/api-report/module/task/repository/mysql"
	taskUseCase "gitlab.com/uniqdev/backend/api-report/module/task/usecase"

	httpCloseShift "gitlab.com/uniqdev/backend/api-report/module/close_shift/delivery/http"
	"gitlab.com/uniqdev/backend/api-report/module/close_shift/repository/middleware"
	usecaseCloseShift "gitlab.com/uniqdev/backend/api-report/module/close_shift/usecase"
)

func main() {
	//certPath :=  os.Getenv("ssl_cert")
	//keyPath :=  os.Getenv("ssl_key")

	cfgProfiler := googleSdk.Profiler()
	if err := profiler.Start(cfgProfiler); err != nil {
		fmt.Println(">> start profiler err: ", err)
	}

	log2.AddHook(log2.SlackHook{
		HookUrl: os.Getenv("SLACK_URL"),
		Channel: os.Getenv("SLACK_CHANNEL"),
	})

	router := routes.CreateRoutes()

	dbConn := db.GetConn()
	bqClient := googleSdk.InitBigQueryClient()
	redis := db.GetRedisClient()

	cacheImplement := cache.NewCacheDb(redis)

	productRepo := mysqlProduct.NewMysqlProductRepository(dbConn)

	salesRepo := mysqlSales.NewMysqlSalesRepository(dbConn, cacheImplement)
	salesRepoReplica := bqSales.NewBigQuerySalesRepository(bqClient)
	salesRepoMidleware := repositorySales.NewSalesRepositoryMiddleware(salesRepo, salesRepoReplica)
	salesUseCase := usecaseSales.NewSalesUseCase(salesRepo, salesRepoReplica, productRepo, salesRepoMidleware)
	httpSales.NewHttpSalesHandler(router, salesUseCase)

	dashboardMidRepo := repositoryDashboard.NewMiddlewareDashboardRepository(dbConn, bqClient, cacheImplement)
	dashboardUseCase := usecaseDashboard.NewDashboardUseCase(dashboardMidRepo, salesRepo)
	httpDashboard.NewHttpDashboardHandler(router, dashboardUseCase)

	stockcardMysqlRepo := mysqlStockcard.NewMysqlStockCardRepository(dbConn)
	stockcardBqRepo := bqStockcard.NewBigQueryStockCardRepository(bqClient)
	stockcardRepo := repoStockcard.NewStockCardRepositoryMiddleware(stockcardMysqlRepo, stockcardBqRepo)
	stockcardUsecase := usecaseStockcard.NewStockCardUseCase(stockcardRepo)
	httpStockcard.NewHttpStockCardHandler(router, stockcardUsecase)

	syncRepo := syncMysql.NewMysqlSyncRepository(dbConn, bqClient)
	syncUseCase := syncUseCase.NewSyncUseCase(syncRepo)

	purchaseRepo := mysqlPurchase.NewMysqlPurchaseRepository(dbConn)

	hppRepo := mysqlHpp.NewMysqlHppRepository(dbConn)
	hppUseCase := usecaseHpp.NewHppUseCase(hppRepo, purchaseRepo, productRepo)
	httpHpp.NewHttpHppHandler(router, hppUseCase)

	taskRepo := taskRepo.NewMysqlTaskRepository(dbConn)
	taskUsecase := taskUseCase.NewTaskUseCase(taskRepo)
	httpTask.NewHttpTaskHandler(router, taskUsecase)

	closeShiftRepo := middleware.NewMiddlewareCloseShiftRepository(dbConn, bqClient, cacheImplement) //repoCloseShift.NewMysqlCloseShiftRepository(dbConn)
	closeShiftUsecase := usecaseCloseShift.NewCloseShiftUseCase(closeShiftRepo)
	httpCloseShift.NewHttpCloseShiftHandler(router, closeShiftUsecase)

	fmt.Println("args", len(os.Args))
	if len(os.Args) > 3 && os.Args[1] == "--test" {
		fmt.Println("--testing for: ", os.Args[2])
		param := os.Args[3]
		if os.Args[2] == "retur" {
			hppUseCase.HandleNewStockInOut(domain.HppSubscription{Source: domain.PurchaseRetur, Id: "24", Qty: 7})
		} else if os.Args[2] == "transfer" {
			hppUseCase.HandleNewStockInOut(domain.HppSubscription{Source: domain.TransferConfirm, Id: "21"})
		} else if os.Args[2] == "stock-out" {
			hppUseCase.Test()
		} else if os.Args[2] == "production" {
			hppUseCase.HandleNewStockInOut(domain.HppSubscription{Source: domain.Production, Id: "160"})
		} else if os.Args[2] == "purchase" {
			hppUseCase.HandleNewStockInOut(domain.HppSubscription{Source: domain.PurchaseConfirm, Id: param})
		} else if os.Args[2] == "adjust" {
			// hppUseCase.UpdateStockPriceQtyIn("NIM65L7562995", "sales_refund", 70)
			if os.Args[3] == "all" {
				hppUseCase.FixWrongFifo()
			} else {
				hppUseCase.AdjustStockPrice(cast.ToInt(os.Args[3]), cast.ToInt64(os.Args[4]))
			}
		} else if os.Args[2] == "report" {
			salesUseCase.RunWeeklyReportGenerator()
		}
	}

	crmRepoReplica := bigqueryCrm.NewBigQueryCrmRepository(bqClient)
	crmRepoPrimary := mysqlCrm.NewMysqlCrmRepository(dbConn)
	crmUseCase := usecaseCrm.NewCrmUseCase(crmRepoPrimary, crmRepoReplica)
	httpCrm.NewHttpCrmHandler(router, crmUseCase)

	scheduler := jobs.NewScheduler(syncUseCase, hppUseCase, salesUseCase)
	scheduler.RunJob()

	//syncUseCase.SyncDataToBigQuery()
	//hppUseCase.Test()

	//fasthttp.ListenAndServeTLS(":2893", certPath, keyPath, router.Handler)
	//fasthttp.ListenAndServe(":"+app_port, router.Handler)

	fmt.Println("Fifo Enable: ", usecaseHpp.IsFifoEnabled())

	errs := make(chan error)
	defer func() {
		if r := recover(); r != nil {
			fmt.Println(">> PANIC << ")
			fmt.Println(r)
		} else if errs != nil {
			fmt.Println(errs)
		}
		fmt.Println("\n\n------- SERVICE ENDED ---------")
	}()

	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errs <- fmt.Errorf("%s", <-c)
	}()

	go func() {
		app_port := os.Getenv("PORT")
		env := os.Getenv("ENV")
		if app_port == "" {
			app_port = os.Getenv("app_port")
			if app_port == "" {
				app_port = "2893"
			}
		}

		fmt.Printf("\n\n|-----------API REPORT-----------|\n"+
			"port  : %s\n"+
			"env   : %s\n"+
			"|-----------------------------|\n\n", app_port, env)

		// errs <- fasthttp.ListenAndServe(":"+port, router.Handler)

		errs <- fasthttp.ListenAndServe(":"+app_port, auth.CORS(router.Handler))
	}()

	fmt.Printf("\n>>app exit: %v", <-errs)
}
