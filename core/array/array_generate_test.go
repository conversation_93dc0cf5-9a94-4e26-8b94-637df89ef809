package array

import (
	"reflect"
	"testing"
)

func TestRange(t *testing.T) {
	type args struct {
		start int
		end   int
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{"1-10", args{start: 1, end: 10}, []int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := Range(tt.args.start, tt.args.end); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("Range() = %v, want %v", got, tt.want)
			}
		})
	}
}
