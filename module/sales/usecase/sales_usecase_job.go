package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/wcharczuk/go-chart/v2"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gonum.org/v1/plot"
	"gonum.org/v1/plot/plotter"
	"gonum.org/v1/plot/vg"
)

type reportConfig struct {
	DateStart int64
	useCase   *salesUseCase
}

func (s *salesUseCase) RunWeeklyReportGenerator() {
	conf := reportConfig{
		DateStart: utils.GetTimestampMillis(-7),
		useCase:   s,
	}
	//fetch admin who has transaction
	adminIds, err := s.repoPrimary.FetchSalesOfAdmin(conf.DateStart)
	if log.IfError(err) {
		return
	}

	log.Info("total admin: %v, sales since: %v | %v", len(adminIds), conf.DateStart, adminIds)
	for _, id := range adminIds {
		conf.generateSalesReport(id)
	}
}

func (r *reportConfig) generateSalesReport(adminId int) error {
	//get count total outlet which has transaction
	count, err := r.useCase.repoPrimary.CountOutletsWithTransactionsSince(adminId, r.DateStart)
	if log.IfError(err) {
		return err
	}

	//for store > 3, the graph is a sum of all outlet daily
	//otherwise the line graph is per outlet
	graphPath := ""
	if count > 3 || os.Getenv("ENV") == "staging" {
		graphPath, err = r.buildGraphDaily(adminId)
		if log.IfError(err) {
			return err
		}
	} else {
		//for now skip....
		log.Info("skip as user %v has %v outlet", adminId, count)
		return nil
	}

	summaryReport, err := r.buildWeeklySummary(adminId)
	if log.IfError(err) {
		return err
	}

	fmt.Println(graphPath)
	fmt.Println(summaryReport)

	imgBase64, err := cast.ToBase64(graphPath)
	if log.IfError(err) {
		return err
	}

	admin, err := r.useCase.repoPrimary.FetchAdmin(adminId)
	log.IfError(err)

	attachments, _ := json.Marshal([]string{imgBase64})
	messages := make([]models.ScheduledMessageEntity, 0)
	messages = append(messages, models.ScheduledMessageEntity{
		Title:       "WEEKLY REPORT",
		Message:     summaryReport,
		Media:       "whatsapp",
		Receiver:    admin.Phone,
		Attachments: string(attachments),
	})

	receivers, err := r.useCase.repoPrimary.FetchReportNotification(adminId, models.NotifTypeCloseShift)
	log.IfError(err)
	for _, receiver := range receivers {
		if receiver.ReceiverType != models.ReceiverTypeWhatsApp {
			continue
		}
		messages = append(messages, models.ScheduledMessageEntity{
			Title:       "WEEKLY REPORT",
			Message:     summaryReport,
			Media:       receiver.ReceiverType,
			Receiver:    receiver.Receiver,
			Attachments: string(attachments),
		})
	}

	r.useCase.repoPrimary.AddScheduledMessage(messages...)
	return nil
}

func (r *reportConfig) buildWeeklySummary(adminId int) (string, error) {
	// Calculate start and end timestamps for the last 14 days
	endTime := time.Now().UnixNano() / int64(time.Millisecond)
	startTime := endTime - 14*24*60*60*1000 // Subtract 14 days in milliseconds

	// Fetch total transactions data for the last 14 days
	transactionsData, err := r.useCase.repoPrimary.FetchDailySalesSumByAdmin(adminId, startTime, endTime)
	if err != nil {
		return "", fmt.Errorf("failed to fetch total transactions data: %w", err)
	}

	// Initialize variables to hold metrics
	var totalTransactionsFirstWeek, totalTransactionsSecondWeek int
	var totalAmountFirstWeek, totalAmountSecondWeek float64
	var totalDaysSecondWeek float64

	// Calculate the threshold date for dividing data into two weeks
	thresholdDate := time.Now().AddDate(0, 0, -7)
	layoutFormat := "02/01/2006"

	// Assuming transactionsData is sorted by _date in ascending order
	for _, data := range transactionsData {
		total, _ := strconv.Atoi(data["total_transaction"].(string))
		amount, _ := strconv.ParseFloat(data["total"].(string), 64) // Assuming there's an "amount" field

		// Convert dateStr to time.Time for comparison
		dataDate, err := time.Parse(layoutFormat, data["date"].(string))
		if err != nil {
			log.Error("Error parsing date: %v", err)
		}
		fmt.Println(dataDate, " isBefore: ", dataDate.Before(thresholdDate))

		// Divide the data into two weeks
		if dataDate.Before(thresholdDate) {
			totalTransactionsFirstWeek += total
			totalAmountFirstWeek += amount
		} else {
			totalTransactionsSecondWeek += total
			totalAmountSecondWeek += amount
			totalDaysSecondWeek += 1
		}
	}

	admin, err := r.useCase.repoPrimary.FetchAdmin(adminId)
	log.IfError(err)

	// Calculate total transactions and average daily transactions
	// totalTransactions := totalTransactionsFirstWeek + totalTransactionsSecondWeek
	avgDailyTransactions := float64(totalTransactionsSecondWeek) / totalDaysSecondWeek
	avgDailyTransAmount := totalAmountSecondWeek / totalDaysSecondWeek

	// Calculate week-over-week growth rate
	growthRate := ((float64(totalTransactionsSecondWeek) - float64(totalTransactionsFirstWeek)) / float64(totalTransactionsFirstWeek)) * 100
	growthFlag := "Kenaikan"
	if growthRate < 0 {
		growthFlag = "Penurunan"
	}

	// Format the summary report
	report := fmt.Sprintf(`*#UNIQ POS* - Laporan Mingguan *%v*

Mengalami *%s Penjualan %.1f%%* dibandingkan minggu sebelumnya

*#Key Metrics* 
- Total Transactions : *%s* (IDR %s)
- Average Daily Transactions: *%.1f* (IDR %s)
- Week-over-Week Growth Rate: *%.1f%%* 
`, admin.BusinessName, growthFlag, growthRate,
		utils.CurrencyFormat(totalTransactionsSecondWeek),
		utils.CurrencyFormat(int(totalAmountSecondWeek)),
		avgDailyTransactions,
		utils.CurrencyFormat(int(avgDailyTransAmount)),
		growthRate)

	return report, nil
}

// generate line graph daily by adminId, (sum all outlets)
// returns file path of graph
func (r *reportConfig) buildGraphDaily(adminId int) (string, error) {
	// Fetch data from repository
	data, err := r.useCase.repoPrimary.FetchDailySalesSumByAdmin(adminId, r.DateStart, utils.CurrentMillis())
	if err != nil {
		return "", fmt.Errorf("failed to fetch daily sales sum: %w", err)
	}

	filePath, err := generateGraph2(data)
	if err != nil {
		return "", fmt.Errorf("failed to generate graph: %w", err)
	}

	return filePath, nil
}

// generateGraph takes sales data and generates a line graph, saving it to a file.
func generateGraph(data []map[string]interface{}) (string, error) {
	layoutFormat := "02/01/2006"
	// Parse data into plotter.XYs
	pts := make(plotter.XYs, len(data))
	for i, d := range data {
		dateStr := d["date"].(string)
		total, _ := strconv.ParseFloat(fmt.Sprintf("%v", d["total"]), 64)
		date, err := time.Parse(layoutFormat, dateStr)
		fmt.Println("parse err: ", err)
		pts[i].X = float64(date.Unix())
		pts[i].Y = total
	}

	// Create the plot
	p := plot.New()
	p.Title.Text = "Sales Data"
	p.X.Label.Text = "Date"
	p.Y.Label.Text = "Total"
	// p.NominalX()

	p.X.Tick.Marker = plot.TimeTicks{Format: layoutFormat}

	// Create a line plotter
	line, points, err := plotter.NewLinePoints(pts)
	if err != nil {
		panic(err)
	}

	// Add the line and points to the plot
	p.Add(line, points)

	// Save the plot to a PNG file
	if err := p.Save(8*vg.Inch, 4*vg.Inch, "sales_data.png"); err != nil {
		panic(err)
	}

	return "sales_data.png", nil
}

// generateGraph takes sales data and generates a line graph, saving it to a file.
func generateGraph2(data []map[string]interface{}) (string, error) {
	// Extract values from the input data
	layoutFormat := "02/01/2006"
	var dates []float64
	var totals []float64
	annotations := make([]chart.Value2, 0)
	for _, entry := range data {
		dateStr := entry["date"].(string)
		date, err := time.Parse(layoutFormat, dateStr)
		if err != nil {
			return "", err
		}
		total := cast.ToFloat64(entry["total"])

		dates = append(dates, float64(date.Unix()))
		totals = append(totals, total)

		annotations = append(annotations, chart.Value2{
			XValue: float64(date.Unix()), YValue: total, Label: utils.CurrencyFormat(int(total))})
	}

	textStyle := chart.Style{
		StrokeColor: chart.ColorWhite,
		FontColor:   chart.ColorWhite,
	}
	// Create a new line graph
	graph := chart.Chart{
		Title: "Weekly Report All Outlets",
		TitleStyle: chart.Style{
			FontColor: chart.ColorWhite,
			FontSize:  10,
		},
		XAxis: chart.XAxis{
			Name:      "Date",
			NameStyle: textStyle,
			ValueFormatter: func(v interface{}) string {
				return time.Unix(int64(v.(float64)), 0).Format(layoutFormat)
			},
			Style: chart.Style{
				StrokeColor: chart.ColorWhite,
				FontColor:   chart.ColorWhite,
			},
		},
		YAxis: chart.YAxis{
			Name:      "Total Transactions",
			NameStyle: textStyle,
			Style: chart.Style{
				StrokeColor: chart.ColorWhite,
				FontColor:   chart.ColorWhite,
			},
		},
		Background: chart.Style{
			Padding: chart.Box{
				Top:    50,
				Left:   20,
				Right:  20,
				Bottom: 20,
			},
			FillColor: chart.ColorBlack,
		},
		Canvas: chart.Style{
			FillColor: chart.ColorBlack,
		},
		Series: []chart.Series{
			chart.ContinuousSeries{
				XValues: dates,
				YValues: totals,
				Style: chart.Style{
					StrokeColor: chart.ColorYellow,
					// FillColor:   chart.ColorLightGray,
				},
			},
			chart.AnnotationSeries{
				Annotations: annotations,
				Style: chart.Style{
					StrokeColor: chart.ColorYellow,
					FillColor:   chart.ColorBlack,
					FontColor:   chart.ColorWhite,
				},
			},
		},
	}

	// Add annotations
	// graph.Series[0].Annotations = annotations

	// Generate the graph
	f, err := os.CreateTemp("", "chart.png")
	if err != nil {
		return "", err
	}
	defer f.Close()

	err = graph.Render(chart.PNG, f)
	if err != nil {
		return "", err
	}

	return f.Name(), nil
}

// generateGraph takes sales data and generates a line graph, saving it to a file.
func generateGraph1(data []map[string]interface{}) (string, error) {
	var xValues []float64
	var yValues []float64

	// Assuming data is a slice of maps with "_date" and "total" keys
	for i, datum := range data {
		// Convert _date to a float64 representation (e.g., a simple incrementing index)
		// and total to float64 as required by go-chart
		xValues = append(xValues, float64(i))
		if total, ok := datum["total"].(float64); ok {
			yValues = append(yValues, total)
		} else {
			return "", fmt.Errorf("invalid data format for total, expected float64")
		}
	}

	// Create a new chart
	graph := chart.Chart{
		Series: []chart.Series{
			chart.ContinuousSeries{
				XValues: xValues,
				YValues: yValues,
			},
		},
	}

	// Save the chart to a file
	graphFile, err := os.Create("sales_graph.png")
	if err != nil {
		return "", fmt.Errorf("failed to create graph file: %w", err)
	}
	defer graphFile.Close()

	err = graph.Render(chart.PNG, graphFile)
	if err != nil {
		return "", fmt.Errorf("failed to render graph: %w", err)
	}

	return "sales_graph.png", nil
}
