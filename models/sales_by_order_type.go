package models

type SalesByOrderTypeResponse struct {
	Name             string                   `json:"name,omitempty"`
	Total            int                      `json:"total,omitempty"`
	TotalTransaction int                      `json:"total_transaction,omitempty"`
	Outlet           []SalesByOrderTypeDetail `json:"outlet,omitempty"`
}

type SalesByOrderTypeDetail struct {
	Name             string `json:"name,omitempty"`
	Total            int    `json:"total,omitempty"`
	TotalTransaction int    `json:"total_transaction,omitempty"`
}

type SalesTagByOutlet struct {
	Total            int    `json:"total,omitempty" mapstructure:"total"`
	TotalTransaction int    `json:"total_transaction,omitempty" mapstructure:"total_transaction"`
	OutletFkid       int    `json:"outlet_fkid,omitempty" mapstructure:"outlet_fkid"`
	SalesTagFkid     int    `json:"sales_tag_fkid,omitempty" mapstructure:"sales_tag_fkid"`
	OutletName       string `json:"outlet_name,omitempty" mapstructure:"outlet_name"`
}

type SalesTagByDate struct {
	SalesTagByOutlet
	Date string `json:"date,omitempty"`
}

type SalesByOrderTypeGroupResponse struct {
	Date   string                         `json:"date,omitempty"`
	Total  []SalesByOrderTypeDetail       `json:"total,omitempty"`
	Outlet []SalesByOrderTypeDetailOutlet `json:"outlet,omitempty"`
}

type SalesByOrderTypeDetailOutlet struct {
	Name  string                   `json:"name,omitempty"`
	Total []SalesByOrderTypeDetail `json:"total,omitempty"`
}
