package auth

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/dgrijalva/jwt-go/request"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

var env = os.Getenv("ENV")

/* JWT TOKEN START */
func ValidateToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		timeStart := time.Now()
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(ctx.Request.Header.Peek("Authorization")))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			for key, data := range claims {
				ctx.Request.Header.Set(key, utils.ToString(data))
			}
			ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
			ctx.Response.Header.Set("X-Frame-Options", "DENY")
			ctx.SetContentType("application/json")
			//date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
			//fmt.Println("[request]  ", date, "  ", string(ctx.Method()), "  ", string(ctx.URI().Path()))
			next(ctx)
		} else {
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		}

		bodySize := len(ctx.Response.Body())
		bodyUnit := "B"
		if bodySize/1000 < 1000 {
			bodyUnit = "KB"
			bodySize = bodySize / 1000
		} else if bodySize/1000 >= 1000 {
			bodyUnit = "MB"
			bodySize = bodySize / 1000000
		}

		fmt.Printf("%d | %v | %s | %d %s | %s \n", ctx.Response.StatusCode(), time.Since(timeStart), string(ctx.Method()), bodySize, bodyUnit, string(ctx.URI().Path()))
		logRequest(ctx, timeStart)
	}
}

func ValidateRefreshToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		userToken := string(ctx.FormValue("refresh_token"))
		authBearer := ctx.Request.Header.Peek("Authorization")
		if string(authBearer) != os.Getenv("auth_token") {
			log.Error("some one try to request new token with invalid auth bearer")
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			return
		}
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", userToken)
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})

		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			tokenId := claims["jti"]
			//insurer := claims["insurer"]
			//role := claims["role"]
			userId := claims["user_id"]
			userType := claims["user_type"]
			adminId := claims["admin_id"]

			if isRefreshTokenExist(tokenId.(string), userToken) {
				if tokenId != nil {
					ctx.Request.Header.Set("token_id", tokenId.(string))
					ctx.Request.Header.Set("admin_id", adminId.(string))
					ctx.Request.Header.Set("user_id", userId.(string))
					ctx.Request.Header.Set("user_type", userType.(string))
					ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
					ctx.Response.Header.Set("X-Frame-Options", "DENY")
					ctx.SetContentType("application/json")
					next(ctx)
				} else {
					log.Error("Login fail, some of variable is null. tokenId : %v ", tokenId)
					ctx.SetStatusCode(fasthttp.StatusUnauthorized)
				}
			} else {
				log.Error("token no longer valid - '%s'", userToken)
				ctx.SetStatusCode(fasthttp.StatusUnauthorized)
			}
		} else {
			log.Error("user request with invalid token refresh.. token : '%s'", string(ctx.FormValue("refresh_token")))
			ctx.SetStatusCode(fasthttp.StatusUnauthorized)
		}
	})
}

// check if refresh token is match with our data in db
func isRefreshTokenExist(tokenId, userToken string) bool {
	//if os.Getenv("server") == "development" || os.Getenv("server") == "demo" {
	data, err := db.Query("select token, expired_at from users_session where id = ? and expired_at > UNIX_TIMESTAMP()", tokenId)
	log.IfError(err)

	return utils.CheckPasswordHash(userToken, utils.ToString(data["token"]))
	//}
	//
	//return true
	//if os.Getenv("server") == "production" {
	//	return true
	//}else{
	//	localDb := db.GetDbJson()
	//	refreshToken := new(models.RefreshToken)
	//	err := localDb.Read("refresh_token", tokenId, &refreshToken)
	//	log.IfError(err)
	//
	//	return utils.CheckPasswordHash(userToken, refreshToken.RefreshToken)
	//}
}

/* JWT TOKEN END */

/* CORS START */
var (
	corsAllowHeaders     = "Authorization, Public-Key, Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token"
	corsAllowMethods     = "HEAD,GET,POST,PUT,DELETE,OPTIONS"
	corsAllowOrigin      = "*"
	corsAllowCredentials = "true"
)

func CORS(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		//corsAllowOrigin = string(ctx.Request.URI().Scheme()) + "://" +string(ctx.Request.Host())
		originHeader := string(ctx.Request.Header.Peek("Origin"))
		if originHeader != "" {
			corsAllowOrigin = originHeader
		}

		if string(ctx.Method()) == fasthttp.MethodOptions {
			ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
			ctx.Response.Header.Set("Access-Control-Allow-Headers", corsAllowHeaders)
			ctx.Response.Header.Set("Access-Control-Allow-Methods", corsAllowMethods)
			ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
			ctx.SetStatusCode(fasthttp.StatusNoContent)
			return
		}

		ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
		ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
		//fmt.Println("CORS - headers resp : ", ctx.Response.Header.String(), "headers req : ", ctx.Request.Header.String())
		ctx.SetContentType("application/json") //set default output to json
		next(ctx)
		fmt.Printf("%v  %s - %s\n", ctx.Response.StatusCode(), ctx.Method(), ctx.URI().FullURI())
	}
}

func EnableCors(ctx *fasthttp.RequestCtx) {
	ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
	ctx.Response.Header.Set("Access-Control-Allow-Headers", corsAllowHeaders)
	ctx.Response.Header.Set("Access-Control-Allow-Methods", corsAllowMethods)
	ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
	// fmt.Println("enable cors for ", ctx.URI().String(), "headers resp : ", ctx.Response.Header.String(), "headers req : ", ctx.Request.Header.String())
	ctx.SetStatusCode(fasthttp.StatusNoContent)
}

/* CORS END */

/* ALLOW ACCESS START */
func AllowAccess(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		token := ctx.Request.Header.Peek("Authorization")
		if string(token) != os.Getenv("auth_token") {
			//log.IfError(fmt.Errorf("invalid authorization token from user : %s", token))
			log.Info("invalid authorization token from user : %s", token)
			ctx.SetContentType("application/json")
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			json.NewEncoder(ctx).Encode(models.ApiResponse{
				Code:    fasthttp.StatusForbidden,
				Message: "unauthorized",
			})
			return
		}
		next(ctx)
	}
}

/* ALLOW ACCESS END */

func logRequest(ctx *fasthttp.RequestCtx, timeStart time.Time) {
	logRequest := map[string]interface{}{
		"date":            utils.GetDate(),
		"method":          string(ctx.Method()),
		"request":         string(ctx.URI().Path()),
		"code":            ctx.Response.StatusCode(),
		"time_elapsed":    time.Since(timeStart).Seconds(),
		"real_ip":         string(ctx.Request.Header.Peek("X-Forwarded-For")), //X-Forwarded-For or Cf-Connecting-Ip
		"real_ip_address": string(ctx.Request.Header.Peek("Cf-Connecting-Ip")),
	}
	fmt.Println(utils.SimplyToJson(logRequest))

	if time.Since(timeStart).Seconds() > 15 {
		if env == "development" {
			log.Info("request took %v - %s (%s)", time.Since(timeStart), ctx.URI().String(), ctx.Method())
		} else {
			log.IfError(fmt.Errorf("request took %v - %s (%s) \n %s", time.Since(timeStart), ctx.URI().String(), ctx.Method(), utils.SimplyToJson(logRequest)))
		}
	}
}
