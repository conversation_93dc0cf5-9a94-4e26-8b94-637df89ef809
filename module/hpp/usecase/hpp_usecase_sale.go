package usecase

import (
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h *hppUseCase) recordSales(salesId string) {
	sales, err := h.repo.FetchSalesDetailBySalesId(salesId)
	if err != nil {
		return
	}

	// fmt.Println("sales -->", sales)
	log.Debug("sales: %v", utils.SimplyToJson(sales))
	dataDetailSales := make([]models.StockOutProduct, 0)
	for _, sale := range *sales {
		dataDetailSales = append(dataDetailSales, sale.ToStockOutProduct())
	}
	h.logStockOut(&dataDetailSales, domain.Sale.String(), salesId)

	//handle breakdown
	breakdowns, err := h.repo.FetchSalesBreakdown(salesId)
	if err != nil || breakdowns == nil || len(*breakdowns) == 0 {
		return
	}

	dataDetalBreakdown := make([]models.StockOutProduct, 0)
	for _, breakdown := range *breakdowns {
		dataDetalBreakdown = append(dataDetalBreakdown, breakdown.ToStockOutProduct())
	}
	h.logStockOut(&dataDetalBreakdown, "sales_breakdown", salesId)
}

func (h *hppUseCase) recordSalesRefund(salesId string) []interface{} {
	stockPriceIds := make([]interface{}, 0)
	// ids, err := h.adjustLogStockOutSales("sales", salesId)
	// if log.IfError(err) {
	// 	return stockPriceIds
	// }

	// stockPriceIds = append(stockPriceIds, ids...)

	// ids, err = h.adjustLogStockOutSales("sales_breakdown", salesId)
	// if log.IfError(err) {
	// 	return stockPriceIds
	// }
	// stockPriceIds = append(stockPriceIds, ids...)

	stockPriceDetailIdRemove := make([]int64, 0)
	dataTypes := []string{domain.Sale.String(), domain.SaleBreakdown.String()}
	for _, dataSource := range dataTypes {
		stockPriceSales, err := h.repo.FetchStockPriceByOutSourceId(salesId, dataSource)
		log.Info("stockPriceDetail size by id %v (%v): %v, %v", salesId, dataSource, len(stockPriceSales), utils.SimplyToJson(stockPriceSales))
		if err != nil {
			return stockPriceIds
		}

		dataDetailSales := make([]models.StockInProduct, 0)
		for _, sale := range stockPriceSales {
			//skip if its a pending stock
			//only remove from stock_price_detail
			if sale.StockInId == "" {
				// h.repo.AdjustStockPriceQtyOut(map[int64]float32{
				// 	sale.StockPriceId: -sale.StockOut,
				// })
				// stockPriceDetailIdRemove = append(stockPriceDetailIdRemove, sale.StockPriceDetailId)

				log.Info("stockOut stockPrice adjusted, id %v, by %v, stockPriceDetail id %v will be removed", sale.StockPriceId, -sale.StockOut, sale.StockPriceDetailId)
				h.repo.DeleteStockPriceDetail(sale.StockPriceDetailId)
				continue
			}

			dataDetailSales = append(dataDetailSales, sale.ToStockInProduct())
			h.logStockIn(&[]models.StockInProduct{sale.ToStockInProduct()}, domain.SaleRefund.String(), salesId)
		}

		log.Debug("logStockIn: %v", dataDetailSales)
		// stockInProducts := uniqueStocks(dataDetailSales)
		// h.logStockIn(&dataDetailSales, domain.SaleRefund.String(), salesId)
	}

	//remove stock_price_detail by id
	log.Info("refund, StockPriceDetailId to be removed %v", stockPriceDetailIdRemove)
	h.repo.AdjustStockPriceDetail(nil, stockPriceDetailIdRemove, nil)

	return stockPriceIds
}

// [DEPRECATED] to adjust the stock price log, for cases : refund
func (h *hppUseCase) adjustLogStockOutSales(dataType, dataId string) ([]interface{}, error) {
	stockPriceIds := make([]interface{}, 0)
	hpps, err := h.repo.FetchStockPriceByOutSourceId(dataId, dataType)
	if err != nil {
		return nil, err
	}

	productIds := make([]int, 0)
	for i, hpp := range hpps {
		stockOut := hpp.StockOut - hpp.StockOutDetail
		hpps[i].StockOut = stockOut
		//update/adjust stock out
		//if stock out becomes zero, remove stock price
		if stockOut == 0 && hpp.StockIn == 0 {
			_ = h.repo.DeleteHppById(int(hpp.StockPriceId))
		} else {
			_ = h.repo.UpdateStockPrice(int(hpp.StockPriceId), map[string]interface{}{
				"stock_out": stockOut,
			})
		}

		if stockOut < hpp.StockIn {
			stockInOld := stockOut
			stockInNew := hpp.StockOut - stockOut

			if hpp.StockOut < hpp.StockIn {
				stockInOld = hpp.StockIn - hpp.StockOutDetail
				stockInNew = hpp.StockOutDetail
			}

			//update/adjust qty in (match with stock out)
			if stockOut == 0 && stockInOld == 0 {
				_ = h.repo.DeleteHppById(int(hpp.StockPriceId))
			} else {
				err = h.repo.UpdateStockPriceQtyIn(int(hpp.StockPriceId), stockInOld)
				log.IfError(err)
				log.Info("update stock in: %v, to: %v", (hpp.StockPriceId), stockInOld)
			}

			lastHpp, err := h.repo.FetchLastHppByProductId(hpp.ProductDetailFkid)
			if err != nil {
				return nil, err
			}

			if lastHpp.StockOut == 0 || lastHpp.StockIn == lastHpp.StockOut {
				//insert to last
				id, err := h.repo.InsertStockPrice(domain.Hpp{
					ProductDetailFkid: hpp.ProductDetailFkid,
					StockIn:           stockInNew,
					StockOut:          0,
					Price:             hpp.Price,
					StockInId:         hpp.StockInId,
					StockInSource:     hpp.StockInSource,
					UpdatedAt:         utils.CurrentMillis(),
					CreatedAt:         utils.CurrentMillis(),
				})
				if err == nil {
					log.Info("stock price added: %v, in: %v, inId: %v", id, stockInNew, hpp.StockInId)
				}

				stockPriceIds = append(stockPriceIds, id)
			} else {
				_ = h.repo.UpdateStockPrice(lastHpp.StockPriceId, map[string]interface{}{
					"stock_in":        lastHpp.StockIn + stockInNew,
					"price":           hpp.Price,
					"stock_in_id":     hpp.StockInId,
					"stock_in_source": hpp.StockInSource,
				})
				log.Info("update stock in: %v, to: %v", lastHpp.StockPriceId, lastHpp.StockIn+stockInNew)
				stockPriceIds = append(stockPriceIds, lastHpp.StockPriceId)

				if lastHpp.StockOut > stockInNew {
					stockPriceDetails, err := h.repo.FetchStockPriceDetail(lastHpp.StockPriceId)
					if err != nil {
						return nil, err
					}

					//newStockOut := lastHpp.StockOut - stockInRemain
					stockPriceDetailNew := make([]map[string]interface{}, 0)
					stockPriceDetailAdjust := make([]models.StockPriceDetailEntity, 0)
					isEnough := false
					totalQty := float32(0)
					for _, detail := range *stockPriceDetails {
						if (detail.StockOut+totalQty) >= stockInNew && !isEnough {
							isEnough = true
							stockOutTaken := detail.StockOut - (stockInNew - totalQty)
							if stockOutTaken > 0 {
								newDetail := array.Copy(detail.ToMap())
								newDetail["stock_out"] = detail.StockOut - (stockInNew - totalQty)
								stockPriceDetailNew = append(stockPriceDetailNew, newDetail)

								stockPriceDetailAdjust = append(stockPriceDetailAdjust, detail)
								stockPriceDetailAdjust[0].StockOut = detail.StockOut - cast.ToFloat32(newDetail["stock_out"])
							}
						} else if isEnough {
							stockPriceDetailNew = append(stockPriceDetailNew, detail.ToMap())
						}

						totalQty += detail.StockOut
					}

					if len(stockPriceDetailAdjust) > 0 {
						_ = h.repo.UpdateStockPrice(int(stockPriceDetailAdjust[0].StockPriceFKID), map[string]interface{}{
							"stock_out": stockPriceDetailAdjust[0].StockOut,
						})
					}
					id, _ := h.repo.AdjustStockPriceDetail(stockPriceDetailAdjust, nil, stockPriceDetailNew)
					stockPriceIds = append(stockPriceIds, id)
				}
			}
		}

		productIds = append(productIds, hpp.ProductDetailFkid)
	}

	h.updateProductPriceBuy(productIds...)
	_ = h.repo.DeleteStockPriceDetailByOutsourceId(dataId, dataType)
	return stockPriceIds, nil
}
