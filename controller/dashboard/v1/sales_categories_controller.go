package v1

import (
	"encoding/json"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"net/http"
	"strings"
)

func SalesCategory_v1(ctx *fasthttp.RequestCtx)  {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  false,
			Code:    400,
			Data:    err,
		})
	}else{
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess,",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		response := SalesByAllCategory_map(adminId, timeStart, timeEnd, outletIds)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  true,
			Code:    200,
			Data:    response,
		})
	}
}
