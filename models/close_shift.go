package models

type CloseShiftResponse struct {
	SubSalesDetail float64 `json:"sub_sales_detail"`
	Bill           int     `json:"bill"`
	Pax            int     `json:"pax"`
	DiscountSales  float64 `json:"discount_sales"`
	Cash           float64 `json:"cash"`
	Card           float64 `json:"card"`
	DutyMeals      float64 `json:"duty_meals"`
	Compliment     float64 `json:"compliment"`
	Free           float64 `json:"free"`
	Piutang        float64 `json:"piutang"`
	Service        float64 `json:"service"`
	Tax            float64 `json:"tax"`
	GrandRefund    float64 `json:"grand_refund"`
	Voucher        float64 `json:"voucher"`
	SubVoid        float64 `json:"sub_void"`
	StlDis         float64 `json:"stl_dis"`
	Total          float64 `json:"total"`
	TotalMedia     float64 `json:"total_media"`
	TotalEntertain float64 `json:"total_entertain"`
	Average        float64 `json:"average"`
	AverageBill    float64 `json:"average_bill"`
	Sales          float64 `json:"sales"`
	TotDis         float64 `json:"tot_dis"`
	QtyVoid        int     `json:"qty_void"`
	BillRefund     int     `json:"bill_refund"`
	Promo          float64 `json:"promo"`
}

type SalesRecapShift struct {
	GrandTotal       float64 `json:"grand_total,omitempty"`
	DiscountSales    float64 `json:"discount_sales,omitempty"`
	Pax              int     `json:"pax,omitempty"`
	VoucherSales     float64 `json:"voucher_sales,omitempty"`
	Bill             int     `json:"bill,omitempty"`
	GrandTotalRefund float64 `json:"grand_total_refund,omitempty"`
	BillRefund       int     `json:"bill_refund,omitempty"`
}

type SalesDetailShift struct {
	SubTotal       float64 `json:"sub_total,omitempty"`
	DiscountDetail float64 `json:"discount_detail,omitempty"`
}

type PayShift struct {
	Cash       float64
	Card       float64
	Piutang    float64
	Compliment float64
	Free       float64
	DutyMeals  float64
}

type TaxShift struct {
	Tax      float64
	Service  float64
	Voucher  float64
	Discount float64
}

type VoidShiftRecap struct {
	QtyVoid int
	SubVoid float64
	Dicount int
}

type RefundShiftRecap struct {
	GrandTotal float64
	BillRefund int
}

type PromoRecapTotal struct {
	Promo float64 // Assuming Promo is of type float64, you can adjust it accordingly
}
