package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// FetchSpoil implements hpp.Repository.
func (h *hppRepository) FetchSpoil(spoilId ...int) ([]models.SpoilEntity, error) {
	sql := `SELECT spoil_id, product_detail_fkid, qty, time_created from spoils where spoil_id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": spoilId,
	})

	var result []models.SpoilEntity
	err := h.db.Query(sql, params...).Model(&result)
	return result, err
}
