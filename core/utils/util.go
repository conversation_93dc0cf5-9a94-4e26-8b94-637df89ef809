package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"math"
	"math/rand"
	"net/url"
	"os"
	"os/exec"
	"path/filepath"
	"reflect"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"
	"unicode"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/models"
	"golang.org/x/crypto/bcrypt"
)

func Cleanup(ctx *fasthttp.RequestCtx) {
	if r := recover(); r != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprint(ctx, r)
		fmt.Println(date, " : Panic ", r)
	}
}

// return true if found error
func CheckError(ctx *fasthttp.RequestCtx, err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		fmt.Fprintln(ctx, "Server Error "+err.Error())
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s   %s:%d %v", date, fn, line, err)
		log.Printf("%s   %s:%d %v", date, fn, line, err)

		return true
	}
	return false
}

func CheckErr(err error) bool {
	if err != nil {
		date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
		//fmt.Println(date, "   Error While ", onDoing, ". ", err)
		_, fn, line, _ := runtime.Caller(1)
		//fmt.Printf("%s  [error] %s:%d %v", date, fn, line, err)
		log.Printf("%s  [error] %s:%d %v", date, fn, line, err)

		return true
	}

	return false
}

func ByteToInt(data []byte) int {
	intValue, err := strconv.Atoi(string(data))
	if err != nil {
		return 0
	}
	return intValue
}

func StringToInt(data string) int {
	res, err := strconv.Atoi(data)
	if err != nil {
		return 0
	} else {
		return res
	}
}

func ReplaceEmoji(data string) string {
	var emojiRx = regexp.MustCompile(`[\x{1F600}-\x{1F6FF}|[\x{2600}-\x{26FF}]`)
	return emojiRx.ReplaceAllString(data, ``)
}

func CurrencyFormat(amount interface{}) string {
	result := ""
	amountStr := ToString(amount)
	index := 1
	for i := len(amountStr); i > 0; i-- {
		data := amountStr[i-1 : i]
		if index%3 == 0 {
			result += data + "."
		} else {
			result += data
		}
		index++
	}
	result = Reverse(result)
	if strings.HasPrefix(result, ".") {
		result = result[1:]
	} else if strings.HasPrefix(result, "-.") {
		result = "-" + result[2:]
	}

	return result
}

func ParseCurrencyToInt(data string) int {
	if data == "" {
		return 0
	}

	r := strings.NewReplacer(".", "",
		",", "")
	result := r.Replace(data)
	resultInt, err := strconv.Atoi(result)
	if err != nil {
		fmt.Println(fmt.Sprintf("Parse currency error. data : %s | after replace : %s | error : %s", data, result, err))
		return 0
	}
	return resultInt
}

func ParseCurrencyToFloat(data string) float64 {
	if data == "" {
		return 0
	}

	r := strings.NewReplacer(".", "",
		",", "")
	result := r.Replace(data)
	resultInt, err := strconv.ParseFloat(result, 64)
	if err != nil {
		fmt.Println(fmt.Sprintf("Parse currency error. data : %s | after replace : %s | error : %s", data, result, err))
		return 0
	}
	return resultInt
}

func Add(num1 string, num2 interface{}) string {
	num1Int := ParseCurrencyToInt(num1)
	num2Int := ParseCurrencyToInt(ToString(num2))
	return CurrencyFormat(num1Int + num2Int)
}

func Reverse(s string) string {
	runes := []rune(s)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func ExeCommand(name string, args ...string) (string, error) {
	cmd := exec.Command(name, args...)

	cmdOutput := &bytes.Buffer{}
	// Attach buffer to command
	cmd.Stdout = cmdOutput

	err := cmd.Run()
	if err != nil {
		fmt.Printf("==> Error When Executing : %s\n", strings.Join(cmd.Args, " "))
		fmt.Println("==> Error Message : ", err.Error())
		return "", err
	}

	outputByte := cmdOutput.Bytes()
	if len(outputByte) > 0 {
		return string(outputByte), nil
	} else {
		return "", nil
	}
}

func ReadFile(path string) (string, error) {
	file, err := os.Open(path)
	if err != nil {
		return "", err
	}
	defer file.Close()
	result, err := ioutil.ReadAll(file)
	return string(result), nil
}

func WriteFile(data, path string) error {
	// To start, here's how to dump a string (or just
	// bytes) into a file.
	d1 := []byte(data)
	err := ioutil.WriteFile(path, d1, 0644)
	return err
	//if err != nil {
	//	return err
	//}
	//
	//// For more granular writes, open a file for writing.
	//f, err := os.Create("/tmp/dat2")
	//if err != nil {
	//	return err
	//}
	//
	//// It's idiomatic to defer a `Close` immediately
	//// after opening a file.
	//defer f.Close()
	//
	//// You can `Write` byte slices as you'd expect.
	//d2 := []byte{115, 111, 109, 101, 10}
	//n2, err := f.Write(d2)
	//if err != nil {
	//	return err
	//}
	//
	//fmt.Printf("wrote %d bytes\n", n2)
	//
	//// A `WriteString` is also available.
	//n3, err := f.WriteString("writes\n")
	//fmt.Printf("wrote %d bytes\n", n3)
	//
	//// Issue a `Sync` to flush writes to stable storage.
	//f.Sync()
	//
	//// `bufio` provides buffered writers in addition
	//// to the buffered readers we saw earlier.
	//w := bufio.NewWriter(f)
	//n4, err := w.WriteString("buffered\n")
	//fmt.Printf("wrote %d bytes\n", n4)
	//
	//// Use `Flush` to ensure all buffered operations have
	//// been applied to the underlying writer.
	//w.Flush()
}

const letterBytes = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"

func RandStringBytes(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}
	return string(b)
}

func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), 14)
	return string(bytes), err
}

func CheckPasswordHash(password, hash string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hash), []byte(password))
	return err == nil
}

func ToString(data interface{}) string {
	switch v := data.(type) {
	case []uint8:
		return string(v)
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case nil:
		return ""
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("invalid convert toString. '%v' is %s | %s:%d", data, reflect.TypeOf(data), GetFileName(fn, true), line)
		return ""
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.Atoi(dataStr)
	if CheckErr(err) {
		return 0
	} else {
		return result
	}
}

func ToFloat(data interface{}) float64 {
	dataStr := ToString(data)
	result, err := strconv.ParseFloat(dataStr, 64)
	if err != nil {
		//fmt.Println("Converting string to float error", err, "Data : ", data)
		return 0
	}
	return result
}

func ToFloat32(data interface{}) float32 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseFloat(dataStr, 32)
	if CheckErr(err) {
		return 0
	} else {
		return float32(result)
	}
}

func FloatFormat(data interface{}) string {
	f64 := ToFloat(data)
	if f64 == float64(int(f64)) {
		return fmt.Sprintf("%.0f", f64)
	} else {
		return fmt.Sprintf("%.2f", f64)
	}
}

// TakeMax returns a substring of data up to max length.
// If data length is less than or equal to max, returns the original string.
// If data length exceeds max, returns the first max characters.
func TakeMax(data string, max int) string {
	if len(data) > max {
		return data[:max]
	} else {
		return data
	}
}

func TakeGreater(a, b int) int {
	if b > a {
		return b
	}
	return a
}

func SendMessageToSlack(message string) {
	request := HttpRequest{}
	request.DisableLog = true
	request.Method = "POST"
	request.Url = "*****************************************************************************"
	request.PostRequest.Body = map[string]interface{}{
		"text": message,
	}
	_, err := request.Execute()
	if err != nil {
		fmt.Println("Sending to slack error ", err)
	}
}

func SendEmailZoho(zohoMail models.ZohoMail) {
	attachment := make([]map[string]interface{}, 0)
	for _, path := range zohoMail.Attachments {
		request := HttpRequest{
			Method:         "POST",
			Url:            fmt.Sprintf("https://mail.zoho.com/api/accounts/5959564000000008002/messages/attachments?fileName=%s", filepath.Base(path)),
			BinaryFilePath: path,
			Header: map[string]interface{}{
				"Authorization": "6269fea601cf0fe94cde7b67fd849499",
				"Content-Type":  "application/octet-stream",
			},
		}
		res, err := request.Execute()
		if !CheckErr(err) {
			var zohoAttachment models.ZohoAttachment
			err := json.Unmarshal(res, &zohoAttachment)
			if err != nil {
				fmt.Println("parsing zoho response error: ", err)
				fmt.Println("resp: ", string(res))
				continue
			}

			attachment = append(attachment, map[string]interface{}{
				"storeName":      zohoAttachment.Data.StoreName,
				"attachmentPath": zohoAttachment.Data.AttachmentPath,
				"attachmentName": zohoAttachment.Data.AttachmentName,
			})
			fmt.Println("path: ", zohoAttachment.Data.AttachmentPath)
		}
	}

	request := HttpRequest{
		Method: "POST",
		Url:    "https://mail.zoho.com/api/accounts/5959564000000008002/messages",
		Header: map[string]interface{}{
			"Authorization": "6269fea601cf0fe94cde7b67fd849499",
			"Content-Type":  "application/json",
		},
		PostRequest: PostRequest{
			Body: map[string]interface{}{
				"fromAddress": "<EMAIL>",
				"toAddress":   zohoMail.To,
				"subject":     zohoMail.Subject,
				"content":     zohoMail.Content,
				"attachments": attachment,
			},
		},
		MultipartRequest: MultipartRequest{},
		BinaryFilePath:   "",
	}

	resp, err := request.Execute()
	if err != nil {
		fmt.Println("sending mail error", err)
	}

	fmt.Println("body: ", TakeMax(string(resp), 300))

	//url := ""
	//
	//jsonMap := map[string]interface{}{
	//	"fromAddress": "<EMAIL>",
	//	"toAddress":   zohoMail.To,
	//	"subject":     zohoMail.Subject,
	//	"content":     zohoMail.Content,
	//	"attachments": attachment,
	//}
	//
	//json, err := json.Marshal(jsonMap)
	//if err != nil {
	//	fmt.Println("Parse map to json error : ", err)
	//}
	//
	//var jsonStr = []byte(json)
	//req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonStr))
	//req.Header.Set("Authorization", "6269fea601cf0fe94cde7b67fd849499")
	//req.Header.Set("Content-Type", "application/json")
	//
	//client := &http.Client{}
	//resp, err := client.Do(req)
	//if err != nil {
	//	panic(err)
	//}
	//defer resp.Body.Close()
	//
	//fmt.Println("Send Email to "+zohoMail.To+" response Status:", resp.Status)
	//if resp.StatusCode != 200 {
	//	fmt.Println("body: ", resp.Body)
	//}
	//fmt.Println("response Headers:", resp.Header)
	//body, _ := ioutil.ReadAll(resp.Body)
	//fmt.Println("response Body:", string(body))
}

func GetFileName(path string, withoutExtension bool) string {
	pathArray := strings.Split(path, "/")
	lastPath := path
	if len(pathArray) > 1 {
		lastPath = pathArray[len(pathArray)-1]
	}

	if withoutExtension {
		lastPath = strings.TrimSuffix(lastPath, filepath.Ext(lastPath))
	}

	return lastPath
}

func DeleteFile(path string) {
	// delete file
	var err = os.Remove(path)
	CheckErr(err)

	fmt.Println("==> done deleting file")
}

func BuildHttpQuery(data map[string]interface{}) string {
	q := url.Values{}
	for index, d := range data {
		q.Add(index, ToString(d))
	}
	return ToString(q.Encode())
}

// Given two maps, recursively merge right into left, NEVER replacing any key that already exists in left
func MergeKeys(left, right map[string]interface{}) map[string]interface{} {
	for key, rightVal := range right {
		if _, present := left[key]; present {
			//then we don't want to replace it - recurse
			//left[key] = MergeKeys(leftVal.(map[string]interface{}), rightVal.(map[string]interface{}))
		} else {
			// key not in left so we can just shove it in
			left[key] = rightVal
		}
	}
	return left
}

func RoundUp(num float64) float64 {
	return math.Ceil(num*100) / 100
}

func IsValidFloat32(value float32) bool {
	return !math.IsInf(float64(value), 0) && !math.IsNaN(float64(value))
}

func ConcatData(data interface{}, separator string) string {
	// Use reflection to handle different data types
	val := reflect.ValueOf(data)

	// Check if it's a slice or array
	if val.Kind() != reflect.Slice && val.Kind() != reflect.Array {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v ConcatData failed, its not slice.. but %v \n", fn, line, val.Kind())
		return "" // Or handle the error as needed
	}

	var strParts []string
	for i := 0; i < val.Len(); i++ {
		// Convert each element to a string
		strParts = append(strParts, fmt.Sprintf("%v", val.Index(i)))
	}

	fmt.Println(">>>> ", strings.Join(strParts, separator))
	return strings.Join(strParts, separator)
}

func IsAlphabet(input interface{}) bool {
	str := ToString(input)
	if str == "" {
		return false
	}

	// Convert to rune for proper Unicode handling
	for _, char := range str {
		if !unicode.IsLetter(char) {
			return false
		}
	}
	return true
}
