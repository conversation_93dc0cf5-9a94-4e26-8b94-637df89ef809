package usecase

import (
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h *hppUseCase) handleSpoil(spoilIds []int) error {
	spoils, err := h.repo.FetchSpoil(spoilIds...)
	if log.IfError(err) {
		return err
	}
	log.Info("spoilSize: %v, ids size: %v, ids: %v", len(spoils), len(spoilIds), utils.SimplyToJson(spoilIds))

	// stockIns := make([]models.StockInProduct, 0)
	for _, spoil := range spoils {
		// stockIns = append(stockIns, spoil.ToStockInProduct())
		h.logStockOut(&[]models.StockOutProduct{spoil.ToStockOutProduct()}, domain.Spoil.String(), cast.ToString(spoil.SpoilID))
	}

	return nil
}
