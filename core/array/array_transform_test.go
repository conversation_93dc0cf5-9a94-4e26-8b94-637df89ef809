package array

import "testing"

func TestIsIn(t *testing.T) {
	type args struct {
		data      interface{}
		dataArray interface{}
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{name: "[int] not in", args: args{data: 1, dataArray: []int{2, 3, 4, 5}}, want: false},
		{name: "[int] in", args: args{data: 1, dataArray: []int{2, 3, 4, 5, 1}}, want: true},
		{name: "[str] not in", args: args{data: "joko", dataArray: []string{"budi", "andi"}}, want: false},
		{name: "[str] in", args: args{data: "joko", dataArray: []string{"budi", "joko", "andi"}}, want: true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsIn(tt.args.data, tt.args.dataArray); got != tt.want {
				t.Errorf("IsIn() = %v, want %v", got, tt.want)
			}
		})
	}
}
