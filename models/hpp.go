package models

import "time"

type StockPriceEntity struct {
	ID              int     `json:"stock_price_id"`
	ProductDetailID int     `json:"product_detail_fkid"`
	StockIn         float32 `json:"stock_in"`
	StockOut        float32 `json:"stock_out"`
	Price           float32 `json:"price"`
	StockInID       string  `json:"stock_in_id"`
	StockInSource   string  `json:"stock_in_source"`
	UpdatedAt       int64   `json:"updated_at"`
	CreatedAt       int64   `json:"created_at"`
	DeletedAt       int64   `json:"deleted_at"`
	MetaData        string  `json:"meta_data"`
}

type StockPriceDetailEntity struct {
	StockPriceDetailID int64   `json:"stock_price_detail_id,omitempty"`
	StockPriceFKID     int64   `json:"stock_price_fkid,omitempty"`
	StockOut           float32 `json:"stock_out"`
	StockOutID         string  `json:"stock_out_id,omitempty"`
	StockOutSource     string  `json:"stock_out_source,omitempty"`
	CreatedAt          int64   `json:"created_at,omitempty"`
	UpdatedAt          int64   `json:"updated_at,omitempty"`
	MetaData           string  `json:"meta_data,omitempty"`
}

type HppUpdate struct {
	StockPriceId    int
	Qty             float32
	Price           float32
	ProductDetailId int
}

// StockPriceEntity toMap
func (e *StockPriceEntity) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"stock_price_id":      e.ID,
		"product_detail_fkid": e.ProductDetailID,
		"stock_in":            e.StockIn,
		"stock_out":           e.StockOut,
		"price":               e.Price,
		"stock_in_id":         e.StockInID,
		"stock_in_source":     e.StockInSource,
		"updated_at":          e.UpdatedAt,
		"created_at":          e.CreatedAt,
		"deleted_at":          e.DeletedAt,
		"meta_data":           e.MetaData,
	}
}

func (s *StockPriceDetailEntity) ToMap() map[string]interface{} {
	m := make(map[string]interface{})
	if s.StockPriceDetailID != 0 {
		m["stock_price_detail_id"] = s.StockPriceDetailID
	}
	if s.StockPriceFKID != 0 {
		m["stock_price_fkid"] = s.StockPriceFKID
	}
	if s.StockOut != 0 {
		m["stock_out"] = s.StockOut
	}
	if s.StockOutID != "" {
		m["stock_out_id"] = s.StockOutID
	}
	if s.StockOutSource != "" {
		m["stock_out_source"] = s.StockOutSource
	}
	if s.CreatedAt != 0 {
		m["created_at"] = s.CreatedAt
	}
	if s.UpdatedAt != 0 {
		m["updated_at"] = s.UpdatedAt
	}
	if s.MetaData != "" {
		m["meta_data"] = s.MetaData
	}
	return m
}

type StockPriceWithDetail struct {
	ProductName        string `json:"product_name,omitempty"`
	OutletName         string `json:"outlet_name,omitempty"`
	CreatedAtIn        int64  `json:"created_at_in,omitempty"`
	StockPriceID       int    `json:"stock_price_id,omitempty"`
	ProductDetailFkid  int    `json:"product_detail_fkid,omitempty"`
	StockIn            int    `json:"stock_in,omitempty"`
	StockOut           int    `json:"stock_out,omitempty"`
	Price              int    `json:"price,omitempty"`
	StockInID          string `json:"stock_in_id,omitempty"`
	StockInSource      string `json:"stock_in_source,omitempty"`
	UpdatedAt          int64  `json:"updated_at,omitempty"`
	CreatedAt          int64  `json:"created_at,omitempty"`
	DeletedAt          int64  `json:"deleted_at,omitempty"`
	MetaDataIn         string `json:"meta_data_in,omitempty"`
	MetaData           string `json:"meta_data,omitempty"`
	StockPriceDetailID int    `json:"stock_price_detail_id,omitempty"`
	StockPriceFkid     int    `json:"stock_price_fkid,omitempty"`
	StockOutID         string `json:"stock_out_id,omitempty"`
	StockOutSource     string `json:"stock_out_source,omitempty"`
}

type StockPriceWithDetailQty struct {
	StockOut           float32 `json:"stock_out,omitempty"`        //stock_out in stock_price
	StockOutDetail     float32 `json:"stock_out_detail,omitempty"` //stock_out in stock_price_detail
	StockIn            float32 `json:"stock_in,omitempty"`
	StockInId          string  `json:"stock_in_id,omitempty"`
	StockInSource      string  `json:"stock_in_source,omitempty"`
	StockPriceId       int64   `json:"stock_price_id,omitempty"`
	StockPriceDetailId int64   `json:"stock_price_detail_id,omitempty"`
	ProductDetailFkid  int     `json:"product_detail_fkid,omitempty"`
	Price              float32 `json:"price,omitempty"`
}

func (s *StockPriceWithDetailQty) ToStockInProduct() StockInProduct {
	return StockInProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.StockOutDetail,
		Price:           s.Price,
		MetaData: map[string]interface{}{
			"stock_price_id": s.StockPriceId,
		},
		CreatedAt: time.Now().UnixNano() / int64(time.Millisecond),
	}
}
