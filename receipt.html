<!doctype html>
<html>
<head>
    <meta charset="utf-8">
    <title>Receipt Note</title>
    <meta name="viewport" content="width=device-width" />
    <style>
        .invoice-box {
            max-width: 800px;
            margin: auto;
            padding: 30px;
            border: 1px solid #eee;
            box-shadow: 0 0 10px rgba(0, 0, 0, .15);
            font-size: 16px;
            line-height: 24px;
            font-family: 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
            color: #555;
        }

        .invoice-box table {
            width: 100%;
            line-height: inherit;
            text-align: left;
        }

        .invoice-box table td {
            padding: 5px;
            vertical-align: top;
        }

        .invoice-box table tr td:nth-child(3) {
            text-align: right;
        }

        .invoice-box table tr.top table td {
            padding-bottom: 20px;
        }

        .invoice-box table tr.top table td.title {
            font-size: 45px;
            line-height: 45px;
            color: #333;
        }

        .invoice-box table tr.information table td {
            padding-bottom: 40px;
        }

        .invoice-box table tr.heading td {
            background: #eee;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
        }

        .invoice-box table tr.details td {
            padding-bottom: 20px;
        }

        .invoice-box table tr.item td {
            border-bottom: 1px solid #eee;
        }

        .invoice-box table tr.item.last td {
            border-bottom: none;
        }

        .invoice-box table tr.total td:nth-child(3) {
            border-top: 2px solid #eee;
            font-weight: bold;
        }

        @media only screen and (max-width: 600px) {
            .auto-resize-square{
                margin: auto;
            }
            .invoice-box table tr.top table td {
                width: 100%;
                display: block;
                text-align: center;
            }

            .invoice-box table tr.information table td {
                width: 100%;
                display: block;
                text-align: center;
            }
        }

        /** RTL **/
        .rtl {
            direction: rtl;
            font-family: Tahoma, 'Helvetica Neue', 'Helvetica', Helvetica, Arial, sans-serif;
        }

        .rtl table {
            text-align: right;
        }

        .rtl table tr td:nth-child(3) {
            text-align: left;
        }

        .tab {
            margin-left: 10px;
        }

        img {
            max-width: 100%;
            max-height: 100%;
        }

        .auto-resize-square {
            height: 95px;
            width: 95px;
        }
    </style>
</head>

<body>
<div class="invoice-box">
    <table cellpadding="0" cellspacing="0">
        <tr class="top">
            <td colspan="3">
                <table>
                    <tr>
                        <td class="title">
                            <div class="auto-resize-square">
                                <img src={{.OutletLogo}}>
                            </div>
                        </td>
                        <td></td>
                        <td>
                            No Nota : {{.ReceiptNo}}<br>
                            Tanggal : {{.Date}}<br>
                            Kasir : {{.Cashier}}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr class="information">
            <td colspan="3">
                <table>
                    <tr>
                        <td>
                        {{.Outlet}}<br>
                        {{.OutletAddress}}<br>
                        {{.OutletPhone}}
                        </td>
                        <td></td>
                        <td>

                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr class="heading">
            <td>
                Item
            </td>
            <td>
                QTY
            </td>
            <td>
                Subtotal
            </td>
        </tr>

    {{range .Items}}
        <tr class="item">
            <td> {{.Product}} </td>
            <td> {{.Qty}} </td>
            <td>{{.SubTotal}} </td>
        </tr>
    {{range .SubItems}}
        <tr class="item">
            <td>
            {{.Product}}
            </td>
            <td> {{.Qty}} </td>
            <td>
            {{.SubTotal}}
            </td>
        </tr>
    {{end}}
    {{end}}

    {{/*<tr class="item last">*/}}
    {{/*<td>*/}}
    {{/*Domain name (1 year)*/}}
    {{/*</td>*/}}

    {{/*<td>*/}}
    {{/*$10.00*/}}
    {{/*</td>*/}}
    {{/*</tr>*/}}

    {{range .TaxAndDisc}}
        <tr>
            <td></td>
            <td></td>
            <td>
            {{.Name}} : {{.Total}}
            </td>
        </tr>
    {{end}}
        <tr class="total">
            <td></td>
            <td></td>
            <td>
                Grand Total: {{.GrandTotal}}
            </td>
        </tr>

        <tr class="heading">
            <td>
                Payment Method
            </td>
            <td></td>
            <td>
                Check #
            </td>
        </tr>
    {{range .Payments}}
        <tr>
            <td>
            {{.Method}}
            </td>
            <td></td>
            <td>
            {{.Total}}
            </td>
        </tr>
    {{end}}

        <tr class="total">
            <td></td>
            <td></td>
            <td>
                Kembali : {{.Return}}
            </td>
        </tr>
    </table>
    <br/><br/>
    <p align="center">
        powered by UNIQ <br/>
        www.uniq.id
    </p>

</div>
</body>
</html>
