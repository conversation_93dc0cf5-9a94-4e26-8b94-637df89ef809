package v1

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"github.com/yvasiyarov/php_session_decoder"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func Login(ctx *fasthttp.RequestCtx) {
	//init var
	valid := false
	ctx.SetContentType("application/json")
	ctx.SetStatusCode(http.StatusBadRequest)
	response := models.ApiResponse{
		Code:    400,
		Message: "Username or password invalid!",
	}
	tokenPayload := map[string]interface{}{}

	//retrieve input data
	email := string(ctx.FormValue("email"))
	password := string(ctx.FormValue("password"))

	if email == "" || password == "" {
		log.Info("email: '%s' password: '%s'", email, password)
		ctx.SetStatusCode(http.StatusBadRequest)
		response = models.ApiResponse{
			Code:    400,
			Message: "email or password cannot be empty!",
		}

		json.NewEncoder(ctx).Encode(response)
		return
	}
	issuer := string(ctx.Request.Header.Peek("issuer"))
	token := ctx.Request.Header.Peek("Authorization")
	if string(token) != os.Getenv("auth_token") {
		fmt.Println("token .env: ", os.Getenv("auth_token"))
		log.Error("user %s login with invalid authorization \ntoken from user : %s", email, token)
		ctx.SetContentType("application/json")
		ctx.SetStatusCode(fasthttp.StatusForbidden)
		json.NewEncoder(ctx).Encode(models.ApiResponse{
			Code:    fasthttp.StatusForbidden,
			Message: "unauthorized",
		})
		return
	}

	//cek admin
	if !valid {
		data, err := db.Query(`SELECT email,password,admin_id FROM admin WHERE email=? AND activation_status=?`, email, "activated")
		if utils.CheckErr(err) {
			return
		}
		if len(data) > 0 {
			log.Info("user %s found as ADMIN !", email)
			if utils.CheckPasswordHash(string(password), data["password"].(string)) {
				log.Info("password %s VALID !", email)
				valid = true

				//get outlet access
				dataOutlet, err := db.QueryArray(`SELECT outlet_id FROM outlets WHERE data_status = 'on' AND admin_fkid = ? `, data["admin_id"])
				if utils.CheckErr(err) {
					return
				}
				var outletIds = make([]string, 0)
				for _, outlet := range dataOutlet {
					outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
				}
				outletAccess := strings.Join(outletIds, ",")

				//create token ADMIN
				if issuer != "" {
					issuer += ".report"
				} else {
					issuer = "A" + cast.ToString(data["admin_id"]) + ".report"
				}
				tokenPayload = map[string]interface{}{
					"user_id":       cast.ToString(data["admin_id"]),
					"user_type":     "admin",
					"admin_id":      cast.ToString(data["admin_id"]),
					"outlet_access": outletAccess,
				}
			} else {
				log.Error("password %s INVALID !", email)
			}
		}
	}

	//cek employee
	if !valid {
		data, err := db.Query(`SELECT email,password,employee_id,admin_fkid FROM employee WHERE email=? AND access_status_web=? AND data_status=?`, email, "activated", "on")
		if utils.CheckErr(err) {
			return
		}
		if len(data) > 0 {
			log.Info("user %s found as EMPLOYEE !", email)
			if utils.CheckPasswordHash(string(password), data["password"].(string)) {
				log.Info("password %s VALID !", email)
				valid = true

				//get outlet access
				dataOutlet, err := db.QueryArray(`
					SELECT eo.outlet_fkid AS outlet_id, e.admin_fkid AS admin_id
					FROM employee_outlet eo
					LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
					LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
					WHERE o.data_status=? 
					AND o.admin_fkid=?
					AND eo.employee_fkid=?
				`, "on", data["admin_fkid"], data["employee_id"])
				if utils.CheckErr(err) {
					return
				}
				var outletIds = make([]string, 0)
				for _, outlet := range dataOutlet {
					outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
				}
				outletAccess := strings.Join(outletIds, ",")

				//create token EMPLOYEE
				if issuer != "" {
					issuer += ".report"
				} else {
					issuer = "E" + cast.ToString(data["employee_id"]) + ".report"
				}
				tokenPayload = map[string]interface{}{
					"user_id":       cast.ToString(data["employee_id"]),
					"user_type":     "employee",
					"admin_id":      cast.ToString(data["admin_fkid"]),
					"outlet_access": outletAccess,
				}
			} else {
				log.Error("password %s INVALID !", email)
			}
		}
	}

	//create token
	if valid {
		jwtToken := auth.InitJWTAuth().GenerateTokenWithRefresh(issuer, tokenPayload)
		//response
		ctx.SetStatusCode(http.StatusOK)
		response = models.ApiResponse{
			Status:  true,
			Code:    200,
			Message: "success",
			Data: map[string]interface{}{
				"authorization": jwtToken,
			},
		}
	}

	//output
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}

// get token using refresh token
func Token(ctx *fasthttp.RequestCtx) {
	tokenId := string(ctx.Request.Header.Peek("token_id"))
	userId := string(ctx.Request.Header.Peek("user_id"))
	userType := string(ctx.Request.Header.Peek("user_type"))
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := ""

	//get outlet access
	if userType == "admin" {
		dataOutlet, err := db.QueryArray(`SELECT outlet_id FROM outlets WHERE data_status=? AND admin_fkid=?`, "on", userId)
		if utils.CheckErr(err) {
			return
		}
		var outletIds = make([]string, 0)
		for _, outlet := range dataOutlet {
			outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
		}
		outletAccess = strings.Join(outletIds, ",")
	} else {
		dataOutlet, err := db.QueryArray(`
					SELECT eo.outlet_fkid AS outlet_id
					FROM employee_outlet eo
					LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
					LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
					WHERE o.data_status=? 
					AND o.admin_fkid=e.admin_fkid
					AND eo.employee_fkid=?
				`, "on", userId)
		if utils.CheckErr(err) {
			return
		}
		var outletIds = make([]string, 0)
		for _, outlet := range dataOutlet {
			outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
		}
		outletAccess = strings.Join(outletIds, ",")
	}

	log.Info("refresh_token validated. create new token [refresh token]...")

	//Generate New Token
	auth := auth.InitJWTAuth()
	authToken := auth.GenerateTokenWithRefresh(tokenId, map[string]interface{}{
		"user_id":       userId,
		"user_type":     userType,
		"admin_id":      adminId,
		"outlet_access": outletAccess,
	})

	ctx.SetContentType("application/json")
	ctx.SetStatusCode(http.StatusOK)
	json.NewEncoder(ctx).Encode(authToken)
}

func TokenByPHPSession(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(http.StatusUnauthorized) //set default response to 401
	sessionID := string(ctx.Request.Header.Peek("sessionID"))
	if sessionID == "" {
		log.Error("empty sessionID on request")
		return
	}

	//get session data
	data, err := db.Query("SELECT *, timestamp+7500 AS timestamp_expired FROM users_session WHERE id=? AND data is not null HAVING timestamp_expired>? ", sessionID, time.Now().Unix())
	utils.CheckErr(err)
	if len(data) <= 0 {
		log.Error("session not found: %s", sessionID)
	} else {
		fmt.Println("get new token with session")
		//get php session data
		sessionRawData := cast.ToString(data["data"])
		sessionData, err := php_session_decoder.NewPhpDecoder(sessionRawData).Decode()
		utils.CheckErr(err)
		if sessionData["user_logged"] == false {
			log.Error("user is not logged in this session")
			return
		} else {
			log.Info("session data: %s", utils.SimplyToJson(sessionData))
			//create token based on session information
			userId := cast.ToString(sessionData["user_id"])
			userType := cast.ToString(sessionData["user_type"])
			adminId := cast.ToString(sessionData["admin_id"])
			dataOutlet := []map[string]interface{}{}

			//ambil data outlets
			if strings.ToLower(userType) == "admin" {
				//ambil data admin
				dataOutlet, err = db.QueryArray("SELECT outlet_id, admin_fkid AS admin_id FROM outlets WHERE admin_fkid=? AND data_status=?", adminId, "on")
				utils.CheckErr(err)
			} else {
				//ambil data employee
				dataOutlet, err = db.QueryArray(`
						SELECT eo.outlet_fkid AS outlet_id, e.admin_fkid AS admin_id
						FROM employee_outlet eo
						LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
						LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
						WHERE o.data_status=? 
						AND o.admin_fkid=?
						AND eo.employee_fkid=?
					`, "on", adminId, userId)
				utils.CheckErr(err)
			}

			var outletIds = make([]string, 0)
			for _, outlet := range dataOutlet {
				outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
			}
			outletAccess := strings.Join(outletIds, ",")

			tokenPayload := map[string]interface{}{
				"user_id":       userId,
				"user_type":     strings.ToLower(userType),
				"admin_id":      adminId,
				"outlet_access": outletAccess,
			}

			//generate token
			jwtToken := auth.InitJWTAuth().GenerateTokenWithRefresh(sessionID+".report", tokenPayload)

			//output
			ctx.SetStatusCode(http.StatusOK)
			json.NewEncoder(ctx).Encode(jwtToken)
		}
	}
}

func TokenByRememberMe(ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(http.StatusUnauthorized) //set default response to 401
	remembermeID := string(ctx.Request.Header.Peek("rememberme"))
	if remembermeID == "" {
		log.Error("Remember ID empty")
		return
	}

	//get rememberme data
	currentTime := time.Now().Unix()
	searchRememberMe := remembermeID + ".rememberme"
	data, err := db.Query("SELECT * FROM users_session WHERE id=? AND timestamp<=? AND expired_at>=?", searchRememberMe, currentTime, currentTime)
	utils.CheckErr(err)
	if len(data) <= 0 {
		log.Error("Remember Me not found: %s", remembermeID)
		return
	} else {
		//validate data
		plainData := remembermeID + "." + cast.ToString(data["data"])
		hashData := cast.ToString(data["token"])
		if utils.CheckPasswordHash(plainData, hashData) == false {
			log.Error("rememberme data is invalid")
		} else {
			dataJson, err := djson.DecodeObject([]byte(cast.ToString(data["data"])))
			utils.CheckErr(err)
			//generate token
			tokenId := remembermeID
			userId := cast.ToString(dataJson["user_id"])
			userType := cast.ToString(dataJson["user_type"])
			adminId := ""
			outletAccess := ""

			//get outlet access
			if userType == "admin" {
				dataOutlet, err := db.QueryArray(`SELECT outlet_id FROM outlets WHERE data_status=? AND admin_fkid=?`, "on", userId)
				if utils.CheckErr(err) {
					return
				}
				var outletIds = make([]string, 0)
				for _, outlet := range dataOutlet {
					outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
				}
				outletAccess = strings.Join(outletIds, ",")
				adminId = userId
			} else {
				dataOutlet, err := db.QueryArray(`
					SELECT eo.outlet_fkid AS outlet_id, e.admin_fkid AS admin_id
					FROM employee_outlet eo
					LEFT JOIN outlets o ON eo.outlet_fkid=o.outlet_id
					LEFT JOIN employee e ON eo.employee_fkid=e.employee_id
					WHERE o.data_status=? 
					AND o.admin_fkid=e.admin_fkid
					AND eo.employee_fkid=?
				`, "on", userId)
				if utils.CheckErr(err) {
					return
				}
				var outletIds = make([]string, 0)
				for _, outlet := range dataOutlet {
					outletIds = append(outletIds, utils.ToString(outlet["outlet_id"]))
				}
				outletAccess = strings.Join(outletIds, ",")
				adminId = cast.ToString(dataOutlet[0]["admin_id"])
			}

			//Generate New Token
			auth := auth.InitJWTAuth()
			authToken := auth.GenerateTokenWithRefresh(tokenId, map[string]interface{}{
				"user_id":       userId,
				"user_type":     userType,
				"admin_id":      adminId,
				"outlet_access": outletAccess,
			})

			ctx.SetContentType("application/json")
			ctx.SetStatusCode(http.StatusOK)
			json.NewEncoder(ctx).Encode(authToken)
		}
	}
}

func TokenRefreshRevoke(ctx *fasthttp.RequestCtx) {
	//set default response to 401
	ctx.SetStatusCode(http.StatusUnauthorized)
	response := models.ApiResponse{
		Status:  false,
		Code:    401,
		Message: "unauthorized",
	}

	tokenId := string(ctx.Request.Header.Peek("token_id"))
	data, err := db.QueryArray("SELECT id FROM users_session WHERE id=?", tokenId)
	utils.CheckErr(err)
	if len(data) > 0 {
		log.Info("remove all refresh token: %s", tokenId)
		_, err := db.Query("DELETE FROM `users_session` WHERE id=?", tokenId)
		utils.CheckErr(err)
		ctx.SetStatusCode(http.StatusOK)
		response = models.ApiResponse{
			Status:  true,
			Code:    200,
			Message: "success",
		}
	} else {
		log.Error("token not found: %s", tokenId)
		ctx.SetStatusCode(http.StatusNotFound)
		response = models.ApiResponse{
			Status:  false,
			Code:    404,
			Message: "notfound",
		}
	}

	//output
	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(response)
}
