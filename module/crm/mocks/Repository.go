// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	crm "gitlab.com/uniqdev/backend/api-report/module/crm"

	mock "github.com/stretchr/testify/mock"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

// FetchCustomerList provides a mock function with given fields: _a0
func (_m *Repository) FetchCustomerList(_a0 domain.User) ([]map[string]interface{}, error) {
	ret := _m.Called(_a0)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.User) []map[string]interface{}); ok {
		r0 = rf(_a0)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchCustomerTransactionList provides a mock function with given fields: user
func (_m *Repository) FetchCustomerTransactionList(user domain.User) ([]map[string]interface{}, error) {
	ret := _m.Called(user)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.User) []map[string]interface{}); ok {
		r0 = rf(user)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User) error); ok {
		r1 = rf(user)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMemberRegisterRange provides a mock function with given fields: adminId, param
func (_m *Repository) FetchMemberRegisterRange(adminId int, param domain.RequestParam) ([]map[string]interface{}, error) {
	ret := _m.Called(adminId, param)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(int, domain.RequestParam) []map[string]interface{}); ok {
		r0 = rf(adminId, param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(int, domain.RequestParam) error); ok {
		r1 = rf(adminId, param)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchSalesMember provides a mock function with given fields: user, request
func (_m *Repository) FetchSalesMember(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	ret := _m.Called(user, request)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.User, domain.RequestParam) []map[string]interface{}); ok {
		r0 = rf(user, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User, domain.RequestParam) error); ok {
		r1 = rf(user, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// QueryParam provides a mock function with given fields: param
func (_m *Repository) QueryParam(param domain.RequestParam) crm.Repository {
	ret := _m.Called(param)

	var r0 crm.Repository
	if rf, ok := ret.Get(0).(func(domain.RequestParam) crm.Repository); ok {
		r0 = rf(param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(crm.Repository)
		}
	}

	return r0
}

type mockConstructorTestingTNewRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewRepository(t mockConstructorTestingTNewRepository) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
