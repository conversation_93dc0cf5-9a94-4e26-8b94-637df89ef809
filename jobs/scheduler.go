package jobs

import (
	"fmt"
	"log"
	"time"

	"github.com/go-co-op/gocron"
	"gitlab.com/uniqdev/backend/api-report/controller/stock"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
)

type scheduler struct {
	sync  domain.SyncUseCase
	hpp   hpp.UseCase
	sales domain.SalesUseCase
}

func NewScheduler(sync domain.SyncUseCase, hpp hpp.UseCase, sales domain.SalesUseCase) scheduler {
	return scheduler{sync: sync, hpp: hpp, sales: sales}
}

func (s scheduler) RunJob() {
	//run pubsub job
	s.RunSubscription()

	//somehow, this is still not working in server
	loc, err := time.LoadLocation("Asia/Jakarta")

	//for jakarta time
	timeSyncDataToBigQuery := "04:00"
	timeSyncUpdateMaxTime := "06:00"
	timeCheckAnomalyHpp := "04:30"
	timeWeeklyReport := "08:00"

	if err != nil {
		fmt.Println("can not define custom location: ", err)
		loc = time.UTC

		timeSyncDataToBigQuery = "21:00"
		timeSyncUpdateMaxTime = "23:00"
		timeCheckAnomalyHpp = "21:30"
		timeWeeklyReport = "01:00"
	}

	schedule := gocron.NewScheduler(loc)
	_, err = schedule.Every(1).Day().At("00:45").Do(stock.SendDailyStockReport)
	if err != nil {
		log.Fatalf("error creating job: %v", err)
	}

	_, err = schedule.Every(1).Day().At(timeSyncDataToBigQuery).Do(s.sync.SyncDataToBigQuery)
	if err != nil {
		log.Fatalf("error creating job: %v", err)
	}

	_, err = schedule.Every(1).Day().At(timeSyncUpdateMaxTime).Do(s.sync.UpdateMaxTimeSync)
	if err != nil {
		log.Fatalf("error creating job: %v", err)
	}

	_, err = schedule.Every(1).Day().At(timeCheckAnomalyHpp).Do(s.hpp.CheckAnomalyHpp)
	if err != nil {
		log.Fatalf("error creating job: %v", err)
	}
	schedule.Every(1).Monday().At(timeWeeklyReport).Do(s.sales.RunWeeklyReportGenerator)

	schedule.StartAsync()
}
