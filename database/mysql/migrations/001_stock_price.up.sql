CREATE TABLE `stock_price` (
  `stock_price_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `product_detail_fkid` int(11) NOT NULL,
  `stock_in` float NOT NULL DEFAULT '0',
  `stock_out` float NOT NULL DEFAULT '0',
  `price` float NOT NULL DEFAULT '0',
  `stock_in_id` varchar(10) DEFAULT NULL,
  `stock_in_source` varchar(35) DEFAULT NULL,
  `updated_at` bigint(20) NOT NULL DEFAULT '0',
  `created_at` bigint(20) NOT NULL DEFAULT '0',
  `deleted_at` bigint(20) DEFAULT NULL,
  `meta_data` json DEFAULT NULL,
  PRIMARY KEY (`stock_price_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;