package bigquery

import (
	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	cast2 "gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/stock_card/models"
	"strings"
)

type stockCardRepository struct {
	bq.Repository
}

func NewBigQueryStockCardRepository(client *bigquery.Client) domain.StockCardRepositoryBigquery {
	return &stockCardRepository{bq.Repository{Client: client}}
}

func (s stockCardRepository) FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
			    '' AS parent_id,
			    sb.time_created AS data_created,
			    sb.sales_breakdown_id AS id,
			    '' AS admin_fkid,
			    'sales_breakdown' AS data_source,
			    sb.qty_total AS qty,
			    -(sb.qty_total) AS calculate,
			    0 AS stock_in,
			    sb.qty_total AS stock_out,
			    0 AS opname,
			    '' AS operator
			FROM
			    $DB.sales_breakdown sb
			WHERE
			    sb.status = ?
			    AND sb.product_detail_fkid = ?
			    AND sb.time_created >= ?
			    AND sb.time_created <= ?`
	params := make([]interface{}, 0)
	params = append(params, "success", productdetailId, timeStart, timeEnd)

	sql = strings.Replace(sql, "$DB.", s.DbName(), -1)
	sql = db.GetSQLRaw(sql, params...)
	result, err := s.Query(sql).MapArray()
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepository) FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s stockCardRepository) FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	//TODO implement me
	panic("implement me")
}
