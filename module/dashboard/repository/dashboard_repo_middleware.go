package repository

import (
	"database/sql"
	"fmt"

	bigquery2 "cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository/bigquery"
	mysql2 "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository/mysql"
)

var maxTime = int64(0)

type dashboardRepository struct {
	mysqlRepo domain.DashboardRepository
	bqRepo    domain.DashboardRepository
}

func NewMiddlewareDashboardRepository(db *sql.DB, client *bigquery2.Client, cache domain.CacheInterface) domain.DashboardRepository {
	bq := bigquery.NewBigQueryDashboardRepository(client)
	my := mysql2.NewMysqlDashboardRepository(db, cache)
	go SetMaxTime(client)
	return &dashboardRepository{my, bq}
}

func GetMaxTimeSync() int64 {
	return maxTime
}

func SetMaxTime(client *bigquery2.Client) {
	if client == nil {
		fmt.Println("bigquery client is null")
		return
	}

	repo := bq.Repository{
		Client: client,
	}

	data, err := repo.Query("SELECT max(time_modified) as max_time FROM " + repo.DbName() + "sales ").Map()
	fmt.Println("max sales: ", data)
	if err == nil {
		if max := utils.ToInt(data["max_time"]); max > 0 {
			maxTime = int64(max)
		}
	}
}

func (d dashboardRepository) FetchNetSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchNetSalesBySales uses big query")
		res, err := d.bqRepo.FetchNetSalesBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchNetSalesBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchNetSalesByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchNetSalesByShift(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchPaidPurchaseBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchPaidPurchaseBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchUnPaidPurchase(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchUnPaidPurchase(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDebtPayment(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchDebtPayment(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDebt(adminId string, timeStart int, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchDebt(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchComplimentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchComplimentBySales uses bigquery")
		res, err := d.bqRepo.FetchComplimentBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	fmt.Println("FetchComplimentBySales uses mysql ", timeEnd, maxTime, timeEnd < maxTime)
	return d.mysqlRepo.FetchComplimentBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDutyMealsBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchDutyMealsBySales uses bigquery")
		res, err := d.bqRepo.FetchDutyMealsBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchDutyMealsBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchTotalSalesPerPaymentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, paymentMethods ...string) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		res, err := d.bqRepo.FetchTotalSalesPerPaymentBySales(adminId, timeStart, timeEnd, outletIds, paymentMethods...)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchTotalSalesPerPaymentBySales(adminId, timeStart, timeEnd, outletIds, paymentMethods...)
}

func (d dashboardRepository) FetchFreeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchFreeBySales uses bigquery")
		res, err := d.bqRepo.FetchFreeBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchFreeBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDiscSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchDiscSalesBySales uses bigquery")
		res, err := d.bqRepo.FetchDiscSalesBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchDiscSalesBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDiscItemBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchDiscItemBySales uses bigquery")
		res, err := d.bqRepo.FetchDiscItemBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchDiscItemBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchDiscGratuityBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchDiscGratuityBySales uses bigquery")
		res, err := d.bqRepo.FetchDiscGratuityBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchDiscGratuityBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchVoucherBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchVoucherBySales uses bigquery")
		res, err := d.bqRepo.FetchVoucherBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchVoucherBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchVoucherSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		fmt.Println("FetchVoucherSalesBySales uses bigquery")
		res, err := d.bqRepo.FetchVoucherSalesBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchVoucherSalesBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchPendingIncomeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//if timeEnd < maxTime {
	//	res, err := d.bqRepo.FetchPendingIncomeBySales(adminId, timeStart, timeEnd, outletIds)
	//	if err == nil {
	//		return res, nil
	//	}
	//}
	return d.mysqlRepo.FetchPendingIncomeBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchTotalSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	if timeEnd < maxTime {
		res, err := d.bqRepo.FetchTotalSalesBySales(adminId, timeStart, timeEnd, outletIds)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchTotalSalesBySales(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchItemSalesGroupSubCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchItemSalesGroupSubCategory(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchItemSalesGroupCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchItemSalesGroupCategory(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchItemSalesGroupType(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchItemSalesGroupType(adminId, timeStart, timeEnd, outletIds)
}

func (d dashboardRepository) FetchSalesByMedia(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	if cast.ToInt64(param.TimeEnd) < maxTime {
		res, err := d.bqRepo.FetchSalesByMedia(param, user)
		if err == nil {
			return res, nil
		}
	}
	return d.mysqlRepo.FetchSalesByMedia(param, user)
}

func (d dashboardRepository) FetchBankById(id ...interface{}) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchBankById(id...)
}

// FetchTopSales implements domain.DashboardRepository.
func (d *dashboardRepository) FetchTopSales(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	return d.mysqlRepo.FetchTopSales(param, user)
}
