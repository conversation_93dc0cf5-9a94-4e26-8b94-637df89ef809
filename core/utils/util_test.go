package utils

import "testing"

func TestConcatData(t *testing.T) {
	type args struct {
		data      interface{}
		separator string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{"test-slice-interface", args{[]interface{}{1,2,"3"}, "_"}, "1_2_3"},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ConcatData(tt.args.data, tt.args.separator); got != tt.want {
				t.Errorf("ConcatData() = %v, want %v", got, tt.want)
			}
		})
	}
}
