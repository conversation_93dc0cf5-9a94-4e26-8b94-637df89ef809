package transform

import (
	"reflect"
	"testing"
)

func TestToIntArray(t *testing.T) {
	type args struct {
		data string
	}
	tests := []struct {
		name string
		args args
		want []int
	}{
		{"test", args{"[[134]]"}, []int{134}},
		{"test2", args{"[[134],[142,156]]"}, []int{134, 142, 156}},
		{"test-unique", args{"[[134],[142,134]]"}, []int{134, 142}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ToIntArray(tt.args.data); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>rf("ToIntArray() = %v, want %v", got, tt.want)
			}
		})
	}
}
