package v1

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
)

func sqlItemSold(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, itemIds []interface{}, orderBy interface{}, limit interface{}) string {
	rawSQL := ""

	//create where clause outlet_id IN (...)
	outletId := ""
	whereInClauseItemVoid := ""
	whereInClauseItemSold := ""
	if outletIds != nil {
		outletId = strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInClauseItemVoid = " AND ssv.outlet_fkid IN ( " + outletId + ")"
		whereInClauseItemSold = " AND s.outlet_fkid IN (" + outletId + ")"
	}

	//create where clasuse product_id IN (...)
	itemId := ""
	itemSoldHavingInClause := ""
	if itemIds != nil {
		itemId = strings.Join(cast.ToStringSlice(itemIds), "','")
		itemSoldHavingInClause = " AND IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) IN ('" + itemId + "') "
	}

	//order query
	orderByQuery := ""
	if orderBy != nil && cast.ToString(orderBy) != "" {
		orderByQuery = "ORDER BY " + cast.ToString(orderBy)
	}

	//limit
	limitQuery := ""
	if limit != nil {
		limitQuery = "LIMIT " + cast.ToString(limit)
	}

	//get item void
	queryItemVoid := db.GetSQLRaw(""+
		//queryItemVoid, err := db.QueryArray(""+
		"SELECT "+
		"IF(pdsv.variant_fkid IS NULL, pdsv.product_fkid, concat(pdsv.product_fkid,'-',pdsv.variant_fkid)) AS item_id,"+
		"sum(sv.qty) AS qty,"+
		"sum(sv.price) AS price,"+
		"sum(sv.sub_total + sv.discount) AS subtotal_void "+
		"FROM sales_void sv "+
		"LEFT JOIN products_detail pdsv ON sv.product_detail_fkid = pdsv.product_detail_id "+
		"LEFT JOIN sales ssv ON sv.sales_fkid = ssv.sales_id "+
		"WHERE sv.time_created >= ? "+
		"AND sv.time_created <= ? "+
		"AND ssv.status = 'success' "+
		"AND ssv.data_status = 'on' "+
		"AND ssv.payment NOT IN ('COMPLIMENT','DUTY MEALS') "+
		whereInClauseItemVoid+" "+
		"GROUP BY pdsv.product_fkid, pdsv.variant_fkid", timeStart, timeEnd)
	//utils.CheckErr(err)
	//fmt.Println(queryItemVoid)

	//get item sold
	rawSQL = db.GetSQLRaw("SELECT "+
		"IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) AS item_id,"+
		"IF(pd.variant_fkid IS NULL, p.name, concat(p.name,' (',v.variant_name,')')) AS item_name,"+
		"sum(sd.qty) AS qty_buy,"+
		"sum((sd.sub_total) - sd.discount) AS subtotal_buy,"+
		"IFNULL(jsv.qty,0) AS qty_void,"+
		"IFNULL(jsv.subtotal_void,0) AS subtotal_void,"+
		"sum(sd.qty) - IFNULL(jsv.qty,0) AS qty,"+
		"sum((sd.sub_total) - sd.discount) + IFNULL(jsv.subtotal_void,0) AS subtotal "+
		"FROM sales_detail sd "+
		"LEFT JOIN sales s ON s.sales_id = sd.sales_fkid "+
		"LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id "+
		"LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id "+
		"LEFT JOIN products p ON p.product_id = pd.product_fkid "+
		"LEFT JOIN products_detail_variant v ON pd.variant_fkid = v.variant_id "+
		"LEFT JOIN ("+queryItemVoid+") jsv ON jsv.item_id = IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) "+
		"WHERE o.admin_fkid = ? "+
		"AND s.time_created >= ? "+
		"AND s.time_created <= ? "+
		"AND s.status = 'success' "+
		"AND s.data_status = 'on' "+
		"AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS') "+
		whereInClauseItemSold+
		itemSoldHavingInClause+
		"GROUP BY pd.product_fkid, pd.variant_fkid, jsv.subtotal_void, jsv.qty "+
		orderByQuery+" "+
		limitQuery, adminId, timeStart, timeEnd)

	return rawSQL
}

func SalesTop(ctx *fasthttp.RequestCtx) {
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	timeStartPrev := cast.ToInt64(post["time_start_previous"])
	timeEndPrev := cast.ToInt64(post["time_end_previous"])
	outletIds := cast.ToSlice(post["outlet_ids"])
	//limit := post["limit"]
	limit := ctx.UserValue("limit")

	//get top item on range data
	//-- qty
	chanTopQty := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, qty " +
			"FROM (" + sqlItemSold(adminId, timeStart, timeEnd, outletIds, nil, "qty DESC, item_name ASC", limit) + ") tb")
		utils.CheckErr(err)
		chanTopQty <- query
	}()
	//-- nominal
	chanTopNominal := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, subtotal " +
			"FROM (" + sqlItemSold(adminId, timeStart, timeEnd, outletIds, nil, "subtotal DESC, qty DESC, item_name ASC", limit) + ") tb")
		utils.CheckErr(err)
		chanTopNominal <- query
	}()
	//----- tampung
	topQtyResult := <-chanTopQty
	topNominalResult := <-chanTopNominal

	//catch item_id to get data on previous range (with go routine)
	//timeStartX := time.Now()
	//-- item_ids for qty
	chanItemIdsQty := make(chan []interface{})
	go func() {
		var itemIdsQty []interface{}
		for _, data := range topQtyResult {
			itemIdsQty = append(itemIdsQty, data["item_id"])
		}
		chanItemIdsQty <- itemIdsQty
	}()
	//-- item_ids for nominal
	chanItemIdsNominal := make(chan []interface{})
	go func() {
		var itemIdsNominal []interface{}
		for _, data := range topNominalResult {
			itemIdsNominal = append(itemIdsNominal, data["item_id"])
		}
		chanItemIdsNominal <- itemIdsNominal
	}()
	//----- tampung
	itemIdsQty := <-chanItemIdsQty
	itemIdsNominal := <-chanItemIdsNominal
	//fmt.Printf("go: catch item_id took : %v\n", time.Since(timeStartX))

	//get data on previous range by on range item_id
	//-- qty
	chanTopQtyPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, qty FROM (" + sqlItemSold(adminId, timeStartPrev, timeEndPrev, outletIds, itemIdsQty, "", limit) + ") tb")
		utils.CheckErr(err)
		chanTopQtyPrevious <- queryPrevious
	}()
	//-- nominal
	chanTopNominalPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, subtotal FROM (" + sqlItemSold(adminId, timeStartPrev, timeEndPrev, outletIds, itemIdsNominal, "", limit) + ") tb")
		utils.CheckErr(err)
		chanTopNominalPrevious <- queryPrevious
	}()
	//----- tampung
	topQtyResultPrevious := <-chanTopQtyPrevious
	topNominalResultPrevious := <-chanTopNominalPrevious

	//simpan hasil data previous ke map
	timeStartBenchmark := time.Now()
	//v1 (go for)
	//*
	//-- qty
	chanTopQtyPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topQtyResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["qty"]
		}
		chanTopQtyPreviousMap <- temp
	}()
	//-- nominal
	chanTopNominalPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topNominalResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["subtotal"]
		}
		chanTopNominalPreviousMap <- temp
	}()
	//-----catch go return
	topQtyPreviousMap := <-chanTopQtyPreviousMap
	topNominalPreviousMap := <-chanTopNominalPreviousMap
	//*/

	//v2 (for go wait groups)
	/*
		var wg sync.WaitGroup
		var lock sync.Mutex

		topQtyPreviousMap := map[string]interface{}{}
		for i, data := range topQtyResultPrevious {
			wg.Add(1)
			go func(i int, data map[string]interface{}) {
				defer lock.Unlock()
				defer wg.Done()
				lock.Lock()
				id := cast.String(data["item_id"])
				topQtyPreviousMap[id] = data["qty"]
			}(i, data)
		}

		topNominalPreviousMap := map[string]interface{}{}
		for i, data := range topNominalResultPrevious {
			wg.Add(1)
			go func(i int, data map[string]interface{}) {
				defer lock.Unlock()
				defer wg.Done()
				lock.Lock()
				id := cast.String(data["item_id"])
				topNominalPreviousMap[id] = data["subtotal"]
			}(i, data)
		}
		wg.Wait()
	*/

	//v3 (only for)
	/*
		topQtyPreviousMap := map[string]interface{}{}
		for _, data := range topQtyResultPrevious {
			id := cast.String(data["item_id"])
			topQtyPreviousMap[id] = data["qty"]
		}

		topNominalPreviousMap := map[string]interface{}{}
		for _, data := range topNominalResultPrevious {
			id := cast.String(data["item_id"])
			topNominalPreviousMap[id] = data["subtotal"]
		}
	*/

	fmt.Printf("item_id to map took : %v\n", time.Since(timeStartBenchmark))

	//re-mappind data untuk dijadikan json
	chanTopQtyJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topQtyResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topQtyPreviousMap[id] != nil {
				prev = cast.ToString(topQtyPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id":   id,
				"item_name": data["item_name"],
				"now":       data["qty"],
				"prev":      prev,
			}
			temp = append(temp, temp2)

			//sort by time prev desc if now ==
			if i > 0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopQtyJson <- temp
	}()

	chanTopNominalJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topNominalResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topNominalPreviousMap[id] != nil {
				prev = cast.ToString(topNominalPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id":   id,
				"item_name": data["item_name"],
				"now":       data["subtotal"],
				"prev":      prev,
			}
			temp = append(temp, temp2)

			//sort by time prev desc if now ==
			if i > 0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopNominalJson <- temp
	}()

	//topQtyJson := <-chanTopQtyJson
	//topNominalJson := <-chanTopNominalJson
	topQtyJson := []map[string]interface{}{}
	if q1 := <-chanTopQtyJson; q1 != nil {
		fmt.Println("qty tidak kosong")
		topQtyJson = q1
	}

	topNominalJson := []map[string]interface{}{}
	if q2 := <-chanTopNominalJson; q2 != nil {
		fmt.Println("nom tidak kosong")
		topNominalJson = q2
	}

	//render semua data
	response := map[string]interface{}{
		"nominal": topNominalJson,
		"qty":     topQtyJson,
	}

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(response)
}

func SalesTop_map(adminId string, timeStart, timeEnd, timeStartPrev, timeEndPrev int64, outletIds []interface{}, limit interface{}) map[string]interface{} {
	if len(outletIds) == 0 {
		return map[string]interface{}{}
	}

	//get top item on range data
	//-- qty
	chanTopQty := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, qty " +
			"FROM (" + sqlItemSold(adminId, timeStart, timeEnd, outletIds, nil, "qty DESC, item_name ASC", limit) + ") tb")
		utils.CheckErr(err)
		chanTopQty <- query
	}()
	//-- nominal
	chanTopNominal := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, subtotal " +
			"FROM (" + sqlItemSold(adminId, timeStart, timeEnd, outletIds, nil, "subtotal DESC, qty DESC, item_name ASC", limit) + ") tb")
		utils.CheckErr(err)
		chanTopNominal <- query
	}()
	//----- tampung
	topQtyResult := <-chanTopQty
	topNominalResult := <-chanTopNominal

	//catch item_id to get data on previous range (with go routine)
	//timeStartX := time.Now()
	//-- item_ids for qty
	chanItemIdsQty := make(chan []interface{})
	go func() {
		var itemIdsQty []interface{}
		for _, data := range topQtyResult {
			itemIdsQty = append(itemIdsQty, data["item_id"])
		}
		chanItemIdsQty <- itemIdsQty
	}()
	//-- item_ids for nominal
	chanItemIdsNominal := make(chan []interface{})
	go func() {
		var itemIdsNominal []interface{}
		for _, data := range topNominalResult {
			itemIdsNominal = append(itemIdsNominal, data["item_id"])
		}
		chanItemIdsNominal <- itemIdsNominal
	}()
	//----- tampung
	itemIdsQty := <-chanItemIdsQty
	itemIdsNominal := <-chanItemIdsNominal
	//fmt.Printf("go: catch item_id took : %v\n", time.Since(timeStartX))

	//get data on previous range by on range item_id
	//-- qty
	chanTopQtyPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, qty FROM (" + sqlItemSold(adminId, timeStartPrev, timeEndPrev, outletIds, itemIdsQty, "", limit) + ") tb")
		utils.CheckErr(err)
		chanTopQtyPrevious <- queryPrevious
	}()
	//-- nominal
	chanTopNominalPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, subtotal FROM (" + sqlItemSold(adminId, timeStartPrev, timeEndPrev, outletIds, itemIdsNominal, "", limit) + ") tb")
		utils.CheckErr(err)
		chanTopNominalPrevious <- queryPrevious
	}()
	//----- tampung
	topQtyResultPrevious := <-chanTopQtyPrevious
	topNominalResultPrevious := <-chanTopNominalPrevious

	//simpan hasil data previous ke map
	timeStartBenchmark := time.Now()
	//v1 (go for)
	//*
	//-- qty
	chanTopQtyPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topQtyResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["qty"]
		}
		chanTopQtyPreviousMap <- temp
	}()
	//-- nominal
	chanTopNominalPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topNominalResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["subtotal"]
		}
		chanTopNominalPreviousMap <- temp
	}()
	//-----catch go return
	topQtyPreviousMap := <-chanTopQtyPreviousMap
	topNominalPreviousMap := <-chanTopNominalPreviousMap
	//*/

	//v2 (for go wait groups)
	/*
		var wg sync.WaitGroup
		var lock sync.Mutex

		topQtyPreviousMap := map[string]interface{}{}
		for i, data := range topQtyResultPrevious {
			wg.Add(1)
			go func(i int, data map[string]interface{}) {
				defer lock.Unlock()
				defer wg.Done()
				lock.Lock()
				id := cast.String(data["item_id"])
				topQtyPreviousMap[id] = data["qty"]
			}(i, data)
		}

		topNominalPreviousMap := map[string]interface{}{}
		for i, data := range topNominalResultPrevious {
			wg.Add(1)
			go func(i int, data map[string]interface{}) {
				defer lock.Unlock()
				defer wg.Done()
				lock.Lock()
				id := cast.String(data["item_id"])
				topNominalPreviousMap[id] = data["subtotal"]
			}(i, data)
		}
		wg.Wait()
	*/

	//v3 (only for)
	/*
		topQtyPreviousMap := map[string]interface{}{}
		for _, data := range topQtyResultPrevious {
			id := cast.String(data["item_id"])
			topQtyPreviousMap[id] = data["qty"]
		}

		topNominalPreviousMap := map[string]interface{}{}
		for _, data := range topNominalResultPrevious {
			id := cast.String(data["item_id"])
			topNominalPreviousMap[id] = data["subtotal"]
		}
	*/

	fmt.Printf("item_id to map took : %v\n", time.Since(timeStartBenchmark))

	//re-mappind data untuk dijadikan json
	chanTopQtyJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topQtyResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topQtyPreviousMap[id] != nil {
				prev = cast.ToString(topQtyPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id":   id,
				"item_name": data["item_name"],
				"now":       data["qty"],
				"prev":      prev,
			}
			temp = append(temp, temp2)

			//sort by time prev desc if now ==
			if i > 0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopQtyJson <- temp
	}()

	chanTopNominalJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topNominalResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topNominalPreviousMap[id] != nil {
				prev = cast.ToString(topNominalPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id":   id,
				"item_name": data["item_name"],
				"now":       data["subtotal"],
				"prev":      prev,
			}
			temp = append(temp, temp2)

			//sort by time prev desc if now ==
			if i > 0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopNominalJson <- temp
	}()

	//topQtyJson := <-chanTopQtyJson
	//topNominalJson := <-chanTopNominalJson
	topQtyJson := []map[string]interface{}{}
	if q1 := <-chanTopQtyJson; q1 != nil {
		fmt.Println("qty tidak kosong")
		topQtyJson = q1
	}

	topNominalJson := []map[string]interface{}{}
	if q2 := <-chanTopNominalJson; q2 != nil {
		fmt.Println("nom tidak kosong")
		topNominalJson = q2
	}

	//render semua data
	response := map[string]interface{}{
		"nominal": topNominalJson,
		"qty":     topQtyJson,
	}

	return response
}

func SalesTop_HTTP(ctx *fasthttp.RequestCtx) {
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	timeStartPrev := cast.ToInt64(post["time_start_previous"])
	timeEndPrev := cast.ToInt64(post["time_end_previous"])
	outletIds := cast.ToSlice(post["outlet_ids"])
	limit := post["limit"]

	//render semua data
	response := SalesTop_map(adminId, timeStart, timeEnd, timeStartPrev, timeEndPrev, outletIds, limit)

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(response)
}
