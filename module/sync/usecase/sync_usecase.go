package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/google"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type syncUseCase struct {
	repo domain.SyncRepository
}

func NewSyncUseCase(repository domain.SyncRepository) domain.SyncUseCase {
	return &syncUseCase{repository}
}

func (s syncUseCase) SyncDataToBigQuery() {
	if os.Getenv("ENV") != "staging" {
		log.Info("we're not performing sync to bg in env %s", os.Getenv("ENV"))
		return
	}

	fmt.Println("sync to big query...")
	// _ = s.syncSalesDetailToBigQuery()
	// _ = s.syncOpenShift()
	// _ = s.syncSalesVoidToBigQuery()
	// _ = s.syncSalesTax()
	// _ = s.syncSalesDetailTaxToBigQuery()
	// _ = s.syncSalesDetailDiscountToBigQuery()
	// _ = s.syncSalesToBigQuery()
	// _ = s.syncSalesPromotionToBigQuery()
	fmt.Println("finished sync to big query...")
}

func (s syncUseCase) UpdateMaxTimeSync() {
	s.repo.UpdateMaxTimeSync()
}

func (s syncUseCase) syncSalesDetailToBigQuery() error {
	//get last sync
	lastId, err := s.repo.FetchLastSalesDetailIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	//fetch salesDetail from our db
	salesDetail, err := s.repo.FetchSalesDetailAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(salesDetail, "sales_detail")
}

func (s syncUseCase) syncOpenShift() error {
	//get last sync
	lastSync, err := s.repo.FetchLastSyncOpenShiftFromBigQuery()
	if log.IfError(err) {
		return err
	}

	log.Info("last sync open shift: %v", lastSync)

	//fetch openShift from our db
	openShift, err := s.repo.FetchOpenShiftAfter(lastSync)
	if log.IfError(err) {
		return err
	}

	log.Info("new data open shift after %v is : %d", lastSync, len(openShift))
	return saveToStorage(openShift, "open_shift")
}

func (s syncUseCase) syncSalesToBigQuery() error {
	//get last sync
	lastSync, err := s.repo.FetchLastSyncSalesFromBigQuery()
	if log.IfError(err) {
		return err
	}

	//fetch sales from our db
	sales, err := s.repo.FetchSalesAfter(lastSync)
	if log.IfError(err) {
		return err
	}

	log.Info("last sales in bq: %v | total new sales: %d", lastSync, len(sales))
	return saveToStorage(sales, "sales")
}

func (s syncUseCase) syncSalesVoidToBigQuery() error {
	//get last sync
	lastId, err := s.repo.FetchLastVoidIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	//fetch salesVoid from our db
	salesVoid, err := s.repo.FetchSalesVoidAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(salesVoid, "sales_void")
}

func (s syncUseCase) syncSalesTax() error {
	//get last sync
	lastId, err := s.repo.FetchLastSalesTaxIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	//fetch sales from our db
	sales, err := s.repo.FetchSalesTaxAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(sales, "sales_tax")
}

func (s syncUseCase) syncSalesDetailTaxToBigQuery() error {
	lastId, err := s.repo.FetchLastSalesDetailTaxIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	salesDetailTax, err := s.repo.FetchSalesDetailTaxAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(salesDetailTax, "sales_detail_tax")
}

func (s syncUseCase) syncSalesDetailDiscountToBigQuery() error {
	lastId, err := s.repo.FetchLastSalesDetailDiscIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	data, err := s.repo.FetchSalesDetailDiscAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(data, "sales_detail_discount")
}

func (s syncUseCase) syncSalesPromotionToBigQuery() error {
	lastId, err := s.repo.FetchLastSalesPromotionIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	data, err := s.repo.FetchSalesPromotionAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	err = saveToStorage(data, "sales_promotion")
	if err != nil {
		return err
	}

	lastId, err = s.repo.FetchLastSalesDetailPromotionIdFromBigQuery()
	if log.IfError(err) {
		return err
	}

	data, err = s.repo.FetchSalesDetailPromotionAfterId(lastId)
	if log.IfError(err) {
		return err
	}

	return saveToStorage(data, "sales_detail_promotion")
}

func saveToStorage(data []map[string]interface{}, tableName string) error {
	if len(data) == 0 {
		fmt.Println("no new data for ", tableName)
		return nil
	}

	log.Info("add %d data of %s", len(data), tableName)
	jsonRow := make([]string, 0)

	for _, row := range data {
		dataJson, err := json.Marshal(row)
		if log.IfError(err) {
			return err
		}

		jsonRow = append(jsonRow, string(dataJson))
	}

	dataJson := strings.Join(jsonRow, "\n")

	//save it to file
	filePath := fmt.Sprintf("%s.json", tableName)
	if err := os.WriteFile(filePath, []byte(dataJson), 0644); log.IfError(err) {
		log.Info("saving file of %s failed: %v", tableName, err)
		return err
	}

	destinationPath := fmt.Sprintf("uniq-asia-souteast1/database/%s/realtime", os.Getenv("ENV"))

	//update to gcp storage
	err := google.UploadFile(filePath, destinationPath)
	log.IfError(err)
	return err
}
