package v1

import (
	"encoding/json"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"net/http"
	"strings"
)

func querySalesByCategoryByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) string {
	rawSql := ""

	//create where clause outlet_id IN (...)
	outletId := ""
	whereInClauseItemVoid := ""
	whereInClauseItemSold := ""
	if outletIds != nil {
		outletId = strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInClauseItemVoid = " AND ssv.outlet_fkid IN ( " +outletId+ " )"
		whereInClauseItemSold = " AND s.outlet_fkid IN (" +outletId+ ")"
	}


	queryTableItemVoid := db.GetSQLRaw("" +
		"SELECT " +
		"IF(pdsv.variant_fkid IS NULL, pdsv.product_fkid, concat(pdsv.product_fkid,'-',pdsv.variant_fkid)) AS item_id," +
		"sum(sv.qty) AS qty," +
		"sum(sv.price) AS price," +
		"sum((sv.qty * sv.price) + sv.discount) AS sub_total_void " +
		"FROM sales_void sv " +
		"LEFT JOIN products_detail pdsv ON sv.product_detail_fkid = pdsv.product_detail_id " +
		"LEFT JOIN sales ssv ON sv.sales_fkid = ssv.sales_id " +
		"WHERE " +
		"sv.time_created >= ? AND " +
		"sv.time_created <= ? AND " +
		"ssv.status = 'success' AND " +
		"ssv.data_status = 'on' AND " +
		"ssv.payment NOT IN ('COMPLIMENT','DUTY MEALS') " + whereInClauseItemVoid + " " +
		"GROUP BY pdsv.product_fkid, pdsv.variant_fkid", timeStart, timeEnd)

	queryTableItemSold := db.GetSQLRaw("SELECT " +
		"IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) AS item_id," +
		"IF(pd.variant_fkid IS NULL, p.name, concat(p.name,' (',v.variant_name,')')) AS item_name," +
		"pt.products_type_id AS type_id," +
		"pt.name AS type_name," +
		"pc.product_category_id AS category_id," +
		"pc.name AS category_name," +
		"pcs.product_subcategory_id AS subcategory_id," +
		"pcs.name AS subcategory_name," +
		"sum(sd.qty) AS qty," +
		"sum((sd.qty * sd.price) - sd.discount) AS sub_total_sold," +
		"IFNULL(jsv.qty,0) AS qty_void," +
		"IFNULL(jsv.sub_total_void,0) AS sub_total_void," +
		"sum(sd.qty) - IFNULL(jsv.qty,0) AS qty_net," +
		"sum((sd.qty * sd.price) - sd.discount) + IFNULL(jsv.sub_total_void,0) AS sub_total_net " +
		"FROM sales_detail sd " +
		"LEFT JOIN sales s ON s.sales_id = sd.sales_fkid " +
		"LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id " +
		"LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id " +
		"LEFT JOIN products p ON p.product_id = pd.product_fkid " +
		"LEFT JOIN products_detail_variant v ON pd.variant_fkid = v.variant_id " +
		"LEFT JOIN products_type pt ON pt.products_type_id = p.product_type_fkid " +
		"LEFT JOIN products_category pc ON pc.product_category_id = p.product_category_fkid " +
		"LEFT JOIN products_subcategory pcs ON pcs.product_subcategory_id = p.product_subcategory_fkid " +
		"LEFT JOIN ("+queryTableItemVoid+") jsv ON jsv.item_id = IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) " +
		"LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid " +
		"WHERE o.admin_fkid=? " +
		"AND os.time_open >=? " +
		"AND os.time_open <=? " +
		"AND s.status='success' AND " +
		"s.data_status='on' AND " +
		"s.payment NOT IN ('COMPLIMENT','DUTY MEALS') " +whereInClauseItemSold+
		"GROUP BY pd.product_fkid, pd.variant_fkid, jsv.sub_total_void,jsv.qty", adminId, timeStart,timeEnd)

	rawSql = queryTableItemSold
	return rawSql
}

func SalesByAllCategoryByShift_map(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) map[string]interface{} {

	rawSqlSalesByCategory := querySalesByCategoryByShift(adminId,timeStart,timeEnd,outletIds)
	chanSalesByType := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT type_id AS id, type_name AS name, sum(sub_total_net) AS qty " +
			"FROM ("+rawSqlSalesByCategory+") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByType <- query
	}()

	chanSalesByCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT category_id AS id, category_name AS name, sum(sub_total_net) AS qty " +
			"FROM ("+rawSqlSalesByCategory+") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesByCategory <- query
	}()

	chanSalesBySubCategory := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("" +
			"SELECT subcategory_id AS id, subcategory_name AS name, sum(sub_total_net) AS qty " +
			"FROM ("+rawSqlSalesByCategory+") tb " +
			"GROUP BY id ORDER BY qty DESC")
		utils.CheckErr(err)
		chanSalesBySubCategory <- query
	}()

	dataSalesType := <- chanSalesByType
	dataSalesCategory := <- chanSalesByCategory
	dataSalesSubCategory := <- chanSalesBySubCategory


	response := map[string]interface{}{
		"type": dataSalesType,
		"category": dataSalesCategory,
		"subcategory": dataSalesSubCategory,
	}

	return response
}

func SalesCategoryByShift_v1(ctx *fasthttp.RequestCtx)  {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  false,
			Code:    400,
			Data:    err,
		})
	}else{
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess,",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		response := SalesByAllCategoryByShift_map(adminId, timeStart, timeEnd, outletIds)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  true,
			Code:    200,
			Data:    response,
		})
	}
}
