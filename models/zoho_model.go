package models

type ZohoMail struct {
	To          string
	Subject     string
	Content     string
	Attachments []string
}

type ZohoAttachment struct {
	Status Status `json:"status"`
	Data   Data   `json:"data"`
}

type Status struct {
	Code        int    `json:"code"`
	Description string `json:"description"`
}

type Data struct {
	StoreName      string `json:"storeName"`
	AttachmentPath string `json:"attachmentPath"`
	AttachmentName string `json:"attachmentName"`
}
