package models

type SalesDetail struct {
	SalesDetailID     int     `json:"sales_detail_id,omitempty"`
	ProductDetailFkid int     `json:"product_detail_fkid,omitempty"`
	Qty               float32 `json:"qty,omitempty"`
	CreatedAt         int64
}

func (s *SalesDetail) ToStockOutProduct() StockOutProduct {
	return StockOutProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.Qty,
		MetaData: map[string]interface{}{
			"sales_detail_id": s.SalesDetailID,
		},
	}
}

func (s *SalesDetail) ToStockInProduct() StockInProduct {
	return StockInProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.Qty,
		Price:           0, // Price is not available in SalesDetail, so defaulting to 0
		MetaData: map[string]interface{}{
			"sales_detail_id": s.SalesDetailID,
		},
		CreatedAt: s.<PERSON>,
	}
}
