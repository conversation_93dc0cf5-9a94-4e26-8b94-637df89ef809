package domain

import (
	"gitlab.com/uniqdev/backend/api-report/models"
)

type Sales struct{}

type SalesReportRequest struct {
	StartDate     int64
	EndDate       int64
	TimeZone      int
	Category      []int
	SubCategory   []int
	Outlet        []int
	Limit         int
	Offset        int
	DataType      int //0: byShift, 1: byDate
	Shift         []int
	Page          int
	OffsetId      int64
	PromotionId   int
	PaymentMethod []string
	BankId        []int
	Customer      string
	Contact       string
	DataStatus    string

	//additional
	ProductDetailIds []int64
}

type SalesRankingRequest struct {
	StartDate   int64
	EndDate     int64
	imeZone     int
	Category    []int
	SubCategory []int
	Outlet      []int
	Limit       int
	Offset      int
	DataType    []int
	Shift       []int
	ProductId   []interface{}
	Page        int
}

type SalesReportMediaRequest struct {
	SalesReportRequest
	GroupBy   string
	DateGroup string
	GroupKey  string
}

type SalesReportOrderTypeRequst struct {
	SalesReportRequest
	GroupBy    string
	SalesTagId []int
}

type SalesGroupKey int

const (
	SalesId SalesGroupKey = iota
	SalesDetailId
	SalesDetailFkid
)

func (s SalesGroupKey) ToString() string {
	switch s {
	case SalesId:
		return "sales_fkid"
	case SalesDetailId:
		return "sales_detail_id"
	case SalesDetailFkid:
		return "sales_detail_fkid"
	default:
		return ""
	}
}

type SalesUseCase interface {
	FetchSalesRanking(request SalesRankingRequest, user User) (map[string]interface{}, error)
	FetchSalesRankingV2(request SalesRankingRequest, user User) (map[string]interface{}, error)

	FetchSalesAnalysisByOutlet(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)
	FetchSalesAnalysisByDay(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)
	FetchSalesAnalysisByDate(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)

	FetchTransferAnalysis(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)
	FetchSalesByMedia(user User, request SalesReportMediaRequest) (map[string]interface{}, error)
	FetchSalesSummary(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSalesPromotion(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSalesAnalysisByPromotion(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)
	FetchSalesAnalysisByHour(request DataTableRequest, dataType string, user User) (map[string]interface{}, error)
	FetchSalesHistoryTransaction(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSalesHistoryTransactionV2(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSalesHistoryTransactionDetail(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSalesByMediaGroupPayment(user User, request SalesReportMediaRequest) (map[string]interface{}, error)

	//sales-by
	FetchSalesByOrderType(user *User, request *SalesReportOrderTypeRequst) (*[]models.SalesByOrderTypeResponse, error)
	FetchSalesByOrderTypeGroup(user *User, request *SalesReportOrderTypeRequst) (*[]models.SalesByOrderTypeGroupResponse, error)

	RunWeeklyReportGenerator()
}

type SalesRepositoryMiddleware interface {
	FetchSalesAnalysisOutletByNominal(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisOutletByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisDayByNominal(request DataTableRequest, toInt int) ([]map[string]interface{}, error)
	FetchSalesAnalysisDayByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesByMedia(user User, request SalesReportMediaRequest) ([]map[string]interface{}, error)
	FetchSalesAnalysisByNominal(groupBy string, request DataTableRequest, toInt int) ([]map[string]interface{}, error)
	FetchSalesAnalysisByQty(groupBy string, request DataTableRequest, toInt int) ([]map[string]interface{}, error)
}

type SalesRepository interface {
	FetchSalesAnalysisOutletByNominal(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesByMedia(user User, request SalesReportMediaRequest) ([]map[string]interface{}, error)
	FetchSalesAnalysisDayByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisOutletByQty(request DataTableRequest, id int) ([]map[string]interface{}, error)
	FetchSalesAnalysisDayByNominal(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisByNominal(groupBy string, request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisByQty(groupBy string, request DataTableRequest, adminId interface{}) ([]map[string]interface{}, error)
	FetchSalesHistoryTransaction(user User, request SalesReportRequest) ([]map[string]interface{}, error)

	FetchSalesHistory(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchSumSalesDetailPromotion(groupBy SalesGroupKey, ids ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesDetailDiscount(groupBy SalesGroupKey, discType []string, salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesPromotion(salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesTaxPerCategory(salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesDetailTaxPerCategory(ids ...interface{}) ([]map[string]interface{}, error)
	FetchShiftByOpenShift(openShiftIds ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesVoid(groupBy SalesGroupKey, salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSumSalesDetail(salesIds ...interface{}) ([]map[string]interface{}, error)

	FetchSalesDetail(user User, request SalesReportRequest) ([]map[string]interface{}, error)

	FetchSalesPromotion(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchSalesAnalysisPromotionByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisPromotionByNominal(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesAnalysisPromotionDetailByNominal(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSummarySales(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchSummaryItem(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSummaryPromo(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSummaryPromoItem(user User, request SalesReportRequest) (map[string]interface{}, error)
	FetchSummaryGratuity(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchSummaryPayment(user User, request SalesReportRequest) ([]map[string]interface{}, error)

	//sales by
	FetchSalesTagByOutlet(user *User, request *SalesReportOrderTypeRequst) (*[]models.SalesTagByOutlet, error)
	FetchSalesByOrderTypeGroup(user *User, request *SalesReportOrderTypeRequst) (*[]models.SalesTagByDate, error)

	FetchSalesTotalByOutlet(startDate, endDate int64, outletIds ...interface{}) ([]map[string]interface{}, error)
}

type SalesRepositoryReplica interface {
	SalesRepository
	FetchSalesRankingPlain(request SalesRankingRequest, user User) ([]map[string]interface{}, error)
}

type SalesRepositoryPrimary interface {
	SalesRepository
	FetchOutletByIds(id ...interface{}) ([]map[string]interface{}, error)
	FetchShiftByIds(id ...interface{}) ([]map[string]interface{}, error)
	FetchProductByIds(id ...interface{}) ([]map[string]interface{}, error)
	FetchProductByDetailIds(ids ...interface{}) ([]map[string]interface{}, error)
	FetchSalesRanking(request SalesRankingRequest, user User) ([]map[string]interface{}, error)
	FetchTransferOutByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchTransferInByQty(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesRankingPlain(request SalesRankingRequest, user User) ([]map[string]interface{}, error)
	FetchBank(user User) ([]map[string]interface{}, error)
	FetchSalesDetailDiscount(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchDiscountSalesDetail(user User, request SalesReportRequest) ([]map[string]interface{}, error)
	FetchPromotion(promotionIds ...interface{}) ([]map[string]interface{}, error)
	FetchEmployee(employeeIds ...interface{}) ([]map[string]interface{}, error)
	FetchSalesPayment(salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSalesRefunds(salesIds ...interface{}) ([]map[string]interface{}, error)
	FetchSalesTagByIds(ids []int) (*[]models.SalesTagEntity, error)

	FetchSalesOfAdmin(timeStart int64) ([]int, error)
	CountOutletsWithTransactionsSince(adminID int, startDate int64) (int, error)
	FetchDailySalesSumByAdmin(adminID int, startDate, endDate int64) ([]map[string]interface{}, error)
	FetchTotalTransactionsByAdmin(adminID int, startTime, endTime int64) ([]map[string]interface{}, error)

	//order sales (app transaction)
	FetchOrderTypeOfOrderSales(salesIds ...interface{}) (*[]OrderTypeOfOrderSales, error)

	FetchAdmin(adminId int) (models.AdminEntity, error)
	AddScheduledMessage(data ...models.ScheduledMessageEntity) error
	FetchReportNotification(adminId int, reportType string) ([]models.SettingReportEntity, error)

	FetchSalesAnalysisOutletByNominalFast(request DataTableRequest, adminId int) ([]map[string]interface{}, error)
	FetchSalesDetailTax(adminId int, request SalesReportRequest) ([]map[string]interface{}, error)
}
