package usecase

import (
	"fmt"
	"sync"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type ProductReportData struct {
	ProductID    int     `json:"product_id"`
	VariantID    *int    `json:"variant_id"`
	ProductName  string  `json:"product_name"`
	SKU          string  `json:"sku"`
	CategoryID   int     `json:"category_id"`
	SubTotal     float64 `json:"sub_total"`
	Qty          int     `json:"qty"`
	Discount     float64 `json:"discount"`
	Voucher      float64 `json:"voucher"`
	Promotion    float64 `json:"promotion"`
	Tax          float64 `json:"tax"`
	Service      float64 `json:"service"`
	QtyVoid      int     `json:"qty_void"`
	SubVoid      float64 `json:"sub_void"`
	DiscountVoid float64 `json:"discount_void"`
}

type ProductReportResponse struct {
	SubTotal    float64 `json:"sub_total"`
	ProductName string  `json:"product_name"`
	SKU         string  `json:"sku"`
	Qty         int     `json:"qty"`
	Category    string  `json:"category"`
	Avg         float64 `json:"avg"`
	Discount    float64 `json:"discount"`
	Voucher     float64 `json:"voucher"`
	Promotion   float64 `json:"promotion"`
	GrandTotal  float64 `json:"grand_total"`
}

func (s *salesUseCase) FetchSalesReportByProduct(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	// Validate required parameters
	if request.StartDate == 0 {
		log.Error("StartDate is required and cannot be empty or zero")
		return nil, fmt.Errorf("startDate is required and cannot be empty or zero")
	}

	if request.EndDate == 0 {
		log.Error("EndDate is required and cannot be empty or zero")
		return nil, fmt.Errorf("endDate is required and cannot be empty or zero")
	}

	// Validate that start date is before end date
	if request.StartDate >= request.EndDate {
		log.Error("StartDate must be before EndDate. StartDate: %d, EndDate: %d", request.StartDate, request.EndDate)
		return nil, fmt.Errorf("startDate must be before endDate")
	}

	var repo domain.SalesRepository = s.repoPrimary
	log.Info("FetchSalesReportByProduct request: %+v", request)

	// Use goroutines to fetch data concurrently
	var wg sync.WaitGroup
	var subtotalData, voidData, discountData, promotionData, taxData []map[string]interface{}
	var subtotalErr, voidErr, discountErr, promotionErr, taxErr error

	// Fetch subtotal data
	wg.Add(1)
	go func() {
		defer wg.Done()
		subtotalData, subtotalErr = repo.FetchSalesReportByProductSubtotal(user, request)
	}()

	// Fetch void data
	wg.Add(1)
	go func() {
		defer wg.Done()
		voidData, voidErr = repo.FetchSalesReportByProductVoid(user, request)
	}()

	// Fetch discount data
	wg.Add(1)
	go func() {
		defer wg.Done()
		discountData, discountErr = repo.FetchSalesReportByProductDiscount(user, request)
	}()

	// Fetch promotion data
	wg.Add(1)
	go func() {
		defer wg.Done()
		promotionData, promotionErr = repo.FetchSalesReportByProductPromotion(user, request)
	}()

	// Fetch tax data
	wg.Add(1)
	go func() {
		defer wg.Done()
		taxData, taxErr = repo.FetchSalesReportByProductTax(user, request)
	}()

	wg.Wait()

	// Check for errors
	if subtotalErr != nil {
		log.Error("Error fetching subtotal data: %v", subtotalErr)
		return nil, subtotalErr
	}
	if voidErr != nil {
		log.Error("Error fetching void data: %v", voidErr)
		return nil, voidErr
	}
	if discountErr != nil {
		log.Error("Error fetching discount data: %v", discountErr)
		return nil, discountErr
	}
	if promotionErr != nil {
		log.Error("Error fetching promotion data: %v", promotionErr)
		return nil, promotionErr
	}
	if taxErr != nil {
		log.Error("Error fetching tax data: %v", taxErr)
		return nil, taxErr
	}

	// Merge data by product_id and variant_id
	productMap := make(map[string]*ProductReportData)

	// Process subtotal data (main data source)
	for _, row := range subtotalData {
		key := s.getProductKey(row)
		productMap[key] = &ProductReportData{
			ProductID:   cast.ToInt(row["product_id"]),
			VariantID:   s.getVariantID(row),
			ProductName: cast.ToString(row["product_name"]),
			SKU:         cast.ToString(row["sku"]),
			CategoryID:  cast.ToInt(row["product_category_fkid"]),
			SubTotal:    cast.ToFloat64(row["sub_total"]),
			Qty:         cast.ToInt(row["qty"]),
			Discount:    cast.ToFloat64(row["discount"]),
		}
	}

	// Process void data
	for _, row := range voidData {
		key := s.getProductKey(row)
		if product, exists := productMap[key]; exists {
			product.QtyVoid = cast.ToInt(row["qty_void"])
			product.SubVoid = cast.ToFloat64(row["sub_void"])
			product.DiscountVoid = cast.ToFloat64(row["discount"])
		}
	}

	// Process discount data
	for _, row := range discountData {
		key := s.getProductKey(row)
		if product, exists := productMap[key]; exists {
			product.Discount += cast.ToFloat64(row["discount"])
			product.Voucher = cast.ToFloat64(row["voucher"])
			product.Promotion += cast.ToFloat64(row["promotion"])
		}
	}

	// Process promotion data
	for _, row := range promotionData {
		key := s.getProductKey(row)
		if product, exists := productMap[key]; exists {
			product.Promotion += cast.ToFloat64(row["total"])
		}
	}

	// Process tax data
	for _, row := range taxData {
		key := s.getProductKey(row)
		if product, exists := productMap[key]; exists {
			product.Discount += cast.ToFloat64(row["discount"])
			product.Voucher += cast.ToFloat64(row["voucher"])
			product.Tax = cast.ToFloat64(row["tax"])
			product.Service = cast.ToFloat64(row["service"])
		}
	}

	// Fetch category names
	categoryMap, err := s.fetchCategoryNames(user)
	if err != nil {
		log.Error("Error fetching category names: %v", err)
		// Continue without category names
		categoryMap = make(map[int]string)
	}

	// Convert to response format
	var result []ProductReportResponse
	for _, product := range productMap {
		// Calculate final values (subtract void)
		finalSubTotal := product.SubTotal - product.SubVoid
		finalQty := product.Qty - product.QtyVoid
		finalDiscount := product.Discount - product.DiscountVoid

		// Calculate grand_total = subtotal - discount - voucher - promotion
		grandTotal := finalSubTotal - finalDiscount - product.Voucher - product.Promotion

		// Calculate average using grand_total/qty
		var avg float64
		if finalQty > 0 {
			avg = grandTotal / float64(finalQty)
		}

		categoryName := categoryMap[product.CategoryID]
		if categoryName == "" {
			categoryName = "Unknown"
		}

		result = append(result, ProductReportResponse{
			SubTotal:    finalSubTotal,
			ProductName: product.ProductName,
			SKU:         product.SKU,
			Qty:         finalQty,
			Category:    categoryName,
			Avg:         avg,
			Discount:    finalDiscount,
			Voucher:     product.Voucher,
			Promotion:   product.Promotion,
			GrandTotal:  grandTotal,
		})
	}

	return map[string]interface{}{
		"data": result,
	}, nil
}

func (s *salesUseCase) getProductKey(row map[string]interface{}) string {
	productID := cast.ToString(row["product_id"])
	if row["product_fkid"] != nil {
		productID = cast.ToString(row["product_fkid"])
	}

	variantID := "null"
	if row["variant_id"] != nil {
		variantID = cast.ToString(row["variant_id"])
	}

	return productID + "_" + variantID
}

func (s *salesUseCase) getVariantID(row map[string]interface{}) *int {
	if row["variant_id"] != nil {
		variantID := cast.ToInt(row["variant_id"])
		return &variantID
	}
	return nil
}

func (s *salesUseCase) fetchCategoryNames(user domain.User) (map[int]string, error) {
	// This would typically fetch from a product category repository
	// For now, return empty map as we don't have access to category repository
	return make(map[int]string), nil
}
