package db

import (
	"database/sql"
	"errors"
	"fmt"
	"runtime"
	"strings"
	"sync"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
)

func QueryArrayFun(sql string, params map[string]interface{}) ([]map[string]interface{}, error) {
	query, args := FormatArgs(sql, params)
	return QueryArray(query, args...)
}

func QueryFun(sql string, params map[string]interface{}) (map[string]interface{}, error) {
	query, args := FormatArgs(sql, params)
	return Query(query, args...)
}

func QueryArrayGo(wg *sync.WaitGroup, result chan []map[string]interface{}, sqlQuery string, args ...interface{}) {
	defer wg.Done()
	data, err := QueryArray(sqlQuery, args...)
	log.IfError(err)
	result <- data
}

func QueryArrayChannel(result chan []map[string]interface{}, sqlQuery string, args ...interface{}) {
	data, err := QueryArray(sqlQuery, args...)
	utils.CheckErr(err)
	result <- data
}

// func QueryArrayContext(ctx context.Context, sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
// 	// Create a channel to receive the result
// 	resultChan := make(chan []map[string]interface{})
// 	errChan := make(chan error)
// 	start := time.Now()

// 	// Run the query in a goroutine
// 	go func(query string, args ...interface{}) {
// 		result, err := QueryArray(query, args...)
// 		if err != nil {
// 			errChan <- err
// 		} else {
// 			resultChan <- result
// 		}
// 	}(sqlQuery, args...)

// 	// Wait for either the query to complete or the context to timeout
// 	select {
// 	case result := <-resultChan:
// 		return result, nil
// 	case err := <-errChan:
// 		return nil, err
// 	case <-ctx.Done():
// 		err := ctx.Err()
// 		if err == context.DeadlineExceeded {
// 			_, fn, line, _ := runtime.Caller(1)
// 			log.IfError(fmt.Errorf("%s:%v query DeadlineExceeded, took %v \n>> %v", fn, line, time.Since(start), getSQLRaw(sqlQuery, args...)))
// 		}
// 		return nil, err // This will be context.DeadlineExceeded if the timeout was reached
// 	}
// }

// func QueryArray(sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
// 	db := GetConn()
// 	tableData := make([]map[string]interface{}, 0)

// 	start := time.Now()
// 	stmt, err := db.Prepare(sqlQuery)
// 	if err != nil {
// 		fmt.Println("prepare statement err: ", err, ", sql: ", sqlQuery)
// 		return tableData, err
// 	}
// 	defer stmt.Close()

// 	rows, err := stmt.Query(args...)

// 	if os.Getenv("ENV") == "localhost" {
// 		fmt.Println("\n>> START QUERY : ", getSQLRaw(sqlQuery, args...))
// 	}

// 	if err == sql.ErrNoRows {
// 		return tableData, nil
// 	}

// 	if err != nil {
// 		_, fn, line, _ := runtime.Caller(1)
// 		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
// 		fmt.Println("query err: ", strings.ReplaceAll(getSQLRaw(sqlQuery, args...), "\n", " "))
// 		if driverErr, ok := err.(*mysql.MySQLError); ok {
// 			if driverErr.Number == 1065 {
// 				fmt.Println("error 1065")
// 			}
// 		}

// 		return tableData, err
// 	}

// 	defer rows.Close()

// 	columns, err := rows.Columns()
// 	if err != nil {
// 		return tableData, errors.New("sql column error: " + err.Error())
// 	}

// 	count := len(columns)
// 	values := make([]sql.RawBytes, count)
// 	scanArgs := make([]interface{}, count)

// 	for i := range values {
// 		scanArgs[i] = &values[i]
// 	}

// 	for rows.Next() {
// 		err := rows.Scan(scanArgs...)
// 		if err != nil {
// 			return tableData, errors.New("sql scan error: " + err.Error())
// 		}

// 		entry := make(map[string]interface{})
// 		for i, col := range values {
// 			entry[columns[i]] = string(col)
// 		}
// 		tableData = append(tableData, entry)
// 	}

// 	err = rows.Err()
// 	if err != nil {
// 		return tableData, errors.New("sql rows error: " + err.Error())
// 	}

// 	if os.Getenv("env") == "localhost" || time.Since(start) > 60*time.Second {
// 		fmt.Println("\n>> QUERY : ", getSQLRaw(sqlQuery, args...), "\n>> took: ", time.Since(start), " | total rows: ", len(tableData))
// 	}

// 	if time.Since(start) > 15*time.Second {
// 		_, fn, line, _ := runtime.Caller(1)
// 		log.IfError(fmt.Errorf("%s:%v query took %v \n>> %v", fn, line, time.Since(start), getSQLRaw(sqlQuery, args...)))
// 	}

// 	return tableData, nil
// }

// func Query(sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
// 	db := GetConn()
// 	entryData := make(map[string]interface{}, 0)

// 	rows, err := db.Query(strings.Replace(sqlQuery, "\n", " ", -1), args...)
// 	if err != nil {
// 		_, fn, line, _ := runtime.Caller(1)
// 		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
// 		fmt.Println("query err: ", getSQLRaw(sqlQuery, args...))
// 		return entryData, err
// 	}

// 	defer rows.Close()

// 	columns, err := rows.Columns()
// 	if err != nil {
// 		return entryData, errors.New("sql column error: " + err.Error())
// 	}

// 	count := len(columns)
// 	values := make([]interface{}, count)
// 	scanArgs := make([]interface{}, count)

// 	for i := range values {
// 		scanArgs[i] = &values[i]
// 	}

// 	rows.Next()
// 	err = rows.Scan(scanArgs...)
// 	if err != nil {
// 		//error might because data is not exist, so return nil
// 		return entryData, nil
// 	}

// 	for i, col := range columns {
// 		v := values[i]

// 		b, ok := v.([]byte)
// 		if ok {
// 			entryData[col] = string(b)
// 		} else {
// 			entryData[col] = v
// 		}
// 	}

// 	return entryData, nil
// }

func Insert(table string, data map[string]interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	res, err := GetConn().Exec(query, values...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Println("[insert] query err: ", getSQLRaw(query, values...))
	}

	if err != nil {
		for i := range query {
			if strings.Contains(query, "?") {
				val := fmt.Sprintf("%s", values[i])
				query = strings.Replace(query, "?", val, 1)
			} else {
				break
			}
		}
		fmt.Println("Executing Query Error : ", query)
	}

	return res, err
}

func Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond
	values = append(values, whereParams...)

	// fmt.Println(getSQLRaw(query, values...))
	res, err := GetConn().Exec(query, values...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Println("[update] query err: ", getSQLRaw(query, values...))
		stackSlice := make([]byte, 512)
		s := runtime.Stack(stackSlice, false)
		fmt.Println("stacktrace: ", string(stackSlice[0:s]))
	}

	return res, err
}

func Delete(table string, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	if whereCond == "" || len(whereParams) == 0 {
		return nil, errors.New("can not delete without where condition")
	}
	query := "DELETE FROM " + table + " WHERE " + whereCond

	res, err := GetConn().Exec(query, whereParams...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Println("Executing Query Error : ", getSQLRaw(query, whereParams...))
	}

	return res, err
}

func ResultArray(rows *sql.Rows) ([]map[string]interface{}, error) {
	tableData := make([]map[string]interface{}, 0)
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	return tableData, nil
}

func Result(rows *sql.Rows) (map[string]interface{}, error) {
	entryData := make(map[string]interface{}, 0)

	columns, err := rows.Columns()
	if err != nil {
		return entryData, nil
	}

	defer rows.Close()

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	if rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return entryData, nil
		}

		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entryData[col] = string(b)
			} else {
				entryData[col] = v
			}
		}
	}

	return entryData, nil
}

func getSQLRaw(sql string, params ...interface{}) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		sql = sql[:index] + fmt.Sprintf("%v", params[i]) + sql[index+1:]
	}
	return sql
}

func GetSQLRaw(sql string, params ...interface{}) string {
	return getSQLRaw(sql, params...)
}
