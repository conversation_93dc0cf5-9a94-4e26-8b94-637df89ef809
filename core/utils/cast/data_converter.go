package cast

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
)

func ToModel(data interface{}, model interface{}) error {
	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := data.(map[string]interface{}); ok {
		return MapToStruct(m, model)
	} else if arr, ok := data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				return MapArrayToStruct(arr, model)
			} else {
				return MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("model is not single map")
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	for k, v := range m {
		SetField(s, k, v)
	}
	return nil
}

func MapArrayToStruct(maps []map[string]interface{}, variable interface{}) error {
	structField := reflect.TypeOf(variable).Elem()
	structArray := reflect.ValueOf(variable).Elem()

	for _, m := range maps {
		newStruct := reflect.New(structField.Elem()).Elem()
		for k, v := range m {
			SetField(newStruct, k, v)
		}
		structArray.Set(reflect.Append(structArray, newStruct))
	}

	return nil
}

func SetField(m interface{}, key string, value interface{}) {
	defer func() {
		if r := recover(); r != nil {
			fmt.Printf("key %s, value %v, err : %v\n", key, value, r)
		}
	}()
	if value == nil {
		return
	}

	//map is not supported yet
	if reflect.ValueOf(value).Kind() == reflect.Map {
		return
	}

	var structValue reflect.Value
	switch res := m.(type) {
	case reflect.Value:
		structValue = res
	default:
		structValue = reflect.ValueOf(m).Elem()
	}

	structFieldValue := structValue.FieldByName(key)

	//if key not match, search for json tag
	if !structFieldValue.IsValid() {
		for i := 0; i < structValue.NumField(); i++ {
			field := structValue.Type().Field(i)
			if field.Anonymous {
				structAnonymous := structValue.Field(i)
				structFieldValue = structAnonymous.FieldByName(key)
				if !structFieldValue.IsValid() {
					isFound := false
					for j := 0; j < structAnonymous.NumField(); j++ {
						fieldAnonymous := structAnonymous.Type().Field(j)
						if v, ok := fieldAnonymous.Tag.Lookup("json"); ok {
							if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
								structFieldValue = structAnonymous.FieldByName(fieldAnonymous.Name)
								isFound = true
								break
							}
						}
					}
					if isFound {
						break
					}
				}
			} else if v, ok := field.Tag.Lookup("json"); ok {
				if v == key || strings.HasPrefix(v, fmt.Sprintf("%s,", key)) {
					structFieldValue = structValue.FieldByName(field.Name)
					break
				}
			}
		}
	}

	if !structFieldValue.IsValid() {
		//fmt.Printf("no such field: %s in obj\n", key)
		return
	}

	if !structFieldValue.CanSet() {
		fmt.Printf("can not set %s field value\n", key)
		return
	}

	structFieldType := structFieldValue.Type()
	val := reflect.ValueOf(value)

	//if data type from struct and map different, convert it
	if structFieldType != val.Type() {
		switch structFieldType.Kind() {
		case reflect.String:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int:
			val = reflect.ValueOf(ToInt(value))
		case reflect.Interface:
			val = reflect.ValueOf(ToString(value))
		case reflect.Int64:
			val = reflect.ValueOf(ToInt64(value))
		case reflect.Float32:
			val = reflect.ValueOf(ToFloat32(value))
		case reflect.Float64:
			val = reflect.ValueOf(ToFloat64(value))
		case reflect.Ptr:
			fmt.Println(reflect.ValueOf(&structFieldValue).Elem().Kind())
			fmt.Println(val.Type().Kind() == reflect.Map)
			reflect.ValueOf(&structFieldValue).Elem().Set(reflect.ValueOf(value))
			return
		default:
			fmt.Printf("[ERROR] field %s type didn't match obj field type, type is %v while value is %v\n", key, structFieldType, val.Type())
			return
		}
	}

	structFieldValue.Set(val)
}
