package mysql

import (
	"context"
	"database/sql"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type dashboardRepository struct {
	db    mysql.Repository
	cache domain.CacheInterface
}

func NewMysqlDashboardRepository(db *sql.DB, cache domain.CacheInterface) domain.DashboardRepository {
	return &dashboardRepository{mysql.Repository{Conn: db, CacheDb: cache}, cache}
}

func (d dashboardRepository) FetchNetSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	//get sales information
	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(CASE WHEN (s.status='Success' and s.data_status='on' and s.payment NOT IN ('COMPLIMENT','DUTY MEALS') ) THEN s.grand_total ELSE 0 END) AS sales
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid=? 
		AND s.time_created >=? 
		AND s.time_created <=?
		`+whereInOutlet+`
		GROUP BY outlet_id
	`, adminId, timeStart, timeEnd)

	log.IfError(err)
	return query, err
}

func (d dashboardRepository) FetchNetSalesByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	//get sales information
	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(CASE WHEN (s.status='Success' and s.data_status='on' and s.payment NOT IN ('COMPLIMENT','DUTY MEALS') ) THEN s.grand_total ELSE 0 END) AS sales
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY outlet_id
	`, adminId, timeStart, timeEnd)
	log.IfError(err)
	return query, nil
}

func (d dashboardRepository) FetchPaidPurchaseBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(p.grand_total) AS sum_grandtotal
		FROM purchase p
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.bayar >= p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid
	`, timeStart, timeEnd, adminId)
	return query, err
}

func (d dashboardRepository) FetchUnPaidPurchase(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(p.bayar) AS sum_uangmuka
		FROM purchase p
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.bayar < p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid
	`, timeStart, timeEnd, adminId)
	return query, err
}

func (d dashboardRepository) FetchDebtPayment(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(dp.nominal) AS sum_debtpayment,
			p.grand_total,
			p.bayar AS purchase_um,
			if(p.data_created<?, 0, p.bayar) AS purchase_um_range,
			dp.purchase_fkid AS purchase_id,
			IFNULL(
			sum(
				(SELECT sum(sdp.nominal) FROM debt_payment sdp WHERE sdp.time_created<? AND sdp.purchase_fkid=dp.purchase_fkid)
			), 0) AS debtpaid_under_range,
			sum(dp.nominal) AS debtpaid_on_range
		FROM debt_payment dp
		LEFT JOIN purchase p ON dp.purchase_fkid = p.purchase_id
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.admin_fkid = ?
		AND dp.time_created >= ?
		AND dp.time_created <= ?
		AND dp.data_status = 'on'
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid, p.bayar, p.grand_total, purchase_id
	`, timeStart, timeStart, adminId, timeStart, timeEnd)

	return query, err
}

func (d dashboardRepository) FetchDebt(adminId string, timeStart int, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			p.purchase_id,
			p.grand_total,
			p.bayar AS uang_muka,
			IF(p.bayar < p.grand_total, (p.grand_total-p.bayar), 0) AS hutang,
			
			(SELECT IFNULL(sum(sdp.nominal),0)
				FROM debt_payment sdp
				WHERE sdp.time_created<=?
				AND sdp.purchase_fkid=p.purchase_id
				AND sdp.data_status="on"
			) AS hutang_dibayar
		FROM purchase p
		LEFT JOIN outlets o ON o.outlet_id = p.outlet_fkid
		LEFT JOIN debt_payment dp ON dp.purchase_fkid = p.purchase_id
		WHERE p.bayar < p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?	
		`+whereInOutlet+`
		GROUP BY purchase_id
	`, timeEnd, timeStart, timeEnd, adminId)
	return query, err
}

func (d dashboardRepository) FetchComplimentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment ='COMPLIMENT', s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=?
		AND s.time_created >= ?
		AND s.time_created <= ?
		`+whereInOutlet+`
		GROUP BY o.outlet_id
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchDutyMealsBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment = 'DUTY MEALS', s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=?
		AND s.time_created >= ?
		AND s.time_created <= ?
		`+whereInOutlet+`
		GROUP BY o.outlet_id
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchTotalSalesPerPaymentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, paymentMethods ...string) ([]map[string]interface{}, error) {
	sql := `SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND trim(s.payment) in @paymentIds, s.grand_total,0)) AS sum_grandtotal,
			trim(s.payment) as payment
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid= @adminId
		AND s.time_created >= @startDate
		AND s.time_created <= @endDate		 
		$WHERE
		GROUP BY o.outlet_id, s.payment`

	var whereSql strings.Builder
	if len(outletIds) > 0 {
		whereSql.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    adminId,
		"startDate":  timeStart,
		"endDate":    timeEnd,
		"paymentIds": paymentMethods,
		"outletIds":  outletIds,
		"WHERE":      whereSql.String(),
	})

	result, err := d.db.QueryCache("FetchTotalSalesPerPaymentBySales", sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	log.Info("use old code, FetchTotalSalesPerPaymentBySales")
	//<----- Use Old Code (if cache failed) - just in case ------->

	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	paymentIn := strings.Repeat("?,", len(paymentMethods))
	paymentIn = strings.TrimRight(paymentIn, ",")

	params = []interface{}{adminId, timeStart, timeEnd}
	for _, method := range paymentMethods {
		params = append(params, method)
	}

	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on', s.grand_total,0)) AS sum_grandtotal,
			trim(s.payment) as payment
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=?
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND trim(s.payment) in (`+paymentIn+`)
		`+whereInOutlet+`
		GROUP BY o.outlet_id, s.payment
	`, params...)
	return query, err
}

func (d dashboardRepository) FetchFreeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(case when (s.status='Success' and s.data_status='on' and sd.discount = sd.sub_total) THEN sd.discount ELSE 0 END) AS free
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE sd.discount > 0
		AND o.admin_fkid = @adminId
		AND s.time_created >= @startDate 
		AND s.time_created <= @endDate 
		$WHERE
		GROUP BY s.outlet_fkid`

	var whereSql strings.Builder
	if len(outletIds) > 0 {
		whereSql.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}
	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   adminId,
		"startDate": timeStart,
		"endDate":   timeEnd,
		"outletIds": outletIds,
		"WHERE":     whereSql.String(),
	})

	result, err := d.db.QueryCache("FetchFreeBySales", sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	//<----- Use Old Code (if cache failed) - just in case ------->

	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}
	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(case when (s.status='Success' and s.data_status='on' and sd.discount = sd.sub_total) THEN sd.discount ELSE 0 END) AS free
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE sd.discount > 0
		AND o.admin_fkid = ?
		AND s.time_created >= ?
		AND s.time_created <= ?
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchDiscSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(s.discount) AS sales_discount
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid = @adminId 
		AND s.time_created >= @startDate
		AND s.time_created <= @endDate
		AND s.data_status = 'on'
		AND s.status = 'success'
		$WHERE
		GROUP BY s.outlet_fkid`

	var whereSql strings.Builder
	if len(outletIds) > 0 {
		whereSql.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   adminId,
		"startDate": timeStart,
		"endDate":   timeEnd,
		"outletIds": outletIds,
		"WHERE":     whereSql.String(),
	})

	result, err := d.db.QueryCache("FetchDiscSalesBySales", sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	query, err := db.QueryArrayContext(ctx, `
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			SUM(IF(s.data_status = 'on' AND s.status = 'Success',s.discount,0)) AS sales_discount
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid = ? 
		AND s.time_created >= ?
		AND s.time_created <= ?
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchDiscItemBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT s.outlet_fkid                             AS outlet_id,
       o.name                                    AS outlet_name,
       sum(CASE WHEN (s.status='Success' and s.data_status='on' and sd.discount < sd.sub_total) THEN sd.discount - coalesce(sv.v_disc, 0) ELSE 0 END) AS discount_item
FROM sales_detail sd
         LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
         left join (select sum(sales_void.discount) as v_disc, sales_detail_fkid
                    from sales_void
                    group by sales_detail_fkid) sv on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = @adminId
  AND s.time_created >= @startDate
  AND s.time_created <= @endDate
  $WHERE
GROUP BY s.outlet_fkid`

	var whereSql strings.Builder
	if len(outletIds) > 0 {
		whereSql.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   adminId,
		"startDate": timeStart,
		"endDate":   timeEnd,
		"outletIds": outletIds,
		"WHERE":     whereSql.String(),
	})

	result, err := d.db.QueryCache("FetchDiscItemBySales", sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	//where IN outlet_fkid
	whereInOutlet := ""
	if len(outletIds) > 0 {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	sql = `SELECT s.outlet_fkid                             AS outlet_id,
       o.name                                    AS outlet_name,
       sum(CASE WHEN (s.status='Success' and s.data_status='on' and sd.discount < sd.sub_total) THEN sd.discount - coalesce(sv.v_disc, 0) ELSE 0 END) AS discount_item
FROM sales_detail sd
         LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
         left join (select sum(sales_void.discount) as v_disc, sales_detail_fkid
                    from sales_void
                    group by sales_detail_fkid) sv on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = ?
  AND s.time_created >= ?
  AND s.time_created <= ?
  ` + whereInOutlet + `
GROUP BY s.outlet_fkid`

	query, err := db.QueryArray(sql, adminId, timeStart, timeEnd)
	return query, err
}
func (d dashboardRepository) FetchDiscGratuityBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(IF(s.data_status='on' AND s.status='Success' AND g.tax_category='discount', st.total, 0)) AS sales_tax_discount
		FROM sales_tax st
		LEFT JOIN sales s ON s.sales_id = st.sales_fkid
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN gratuity g ON g.gratuity_id = st.tax_fkid
		WHERE o.admin_fkid = @adminId
		AND s.time_created >= @startDate
		AND s.time_created <= @endDate
		$WHERE
		GROUP BY s.outlet_fkid`

	var whereSql strings.Builder
	if len(outletIds) > 0 {
		whereSql.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   adminId,
		"startDate": timeStart,
		"endDate":   timeEnd,
		"outletIds": outletIds,
		"WHERE":     whereSql.String(),
	})

	result, err := d.db.QueryCache("FetchDiscGratuityBySales", sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	return result, err
}

func (d dashboardRepository) FetchVoucherBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = " AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(CASE WHEN(s.data_status='on' and s.status='Success' and g.tax_category='voucher' `+whereInOutlet+`) THEN st.total ELSE 0 END) AS sales_tax_voucher
		FROM sales_tax st
		LEFT JOIN sales s ON s.sales_id = st.sales_fkid
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN gratuity g ON g.gratuity_id = st.tax_fkid
		WHERE o.admin_fkid = ? 
		AND s.time_created >= ?
		AND s.time_created <= ?		
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchVoucherSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}
	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(IF(s.data_status='on' AND s.status='Success', s.voucher, 0)) AS sales_voucher
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid = ? 
		AND s.time_created >= ?
		AND s.time_created <= ?
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	return query, err
}

func (d dashboardRepository) FetchPendingIncomeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//cache query
	query := `SELECT 
			o.outlet_id,
			o.name AS outlet_name,
			sum(ts.grand_total) AS pending_income
		FROM tmp_sales ts
		LEFT JOIN outlets o ON ts.outlet_fkid = o.outlet_id
		WHERE ts.time_created >= @timeStart
		AND ts.time_created <= @timeEnd
		AND o.admin_fkid = @adminId
		AND ts.status = 'pending'
		$WHERE
		GROUP BY ts.outlet_fkid
		ORDER BY pending_income DESC `

	var queryWhere strings.Builder
	if len(outletIds) > 0 {
		queryWhere.WriteString(" AND ts.outlet_fkid IN @outletIds ")
	}

	query, params := db.MapParam(query, map[string]interface{}{
		"timeStart": timeStart,
		"timeEnd":   timeEnd,
		"adminId":   adminId,
		"outletIds": outletIds,
		"WHERE":     queryWhere.String(),
	})

	result, err := d.db.QueryCache("FetchPendingIncomeBySales", query, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	//in case above code error, use the legacy code (stable version)
	//if not error happens for long time, consider removing below code
	//TODO: if error never occurs, remove the code

	//where IN outlet_fkid
	whereInOutlet := ""
	if len(outletIds) > 0 {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = " AND ts.outlet_fkid IN (" + outletId + ") "
	}

	result, err = db.QueryArray(`
		SELECT 
			o.outlet_id,
			o.name AS outlet_name,
			sum(ts.grand_total) AS pending_income
		FROM tmp_sales ts
		LEFT JOIN outlets o ON ts.outlet_fkid = o.outlet_id
		WHERE ts.time_created >= ?
		AND ts.time_created <= ?
		AND o.admin_fkid = ?
		AND ts.status = 'pending'
		`+whereInOutlet+`
		GROUP BY ts.outlet_fkid
		ORDER BY pending_income DESC
	`, timeStart, timeEnd, adminId)
	return result, err
}

func (d dashboardRepository) FetchTotalSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//query caching
	var queryWhere strings.Builder
	if outletIds != nil {
		queryWhere.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	query := `SELECT
	outlet_id,
	outlet_name,
	sum(qty_customers) AS total_customer,
	sum(grand_total) AS total_income,	
	count(sales_id) AS total_transaction
FROM (
	SELECT
		s.sales_id,
		s.outlet_fkid AS outlet_id,
		o.name AS outlet_name,
		s.qty_customers,
		if(s.status='Success' and s.data_status='on' and s.payment != 'COMPLIMENT' and s.payment != 'DUTY MEALS', s.grand_total, 0) as grand_total
	FROM
		sales s
		JOIN outlets o ON o.outlet_id = s.outlet_fkid
	WHERE
		o.admin_fkid = @adminId
		AND s.time_created BETWEEN @timeStart AND @timeEnd
		$WHERE
	GROUP BY
		sales_id) tb
GROUP BY
	outlet_id
ORDER BY
	total_income DESC `

	//to avoid caching returning old data, if time end is greater than now, set to now
	if timeEnd > time.Now().UnixMilli() {
		timeEnd = time.Now().UnixMilli()
	}

	query, params := db.MapParam(query, map[string]interface{}{
		"adminId":   adminId,
		"timeStart": timeStart,
		"timeEnd":   timeEnd,
		"outletIds": outletIds,
		"WHERE":     queryWhere.String(),
	})

	result, err := d.db.QueryCache("FetchTotalSalesBySales", query, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	//in case above code error, use the legacy code (stable version)
	//if not error happens for long time, consider removing below code
	//TODO: if error never occurs, remove the code

	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	querySalesInformation := db.GetSQLRaw(`
		SELECT
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			(sum(sd.qty) - IFNULL(sum(sv.qty),0)) AS qty_clean
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id 
		LEFT JOIN sales_detail sd ON sd.sales_fkid = s.sales_id 
		LEFT JOIN (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                            ON sd.sales_detail_id = sv.sales_detail_fkid
		WHERE o.admin_fkid= ?
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP BY sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	result, err = db.QueryArray(`
		SELECT
			outlet_id,
			outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (` + querySalesInformation + `) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)
	return result, err
}

func (d dashboardRepository) FetchItemSalesGroupSubCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereIn := strings.Repeat("?,", len(outletIds))
	whereIn = strings.TrimRight(whereIn, ",")

	//sum((sd.qty * sd.price) - sd.discount) + IFNULL(sum(sv.sub_total_void), 0) AS qty
	sql := `SELECT pcs.product_subcategory_id AS id,
       pcs.name                          AS name,
	   SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN('COMPLIMENT', 'DUTY MEALS'), ((sd.qty * sd.price) - sd.discount)+IFNULL((sv.sub_total_void),0), 0)) qty       
FROM sales_detail sd
         LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
         LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products p ON p.product_id = pd.product_fkid
         LEFT JOIN products_subcategory pcs ON pcs.product_subcategory_id = p.product_subcategory_fkid
         left join (select sales_detail_fkid,
                           sum(_sv.qty)                              as qty,
                           sum((_sv.qty * _sv.price) + _sv.discount) as sub_total_void
                    from sales_void _sv
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = ?
  AND s.time_created >= ?
  AND s.time_created <= ?
  AND pd.outlet_fkid IN (` + whereIn + `)
GROUP BY pcs.product_subcategory_id 
ORDER BY qty DESC `

	params := make([]interface{}, 0)
	params = append(params, adminId, timeStart, timeEnd)
	params = append(params, outletIds...)
	return db.QueryArray(sql, params...)
}

func (d dashboardRepository) FetchItemSalesGroupCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereIn := strings.Repeat("?,", len(outletIds))
	whereIn = strings.TrimRight(whereIn, ",")

	sql := `
SELECT pc.product_category_id        AS id,
       pc.name                          AS name,
       sum((sd.qty * sd.price) - sd.discount) +
       IFNULL(sum(sv.sub_total_void), 0) AS qty
FROM sales_detail sd
         LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
         LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products p ON p.product_id = pd.product_fkid
         LEFT JOIN products_category pc ON pc.product_category_id = p.product_category_fkid
         left join (select sales_detail_fkid,
                           sum(_sv.qty)                              as qty,
                           sum((_sv.qty * _sv.price) + _sv.discount) as sub_total_void
                    from sales_void _sv
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = ?
  AND s.time_created >= ?
  AND s.time_created <= ?
  AND s.status = 'success'
  AND s.data_status = 'on'
  AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS')
  AND pd.outlet_fkid IN (` + whereIn + `)
GROUP BY pc.product_category_id 
ORDER BY qty DESC `

	params := make([]interface{}, 0)
	params = append(params, adminId, timeStart, timeEnd)
	params = append(params, outletIds...)
	return db.QueryArray(sql, params...)
}

func (d dashboardRepository) FetchItemSalesGroupType(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereIn := strings.Repeat("?,", len(outletIds))
	whereIn = strings.TrimRight(whereIn, ",")

	sql := `
SELECT pt.products_type_id AS id,
       pt.name AS name,
       SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), ((sd.qty * sd.price) - sd.discount) + COALESCE((sv.sub_total_void), 0), 0)) AS qty
FROM sales_detail sd
         LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
         LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id
         LEFT JOIN products p ON p.product_id = pd.product_fkid
         LEFT JOIN products_type pt ON pt.products_type_id = p.product_type_fkid
         left join (select sales_detail_fkid,
                           sum(_sv.qty)                              as qty,
                           sum((_sv.qty * _sv.price) + _sv.discount) as sub_total_void
                    from sales_void _sv
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = ?
  AND s.time_created BETWEEN ? AND  ?
  AND pd.outlet_fkid IN (` + whereIn + `)
GROUP BY pt.products_type_id  
ORDER BY qty DESC `

	params := make([]interface{}, 0)
	params = append(params, adminId, timeStart, timeEnd)
	params = append(params, outletIds...)
	return db.QueryArray(sql, params...)
}

func (d dashboardRepository) FetchSalesByMedia(request domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	sql := `select sp.method, sum(sp.total) as total, 
	spb.bank_fkid,  FROM_UNIXTIME((ANY_VALUE(s.time_created)+@diff)/1000, '%Y-%m-%d') as created_at, 
	ANY_VALUE(o.name) as outlet_name, spb.bank_fkid
	from sales_payment sp 
	join sales s on s.sales_id=sp.sales_fkid
	join outlets o on s.outlet_fkid=o.outlet_id
	left join sales_payment_bank spb on spb.sales_payment_fkid=sp.payment_id
	where s.status = 'Success'
	and o.admin_fkid= @adminId
	and s.time_created between @startDate AND @endDate	
	[WHERE]
	group by sp.method, spb.bank_fkid, s.outlet_fkid, FROM_UNIXTIME((s.time_created+@diff)/1000, '%Y-%m-%d')
	order by created_at`

	whereQuery := ""
	if len(request.OutletIds) > 0 {
		whereQuery = " AND s.outlet_fkid in (@outletIds) "
	}

	sql = strings.Replace(sql, "[WHERE]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]interface{}{
		"startDate": request.TimeStart,
		"endDate":   request.TimeEnd,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.OutletIds,
		"diff":      request.TimeDiff,
		// "shiftIds":  request.Shift,
	})

	return d.db.QueryCache("dashboard-sales-media", sql, params...).MapArray()
	// return db.QueryArray(sql, params...)
}

func (d dashboardRepository) FetchBankById(ids ...interface{}) ([]map[string]interface{}, error) {
	sql := "select * from payment_media_bank where data_status ='on' "
	if len(ids) > 0 {
		sql += " and bank_id in @ids "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	return db.QueryArray(sql, params...)
}

// FethTopSales implements domain.DashboardRepository.
func (d *dashboardRepository) FetchTopSales(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	// sql := `
	// SELECT sd.product_fkid, sum((sd.qty-COALESCE(abs(sv.qty), 0))) total_qty,
	// sum(sd.sub_total-COALESCE(abs(sv.sub_total), 0)) total_subtotal
	// from sales_detail sd
	// join sales s on s.sales_id=sd.sales_fkid
	// join outlets o on o.outlet_id=s.outlet_fkid
	// left join sales_void sv on sv.sales_detail_fkid=sd.sales_detail_id
	// where  o.admin_fkid= @adminId and
	// s.time_created BETWEEN @timeStart and @timeEnd
	// and s.status='Success' and s.data_status='on'
	// and s.payment not in ('DUTY MEALS', 'COMPLIMENT')
	// $WHERE
	// group by sd.product_fkid `

	sql := `
	SELECT product_fkid, (qty-COALESCE(qty_void,0)) as total_qty, (subtotal-COALESCE(subtotal_void,0)) as total_subtotal FROM 
	(SELECT 
	sd.product_fkid,
  SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN('DUTY MEALS','COMPLIMENT'), sd.qty, 0))            AS qty,
  SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN('DUTY MEALS','COMPLIMENT'), ABS(sv.qty), 0))      AS qty_void,
  SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN('DUTY MEALS','COMPLIMENT'), sd.sub_total, 0))     AS subtotal,
  SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN('DUTY MEALS','COMPLIMENT'), ABS(sv.sub_total), 0)) AS subtotal_void	
	from sales_detail sd 
	join sales s on s.sales_id=sd.sales_fkid
	join outlets o on o.outlet_id=s.outlet_fkid
	left join sales_void sv on sv.sales_detail_fkid=sd.sales_detail_id
	where  o.admin_fkid= @adminId and 
	s.time_created BETWEEN @timeStart and @timeEnd	
	$WHERE
	group by sd.product_fkid) tmp_top_sales`

	var sqlWhere strings.Builder
	if len(param.OutletIds) > 0 {
		sqlWhere.WriteString(" and s.outlet_fkid in @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   user.BusinessId,
		"outletIds": param.OutletIds,
		"timeStart": param.TimeStart,
		"timeEnd":   param.TimeEnd,
		"WHERE":     sqlWhere.String(),
	})

	result, err := d.db.QueryCache("dashboard-top", sql, params...).MapArray()
	return result, err
}
