package middleware

import (
	"database/sql"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
	bqRepo "gitlab.com/uniqdev/backend/api-report/module/close_shift/repository/bigquery"
	"gitlab.com/uniqdev/backend/api-report/module/close_shift/repository/mysql"
	repoSale "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type closeShiftRepository struct {
	repoMain    domain.CloseShiftRepository
	repoReplica domain.CloseShiftRepository
}

func NewMiddlewareCloseShiftRepository(db *sql.DB, client *bigquery.Client, cache domain.CacheInterface) domain.CloseShiftRepository {
	return &closeShiftRepository{
		repoMain:    mysql.NewMysqlCloseShiftRepository(db, cache),
		repoReplica: bqRepo.NewBigQueryDashboardRepository(client),
	}
}

// FetchPayShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchPayShift(request domain.SalesReportRequest, user domain.User) (models.PayShift, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		payShift, err := c.repoReplica.FetchPayShift(request, user)
		if err == nil {
			return payShift, nil
		}
	}
	return c.repoMain.FetchPayShift(request, user)
}

// FetchPromoRecapProduk implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchPromoRecapProduk(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		promoRecap, err := c.repoReplica.FetchPromoRecapProduk(request, user)
		if err == nil {
			return promoRecap, nil
		}
	}
	return c.repoMain.FetchPromoRecapProduk(request, user)
}

// FetchPromoRecapShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchPromoRecapShift(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		promoRecap, err := c.repoReplica.FetchPromoRecapShift(request, user)
		if err == nil {
			return promoRecap, nil
		}
	}
	return c.repoMain.FetchPromoRecapShift(request, user)
}

// FetchRefundShiftRecap implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchRefundShiftRecap(request domain.SalesReportRequest, user domain.User) (models.RefundShiftRecap, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		refundShiftRecap, err := c.repoReplica.FetchRefundShiftRecap(request, user)
		if err == nil {
			return refundShiftRecap, nil
		}
	}
	return c.repoMain.FetchRefundShiftRecap(request, user)
}

// FetchSalesDetailShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchSalesDetailShift(request domain.SalesReportRequest, user domain.User) (models.SalesDetailShift, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		salesDetailShift, err := c.repoReplica.FetchSalesDetailShift(request, user)
		if err == nil {
			return salesDetailShift, nil
		}
	}
	return c.repoMain.FetchSalesDetailShift(request, user)
}

// FetchSalesRecapShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchSalesRecapShift(request domain.SalesReportRequest, user domain.User) (models.SalesRecapShift, error) {
	// if request.EndDate < repoSale.GetMaxTimeSync() {
	// 	salesRecapShift, err := c.repoReplica.FetchSalesRecapShift(request, user)
	// 	if err == nil {
	// 		return salesRecapShift, nil
	// 	}
	// }
	return c.repoMain.FetchSalesRecapShift(request, user)
}

// FetchTaxShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchTaxShift(request domain.SalesReportRequest, user domain.User) (models.TaxShift, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		taxShift, err := c.repoReplica.FetchTaxShift(request, user)
		if err == nil {
			return taxShift, nil
		}
	}
	return c.repoMain.FetchTaxShift(request, user)
}

// FetchVoidShiftRecap implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchVoidShiftRecap(request domain.SalesReportRequest, user domain.User) (models.VoidShiftRecap, error) {
	if request.EndDate < repoSale.GetMaxTimeSync() {
		voidShiftRecap, err := c.repoReplica.FetchVoidShiftRecap(request, user)
		if err == nil {
			return voidShiftRecap, nil
		}
	}
	return c.repoMain.FetchVoidShiftRecap(request, user)
}
