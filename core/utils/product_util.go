package utils

import (
	"fmt"
	"strings"
)

func FormatVariantName(product string, variantName interface{}) string {
	var formattedName string

	switch v := variantName.(type) {
	case string:
		if strings.TrimSpace(v) != "" {
			formattedName = fmt.Sprintf("%s (%s)", product, v)
		} else {
			formattedName = product
		}
	case *string:
		if v != nil && strings.TrimSpace(*v) != "" {
			formattedName = fmt.Sprintf("%s (%s)", product, *v)
		} else {
			formattedName = product
		}
	default:
		formattedName = product
	}

	return formattedName
}
