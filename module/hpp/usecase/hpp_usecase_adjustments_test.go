package usecase

import (
	"fmt"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
	"gitlab.com/uniqdev/backend/api-report/module/hpp/mocks"
)

func Test_hppUseCase_AdjustStockOpname(t *testing.T) {
	//test cases:
	//- when no stock_opname
	//- adjustment greater than before
	//- adjustment smaller than before

	jsonStockPrice := `[{"stock_price_id":1,"product_detail_fkid":1,"stock_in":10,"stock_out":5,"price":1000,"stock_in_id":"1","stock_in_source":"purchase_confirm","updated_at":1703218739529,"created_at":1703218739529}]`
	jsonStockOpname20 := `[{"stock_price_id":2,"product_detail_fkid":1,"stock_in":20,"stock_out":0,"price":2000,"stock_in_id":"2","stock_in_source":"stock_opname","updated_at":1702960317000,"created_at":1702960317000}]`
	jsonStockOpname2 := `[{"stock_price_id":2,"product_detail_fkid":1,"stock_in":2,"stock_out":0,"price":2000,"stock_in_id":"2","stock_in_source":"stock_opname","updated_at":1702960317000,"created_at":1702960317000}]`
	jsonStockPriceDetail := `[{"stock_price_detail_id":300,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"170KL7LM678N1","stock_out_source":"sales","created_at":1702979085991},{"stock_price_detail_id":301,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"170KL7LM678N1","stock_out_source":"sales","created_at":1702979086140}]`

	type args struct {
		stockInId     string
		stockInSource string
		stockInNew    float32
	}

	stockPrice := jsonToHpp(jsonStockPrice)
	emptyStockPrice := []domain.Hpp{}

	t.Run("no-stock-opname", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)

		arg := args{stockInId: "1", stockInNew: 10, stockInSource: domain.PurchaseConfirm.String()}

		repoMock.On("FetchStockPriceByIdAndSource", arg.stockInId, arg.stockInSource).Return(&stockPrice, nil)
		repoMock.EXPECT().FetchNearestStockPrice(stockPrice[0].ProductDetailFkid,
			stockPrice[0].CreatedAt,
			domain.StockOpname.String()).Return(emptyStockPrice, nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.AdjustStockOpname(arg.stockInId, arg.stockInSource, arg.stockInNew)
	})

	t.Run("new-greater-enough-opname", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)

		surplus := float32(10)
		arg := args{stockInId: "1", stockInNew: stockPrice[0].StockIn + (surplus), stockInSource: domain.PurchaseConfirm.String()}
		stockOpnames := jsonToHpp(jsonStockOpname20)

		repoMock.On("FetchStockPriceByIdAndSource", arg.stockInId, arg.stockInSource).Return(&stockPrice, nil)
		repoMock.EXPECT().FetchNearestStockPrice(stockPrice[0].ProductDetailFkid,
			stockPrice[0].CreatedAt,
			domain.StockOpname.String()).Return(stockOpnames, nil).Once()
		repoMock.EXPECT().UpdateStockPriceQtyIn(stockOpnames[0].StockPriceId, surplus).Return(nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.AdjustStockOpname(arg.stockInId, arg.stockInSource, arg.stockInNew)
	})

	t.Run("new-greater-not-enough-opname", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)

		surplus := float32(10)
		arg := args{stockInId: "1", stockInNew: stockPrice[0].StockIn + (surplus), stockInSource: domain.PurchaseConfirm.String()}
		stockOpnames := jsonToHpp(jsonStockOpname2)

		repoMock.On("FetchStockPriceByIdAndSource", arg.stockInId, arg.stockInSource).Return(&stockPrice, nil)
		repoMock.EXPECT().FetchNearestStockPrice(stockPrice[0].ProductDetailFkid,
			stockPrice[0].CreatedAt,
			domain.StockOpname.String()).Return(stockOpnames, nil).Once()
		repoMock.EXPECT().UpdateStockPriceQtyIn(stockOpnames[0].StockPriceId, stockOpnames[0].StockIn).Return(nil).Once()
		repoMock.EXPECT().FetchNearestStockPrice(stockOpnames[0].ProductDetailFkid, stockOpnames[0].CreatedAt, "").Return(emptyStockPrice, nil).Once()
		repoMock.EXPECT().InsertStockPrice(domain.Hpp{
			StockOut:       surplus - stockOpnames[0].StockIn,
			StockOutId:     stockOpnames[0].StockInId,
			StockOutSource: domain.StockOpname.String(),
		}).Return(1, nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.AdjustStockOpname(arg.stockInId, arg.stockInSource, arg.stockInNew)
	})

	t.Run("new-smaller", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)

		surplus := -float32(5)
		arg := args{stockInId: "1", stockInNew: stockPrice[0].StockIn + (surplus), stockInSource: domain.PurchaseConfirm.String()}
		stockOpnames := jsonToHpp(jsonStockOpname2)
		stockPriceDetail := jsonToStockPriceDetail(jsonStockPriceDetail, t)

		repoMock.On("FetchStockPriceByIdAndSource", arg.stockInId, arg.stockInSource).Return(&stockPrice, nil)
		repoMock.EXPECT().FetchNearestStockPrice(stockPrice[0].ProductDetailFkid,
			stockPrice[0].CreatedAt,
			domain.StockOpname.String()).Return(stockOpnames, nil).Once()
		repoMock.EXPECT().FetchStockPriceDetailByOutSourceId(cast.ToString(stockOpnames[0].StockPriceId),
			stockOpnames[0].StockInSource).Return(stockPriceDetail, nil).Once()

		//update the detail to be 0, or should be removed from db
		repoMock.EXPECT().UpdateStockPriceDetailQty(stockPriceDetail[0].StockPriceDetailID, float32(0)).Return(nil).Once()
		repoMock.EXPECT().UpdateStockPriceDetailQty(stockPriceDetail[1].StockPriceDetailID, float32(0)).Return(nil).Once()

		//add to stockPrice, remains 1 qty
		repoMock.EXPECT().UpdateStockPriceQtyIn(stockOpnames[0].StockPriceId, stockOpnames[0].StockIn+1).Return(nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.AdjustStockOpname(arg.stockInId, arg.stockInSource, arg.stockInNew)
	})

	type fields struct {
		repo hpp.Repository
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo: tt.fields.repo,
			}
			if err := h.AdjustStockOpname(tt.args.stockInId, tt.args.stockInSource, tt.args.stockInNew); (err != nil) != tt.wantErr {
				t.Errorf("hppUseCase.AdjustStockOpname() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_hppUseCase_FifoAdjusmentIntegrated(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	// Prepare the mock database query and result for data insertion
	mock.ExpectExec("INSERT INTO users").WithArgs(1, "John Doe").WillReturnResult(sqlmock.NewResult(1, 1))

	// Insert test data into the database
	_, err = db.Exec("INSERT INTO users (id, name) VALUES (?, ?)", 1, "John Doe")
	assert.NoError(t, err)

	mock.ExpectQuery("SELECT name FROM users WHERE id = ?").WithArgs(1)

	var name string
	err = db.QueryRow("SELECT name FROM users WHERE id = ?", 1).Scan(&name)
	fmt.Println(">> name: ", name, "err: ", err)
	assert.NoError(t, err)
	assert.Equal(t, "John Doe", name)

	// Close the database connection
	assert.NoError(t, db.Close())
}
