package jobs

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/google"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/domain"
)

func (s *scheduler) RunSubscription() {
	env := os.Getenv("ENV")
	subsId := fmt.Sprintf("pos-sales-%s-report", env)
	go google.Subscribe(subsId, s.handleNewSales)

	subsIdPurchase := fmt.Sprintf("purchase_%s-report", env)
	go google.Subscribe(subsIdPurchase, func(data []byte) bool {
		return s.handleNewPurchase(data, "success")
	})

	subsIdPurchaseRetur := fmt.Sprintf("purchase_retur_%s-report", env)
	go google.Subscribe(subsIdPurchaseRetur, func(data []byte) bool {
		return s.handleNewPurchase(data, "retur")
	})

	// stock-opname-development-finance
	subsIdStockOpname := fmt.Sprintf("stock-opname-%s-finance", env)
	go google.Subscribe(subsIdStockOpname, s.handleStockOpname)

	subsIdProduction := fmt.Sprintf("production-%s-report", env)
	go google.Subscribe(subsIdProduction, s.handleNewProduction)

	subsIdTransferConfirm := fmt.Sprintf("transfer_confirm_%s_api_report", env)
	go google.Subscribe(subsIdTransferConfirm, s.handleNewTransferConfirm)

	subsIdPurchaseUpdate := fmt.Sprintf("purchase-confirm-update-%v-report", env)
	go google.Subscribe(subsIdPurchaseUpdate, s.handlePurchaseUpdate)

	subsIdSpoil := fmt.Sprintf("spoil-%v-report", env)
	go google.Subscribe(subsIdSpoil, s.handleSpoil)
}

func (s *scheduler) handleNewSales(data []byte) bool {
	//data := map[string]interface{}{
	//	"sales_id":  sales.NoNota,
	//	"status":    sales.Status,
	//	"outlet_id": sales.OutletID,
	//	"admin_id":  utils.ToInt(adminId),
	//}

	var sales struct {
		SalesId string `json:"sales_id"`
		Status  string `json:"status"`
	}

	log.IfError(json.Unmarshal(data, &sales))

	var source domain.HppSource = domain.Sale
	if strings.ToLower(sales.Status) == "refund" {
		source = domain.SaleRefund
	}

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: source,
		Id:     utils.ToString(sales.SalesId),
	})

	return true
}

func (s *scheduler) handleNewPurchase(data []byte, status string) bool {
	var purchase struct {
		PurchaseProductId interface{} `json:"purchase_product_fkid"`
		PurchaseId        interface{} `json:"purchase_id"`
		Qty               interface{} `json:"qty"`
		ReturProductId    int         `json:"retur_product_id"`
		PurchaseConfirmId interface{} `json:"purchase_confirm_id"`
	}

	if log.IfError(json.Unmarshal(data, &purchase)) {
		return false
	}

	log.Info("purchase data: %v | status: %v", string(data), status)

	var source domain.HppSource = domain.PurchaseConfirm
	if status == "retur" {
		source = domain.PurchaseRetur
	}

	var purchaseMap map[string]interface{}
	log.IfError(json.Unmarshal(data, &purchaseMap))
	log.Info("[by struct] id str: %v", cast.ToString(purchase.PurchaseConfirmId))
	log.Info("[by map]    id str: %v", cast.ToString(purchaseMap["purchase_confirm_id"]))

	// if purchase.PurchaseConfirmId == nil {
	// 	log.IfError(fmt.Errorf("dont know how to handle this type of data [purchase]"))
	// 	return false
	// }

	if cast.ToInt(purchase.PurchaseConfirmId) == 0 && cast.ToInt(purchase.PurchaseProductId) == 0 {
		log.IfError(fmt.Errorf("dont know how to handle this type of data [purchase] (both are 0)"))
		return false
	}

	if cast.ToInt(purchase.PurchaseConfirmId) <= 0 && source == domain.PurchaseConfirm {
		newId, err := s.hpp.FetchPurchaseConfirmId(cast.ToInt(purchase.PurchaseProductId))
		if err != nil || newId == 0 {
			log.IfError(fmt.Errorf("purchase confirm id is %v [purchase] | newId: %v", purchase.PurchaseConfirmId, newId))
			return false
		}

		purchase.PurchaseConfirmId = newId
		log.Info("new purchase confirm id: %v", newId)
	}

	//handle for retur
	var id string
	if source == domain.PurchaseRetur && purchase.ReturProductId > 0 {
		id = cast.ToString(purchase.ReturProductId)
	} else if source == domain.PurchaseConfirm {
		id = cast.ToString(purchase.PurchaseConfirmId)
	} else {
		log.IfError(fmt.Errorf("can not handle purchase/retur, %s", status))
		return false
	}

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: source,
		Id:     id,
		Qty:    cast.ToInt(purchase.Qty),
	})

	return true
}

func (s *scheduler) handlePurchaseUpdate(data []byte) bool {
	var purchaseUpdate struct {
		PurchaseID string `json:"purchase_id,omitempty"`
		Update     struct {
			PurchaseProducts []string `json:"purchase_products,omitempty"`
			PurchaseConfrim  []string `json:"purchase_confrim,omitempty"`
		} `json:"update,omitempty"`
		Add struct {
			PurchaseConfrim []int `json:"purchase_confrim,omitempty"`
		} `json:"add,omitempty"`
	}

	log.Info("purchaseUpdate data: %v", string(data))

	if log.IfError(json.Unmarshal(data, &purchaseUpdate)) {
		return os.Getenv("ENV") == "development"
	}

	purchaseConfirmIds := make(map[int]bool)
	for _, id := range purchaseUpdate.Update.PurchaseConfrim {
		purchaseConfirmIds[cast.ToInt(id)] = true
	}
	for _, id := range purchaseUpdate.Add.PurchaseConfrim {
		purchaseConfirmIds[cast.ToInt(id)] = true
	}

	if len(purchaseConfirmIds) == 0 {
		log.Info(">> skip... no purchaseConfirmIds...")
		return true
	}

	// log.Info("ids: %v", purchaseUpdate.PurchaseConfirmId)

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: domain.PurchaseConfirmUpdate,
		Id:     utils.SimplyToJson(array.MapKey(purchaseConfirmIds)),
	})
	return true
}

func (s *scheduler) handleStockOpname(data []byte) bool {
	var stockOpname struct {
		OpnameIds []int `json:"opname_ids"`
	}

	log.Info("stockOpname data: %v", string(data))

	if log.IfError(json.Unmarshal(data, &stockOpname)) {
		return os.Getenv("ENV") == "development"
	}
	log.Info("ids: %v", stockOpname.OpnameIds)

	// s.hpp.HandleStockOpname(stockOpname.OpnameIds)
	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: domain.StockOpname,
		Id:     utils.SimplyToJson(stockOpname.OpnameIds),
	})
	return true
}

func (s *scheduler) handleNewProduction(data []byte) bool {
	fmt.Println("receive new production: ", string(data))
	var production struct {
		AdminID      string `json:"admin_id"`
		OutletID     string `json:"outlet_id"`
		ProductionID string `json:"production_id"`
	}
	log.IfError(json.Unmarshal(data, &production))

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: domain.Production,
		Id:     production.ProductionID,
	})

	return true
}

func (s *scheduler) handleNewTransferConfirm(data []byte) bool {
	var transferConfirm struct {
		Id string `json:"id,omitempty"`
	}

	if log.IfError(json.Unmarshal(data, &transferConfirm)) {
		return false
	}

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: domain.TransferConfirm,
		Id:     transferConfirm.Id,
	})

	return true
}

func (s *scheduler) handleSpoil(data []byte) bool {
	fmt.Println("receive new spoil: ", string(data))
	var spoil struct {
		SpoilIds []int `json:"spoil_ids,omitempty"`
	}

	if log.IfError(json.Unmarshal(data, &spoil)) {
		return false
	}

	s.hpp.HandleNewStockInOut(domain.HppSubscription{
		Source: domain.Spoil,
		Id:     utils.SimplyToJson(spoil.SpoilIds),
	})
	return true
}
