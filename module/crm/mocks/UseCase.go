// Code generated by mockery v2.15.0. DO NOT EDIT.

package mocks

import (
	mock "github.com/stretchr/testify/mock"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

// UseCase is an autogenerated mock type for the UseCase type
type UseCase struct {
	mock.Mock
}

// FetchCohortAnalysis provides a mock function with given fields: user, request
func (_m *UseCase) FetchCohortAnalysis(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	ret := _m.Called(user, request)

	var r0 []map[string]interface{}
	if rf, ok := ret.Get(0).(func(domain.User, domain.RequestParam) []map[string]interface{}); ok {
		r0 = rf(user, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User, domain.RequestParam) error); ok {
		r1 = rf(user, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchCustomerList provides a mock function with given fields: _a0, _a1
func (_m *UseCase) FetchCustomerList(_a0 domain.User, _a1 domain.RequestParam) (interface{}, error) {
	ret := _m.Called(_a0, _a1)

	var r0 interface{}
	if rf, ok := ret.Get(0).(func(domain.User, domain.RequestParam) interface{}); ok {
		r0 = rf(_a0, _a1)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User, domain.RequestParam) error); ok {
		r1 = rf(_a0, _a1)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchCustomerTransactionList provides a mock function with given fields: user, request
func (_m *UseCase) FetchCustomerTransactionList(user domain.User, request domain.RequestParam) (interface{}, error) {
	ret := _m.Called(user, request)

	var r0 interface{}
	if rf, ok := ret.Get(0).(func(domain.User, domain.RequestParam) interface{}); ok {
		r0 = rf(user, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User, domain.RequestParam) error); ok {
		r1 = rf(user, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FetchMemberAcquisition provides a mock function with given fields: user, request
func (_m *UseCase) FetchMemberAcquisition(user domain.User, request domain.RequestParam) (interface{}, error) {
	ret := _m.Called(user, request)

	var r0 interface{}
	if rf, ok := ret.Get(0).(func(domain.User, domain.RequestParam) interface{}); ok {
		r0 = rf(user, request)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(interface{})
		}
	}

	var r1 error
	if rf, ok := ret.Get(1).(func(domain.User, domain.RequestParam) error); ok {
		r1 = rf(user, request)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

type mockConstructorTestingTNewUseCase interface {
	mock.TestingT
	Cleanup(func())
}

// NewUseCase creates a new instance of UseCase. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewUseCase(t mockConstructorTestingTNewUseCase) *UseCase {
	mock := &UseCase{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
