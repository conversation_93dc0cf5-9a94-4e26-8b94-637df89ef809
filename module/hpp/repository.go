package hpp

import (
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

//go:generate mockery --name Repository --outpkg=mocks
type Repository interface {
	FetchLastHppByProductId(productDetailId int) (domain.Hpp, error)
	// FetchLastHppByProducts(productDetailId ...int) (*[]domain.Hpp, error)
	FetchPurchaseProductById(purchaseProductId int) (map[string]interface{}, error)
	FetchPurchaseProductByConfirmId(purchaseConfirmId ...int) (*[]models.PurchaseProductStockIn, error)

	InsertStockPrice(hpp domain.Hpp) (int64, error)
	FetchProductByProductDetailId(productDetailId int) (map[string]interface{}, error)
	UpdatePriceBuyProduct(productDetailId int, priceBuy int) error
	UpdatePriceBuyProducts(productPrice ...models.CurrentProductPrice) error
	UpdateStockPriceQtyIn(stockPriceId int, qtyIn float32) error
	UpdateStockPriceDetailQty(stockPriceDetailId int64, qtyOut float32) error

	FetchSalesDetailBySalesId(salesId string) (*[]models.SalesDetail, error)
	FetchHppByProductId(productDetailId int) ([]domain.Hpp, error)

	//fetch available hpp
	FetchStockOutHpp(productDetailId ...int) ([]domain.Hpp, error)
	UpdateStockPrice(stockPriceId int, data map[string]interface{}) error

	//add to stock_price_detail, and automatically update stock_out in stock_price
	InsertStockOutDetail(stockDetail domain.StockPriceDetail) error
	FetchHppByStockInId(stockInId string, source string) ([]domain.Hpp, error)
	DeleteHppById(stockPriceId int) error
	DeleteHppByIdSoftly(stockPriceId int) error

	FetchCurrentPriceOfProduct(productDetailId int) (float32, error)
	//get current/latest hpp of the product, based on stock_price
	FetchCurrentPrice(productDetailIds ...int) ([]models.CurrentProductPrice, error)

	FetchStockPriceDetail(stockPriceId ...interface{}) (*[]models.StockPriceDetailEntity, error)

	AdjustStockPriceDetail(adjust []models.StockPriceDetailEntity, remove []int64, stockNew []map[string]interface{}) (int64, error)

	//adjusting stock_out in stock_price,
	// map key is stock_price_id, value is adjustedQty (not new/updated qty).
	// this will update: stock_out = stock_out + adjustedQty.
	AdjustStockPriceQtyOut(stockPrice map[int64]float32) error

	//update stock_out in stock_price table, based on sum from stock_price_detail
	//return total rows effected
	UpdateStockPriceOutBasedDetail(stockPriceIds ...interface{}) (int64, error)

	FetchPurchaseConfirmByPurchaseProduct(purchaseProductId int) ([]map[string]interface{}, error)

	FetchStockPriceDetailByOutSourceId(id string, idSource string) ([]models.StockPriceDetailEntity, error)
	FetchStockPriceDetailByStockIn(inId, inSource string, stockOutSources ...string) ([]models.StockPriceDetailEntity, error)

	FetchStockPriceByOutSourceId(id string, idSource string) ([]models.StockPriceWithDetailQty, error)
	UpdatePriceBuySalesDetail(updates []models.PriceBuyUpdate) error
	DeleteStockPriceDetailByOutsourceId(id string, source string) error
	//delete stock_price_detail while also update stock_out in stock_price with transaction
	DeleteStockPriceDetail(id int64) error

	FetchStockPriceByIdAndSource(id string, source string) (*[]domain.Hpp, error)

	//Fetch stock_price after createdAt based on stockInSource.
	//Returns multiple stock_price if the same stock_in_id recorded twice or more
	FetchNearestStockPrice(productDetailId int, createdAt int64, stockInSource string) ([]domain.Hpp, error)
	//AddStockOut(stockPriceId int, qty float32, sourceId string, sourceName string) error

	//transfer
	FetchTransferConfirm(transferId int) (*[]models.TransferConfirm, error)

	FetchSalesBreakdown(salesId string) (*[]models.SalesBreakdown, error)
	UpdatePriceBuySalesBreakdown(updates []models.PriceBuyUpdate) error
	FetchPurchaseRetur(returId int) (map[string]interface{}, error)
	Filter(filter domain.StockPriceFilter) Repository
	FetchStockOpname(stockOpnameIds ...interface{}) ([]models.StockOpnameEntity, error)
	FetchStockPrice() (*[]domain.Hpp, error)

	//spoil
	FetchSpoil(spoilId ...int) ([]models.SpoilEntity, error)

	//anomaly
	FetchAnomalyOverStockOut() (*models.AnomalyOverStockOut, error)
	FetchAnomalyOverStockOutDetail(productIds ...int) (*domain.Hpp, error)
	FetchAnomalyMismatchStockOut() (*[]models.AnomalyMismatchStockOut, error)

	//for simulation or testing
	FetchStockPriceWithDetail(param *domain.StockPriceRequest) ([]domain.StockPriceResponse, error)

	//removing stock_price, which stock_in and stock_out are zeros, and not stock_opname
	DeleteUnUsedStockPrice() error
}
