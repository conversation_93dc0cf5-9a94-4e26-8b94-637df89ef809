package models

type AdminEntity struct {
	AdminID      int    `json:"admin_id,omitempty"`
	BusinessName string `json:"business_name,omitempty"`
	Name         string `json:"name,omitempty"`
	Email        string `json:"email,omitempty"`
	DateJoin     int64  `json:"date_join,omitempty"`
	DataCreated  int64  `json:"data_created,omitempty"`
	DataModified int64  `json:"data_modified,omitempty"`
	Phone        string `json:"phone,omitempty"`
	BusinessType any    `json:"business_type,omitempty"`
	AccountID    int    `json:"account_id,omitempty"`
}
