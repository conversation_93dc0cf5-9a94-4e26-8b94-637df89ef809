package controller

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func WelcomePage(ctx *fasthttp.RequestCtx) {
	//data, err := db.QueryArray("SELECT * FROM admin WHERE admin_id = ?", 1)
	//if utils.CheckError(ctx, err) {
	//	return
	//}

	//	data, err := db.Table("admin").Select("name","email","admin_id").Where("email = ?", "<EMAIL>").And("last_login >= ?", 0).DataTables(ctx)

	//data, err := db.Table("admin").InnerJoin("outlets").On("admin.admin_id=outlets.admin_fkid").DataTables(ctx)
	data, err := db.QueryArray("SELECT * FROM admin")

	if utils.CheckError(ctx, err) {
		return
	}

	//json.NewEncoder(ctx).Encode(models.ApiResponse{Status:true, Data:data, Code:0, Message:"Success"})
	json.NewEncoder(ctx).Encode(data)
}

func AddProduct(ctx *fasthttp.RequestCtx) {
	//get from Post form
	name := ctx.FormValue("name")
	price := ctx.FormValue("price")

	//get admin id from auth
	adminId := ctx.Request.Header.Peek("userId")

	resp, err := db.Insert("product_test", map[string]interface{}{
		"product_name": name,
		"price":        price,
		"admin_fkid":   adminId,
	})

	if utils.CheckError(ctx, err) {
		return
	}

	insertedId, _ := resp.LastInsertId()
	json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: fmt.Sprintf("Successfully inserted with id %s", insertedId)})
}

func UpdateProduct(ctx *fasthttp.RequestCtx) {
	//get from Post form
	name := ctx.FormValue("name")
	price := ctx.FormValue("price")
	productId := ctx.FormValue("product_id")

	//get admin id from auth
	adminId := ctx.Request.Header.Peek("userId")

	_, err := db.Update("product_test", map[string]interface{}{
		"product_name": name,
		"price":        price,
		"admin_fkid":   adminId,
	}, "product_id = ?", productId)

	if utils.CheckError(ctx, err) {
		return
	}

	json.NewEncoder(ctx).Encode(models.ApiResponse{Status: true, Message: "Success"})
}
