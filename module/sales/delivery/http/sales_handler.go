package http

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type salesHandler struct {
	uc domain.SalesUseCase
}

func NewHttpSalesHandler(app *fasthttprouter.Router, useCase domain.SalesUseCase) {
	handler := &salesHandler{useCase}
	app.POST("/v1/sales/ranking", auth.ValidateToken(handler.FetchSalesRanking))
	app.POST("/v2/sales/ranking", auth.ValidateToken(handler.FetchSalesRankingV2))
	app.POST("/v1/sales/sales_analysis/:group/:type", auth.ValidateToken(handler.FetchSalesAnalysis))
	app.POST("/v1/purchase/transfer_analysis/:group/:type", auth.ValidateToken(handler.FetchTransferAnalysis))
	app.POST("/v1/purchase/transfer_analysis", auth.ValidateToken(handler.FetchTransferAnalysis))

	//sales-by
	app.POST("/v1/sales-by/media", auth.ValidateToken(handler.FetchSalesByMedia))
	app.GET("/v1/sales-by/media", auth.ValidateToken(handler.FetchSalesByMedia))
	app.GET("/v1/sales-by/order-type", auth.ValidateToken(handler.FetchSalesByOrderType))
	// app.POST("/v1/sales-by/media/detail", auth.ValidateToken(handler.FetchSalesByMediaDetail))

	app.POST("/v1/sales/summary", auth.ValidateToken(handler.FetchSalesSummary))

	//entertain sales
	app.POST("/v1/sales-entertain/promotion", auth.ValidateToken(handler.FetchSalesPromotion))

	//sales history
	app.POST("/v1/sales/transaction", auth.ValidateToken(handler.FetchSalesTransaction))
	app.POST("/v2/sales/transaction", auth.ValidateToken(handler.FetchSalesTransactionV2))
	app.POST("/v1/sales/transaction-detail", auth.ValidateToken(handler.FetchSalesTransactionDetail))
}

func (h *salesHandler) FetchSalesRankingV2(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	// userJson, _ := json.Marshal(user)
	// fmt.Println("header -->", ctx.Request.Header.String())
	// fmt.Println("user --> ", string(userJson))
	// fmt.Println("post -> ", ctx.Request.PostArgs().String())

	post := ctx.Request.PostArgs()

	catIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("category")), ","))
	subCatIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("subCategory")), ","))
	outletIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("outlet")), ","))
	shiftIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("shift")), ","))

	request := domain.SalesRankingRequest{
		StartDate:   cast.ToInt64(post.Peek("startDate")),
		EndDate:     cast.ToInt64(post.Peek("endDate")),
		Limit:       cast.ToInt(post.Peek("limit")),
		Offset:      cast.ToInt(post.Peek("offset")),
		Page:        cast.ToInt(post.Peek("page")),
		SubCategory: subCatIds,
		Category:    catIds,
		Outlet:      user.ValidateOuletAccess(outletIds),
		Shift:       shiftIds,
	}

	reqJson, _ := json.Marshal(request)
	fmt.Println("request --> ", string(reqJson))
	result, err := h.uc.FetchSalesRankingV2(request, user)
	if err != nil {
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesRanking(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	userJson, _ := json.Marshal(user)
	// fmt.Println("header -->", ctx.Request.Header.String())
	fmt.Println("user --> ", string(userJson))
	fmt.Println("post -> ", ctx.Request.PostArgs().String())

	post := ctx.Request.PostArgs()

	catIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("category")), ","))
	subCatIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("subCategory")), ","))
	outletIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("outlet")), ","))
	shiftIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("shift")), ","))

	request := domain.SalesRankingRequest{
		StartDate:   cast.ToInt64(post.Peek("startDate")),
		EndDate:     cast.ToInt64(post.Peek("endDate")),
		Limit:       cast.ToInt(post.Peek("limit")),
		Offset:      cast.ToInt(post.Peek("offset")),
		SubCategory: subCatIds,
		Category:    catIds,
		Outlet:      user.ValidateOuletAccess(outletIds),
		Shift:       shiftIds,
	}

	reqJson, _ := json.Marshal(request)
	fmt.Println("request --> ", string(reqJson))
	result, err := h.uc.FetchSalesRanking(request, user)
	if err != nil {
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesAnalysis(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	group := cast.ToString(ctx.UserValue("group"))
	dataType := cast.ToString(ctx.UserValue("type"))

	if dataType != "nominal" && dataType != "qty" {
		return
	}

	request := domain.GetDataTableRequest(ctx)

	log.Info("request param -> %s", cast.ToStringJson(request))
	var err error
	var result interface{}
	if group == "outlet" {
		result, err = h.uc.FetchSalesAnalysisByOutlet(request, dataType, user)
	} else if group == "day" {
		result, err = h.uc.FetchSalesAnalysisByDay(request, dataType, user)
	} else if group == "date" {
		result, err = h.uc.FetchSalesAnalysisByDate(request, dataType, user)
	} else if group == "hour" {
		result, err = h.uc.FetchSalesAnalysisByHour(request, dataType, user)
	} else if group == "promotion" {
		result, err = h.uc.FetchSalesAnalysisByPromotion(request, dataType, user)
	} else {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"status":  false,
			"message": "invalid request",
		})
		return
	}

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"status":  false,
			"message": err.Error(),
		})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchTransferAnalysis(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	group := cast.ToString(ctx.UserValue("group"))
	dataType := cast.ToString(ctx.UserValue("type"))

	if group == "" {
		group = "outlet"
	}
	if dataType == "" {
		dataType = "qty"
	}

	post := ctx.Request.PostArgs()
	outletIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("outletDestination")), ","))
	shiftIds := array.TransformStringToInt(strings.Split(cast.ToString(post.Peek("shift")), ","))
	request := domain.DataTableRequest{
		StartDate: cast.ToInt64(post.Peek("startDate")),
		EndDate:   cast.ToInt64(post.Peek("endDate")),
		Outlet:    outletIds,
		Limit:     cast.ToInt(post.Peek("limit")),
		Offset:    cast.ToInt(post.Peek("offset")),
		Shift:     shiftIds,
		TimeZone:  cast.ToInt(post.Peek("timeZone")),
		RequestTransfer: domain.RequestTransfer{
			OutletOrigin: cast.ToInt(post.Peek("outletOrigin")),
		},
	}

	if request.OutletOrigin == 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "outletOrigin must be specified"})
		return
	}

	result, err := h.uc.FetchTransferAnalysis(request, dataType, user)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(result)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesByOrderType(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)

	requestParam := domain.SalesReportOrderTypeRequst{
		SalesReportRequest: parser.RequestParamSales(ctx),
		GroupBy:            strings.ToLower(string(ctx.QueryArgs().Peek("group_by"))),
		SalesTagId:         array.TransformStringToInt(strings.Split(string(ctx.QueryArgs().Peek("sales_tag_id")), ",")),
	}

	if requestParam.GroupBy == "type" {
		result, err := h.uc.FetchSalesByOrderType(&user, &requestParam)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
			return
		}
		_ = json.NewEncoder(ctx).Encode(result)
	} else if array.Contain([]string{"monthly", "daily"}, requestParam.GroupBy) {
		result, err := h.uc.FetchSalesByOrderTypeGroup(&user, &requestParam)
		if err != nil {
			ctx.SetStatusCode(fasthttp.StatusInternalServerError)
			_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
			return
		}
		_ = json.NewEncoder(ctx).Encode(result)
	} else {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
	}

}

func (h *salesHandler) FetchSalesByMedia(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	groupBy := string(ctx.QueryArgs().Peek("group_by"))
	dateGroup := string(ctx.QueryArgs().Peek("date_group"))
	groupKey := string(ctx.QueryArgs().Peek("group_key"))

	requestJson, _ := json.Marshal(request)
	var requestParam = domain.SalesReportMediaRequest{}
	json.Unmarshal(requestJson, &requestParam)

	if requestParam.PaymentMethod == nil {
		requestParam.PaymentMethod = make([]string, 0)
	}

	bank := parser.ParseRequestParm(ctx)["bank"]
	log.Info("bank: '%s'", bank)
	for _, method := range strings.Split(string(bank), ",") {
		log.Info("method: '%v'", method)
		if method != "" && utils.IsAlphabet(method) {
			requestParam.PaymentMethod = append(requestParam.PaymentMethod, method)
		}
	}

	requestParam.GroupBy = groupBy
	requestParam.DateGroup = dateGroup
	requestParam.GroupKey = groupKey

	log.Info("groupBy: '%v' | dateGroup: '%v'", groupBy, dateGroup)

	var result map[string]interface{}
	var err error
	if groupKey == "payment" {
		result, err = h.uc.FetchSalesByMediaGroupPayment(user, requestParam)
	} else {
		result, err = h.uc.FetchSalesByMedia(user, requestParam)
	}

	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesSummary(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	result, err := h.uc.FetchSalesSummary(user, request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesPromotion(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	result, err := h.uc.FetchSalesPromotion(user, request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesTransaction(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	log.Info("fetch history, - %v", cast.ToStringJson(request))

	result, err := h.uc.FetchSalesHistoryTransaction(user, request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesTransactionV2(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	log.Info("fetch history, - %v", cast.ToStringJson(request))

	result, err := h.uc.FetchSalesHistoryTransactionV2(user, request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *salesHandler) FetchSalesTransactionDetail(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	log.Info("fetch history detail, - %v", cast.ToStringJson(request))
	result, err := h.uc.FetchSalesHistoryTransactionDetail(user, request)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}

	_ = json.NewEncoder(ctx).Encode(result)
}
