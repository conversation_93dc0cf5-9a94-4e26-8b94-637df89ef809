package models

type SpoilEntity struct {
	SpoilID           int     `json:"spoil_id"`
	ProductDetailFkid int     `json:"product_detail_fkid"`
	Qty               float32 `json:"qty"`
	TimeCreated       int64   `json:"time_created"`
}

func (s *SpoilEntity) ToStockInProduct() StockInProduct {
	return StockInProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.Qty,
		CreatedAt:       s.TimeCreated,
	}
}

func (s *SpoilEntity) ToStockOutProduct() StockOutProduct {
	return StockOutProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.Qty,
		CreatedAt:       s.TimeCreated,
	}
}
