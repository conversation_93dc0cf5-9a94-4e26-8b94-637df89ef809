package usecase

import (
	"sort"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/exception"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type taskUseCase struct {
	repo domain.TaskRepository
}

func NewTaskUseCase(repository domain.TaskRepository) domain.TaskUseCase {
	return &taskUseCase{repository}
}

func (t *taskUseCase) FetchReportPerformance(param *domain.TaskReportRequest) ([]domain.TaskPerformanceResponse, error) {
	results := make([]domain.TaskPerformanceResponse, 0)
	workflowsData, err := t.getWorkflowData(param)
	if log.IfError(err) {
		return nil, err
	}
	if workflowsData == nil {
		return results, nil
	}
	workflows := workflowsData.Workflows
	log.Info("total workflows: %v", len(workflows))
	if len(workflows) < 5 {
		log.Info("workflows: %v", utils.SimplyToJson(workflows))
	}

	if len(workflows) == 0 {
		return results, nil
	}

	masterWorkflow := workflowsData.MasterWorkflowMap
	masterBoard := workflowsData.BoardMap
	workflowCategoryMap := make(map[int]string)

	// Calculate duration between consecutive workflow changes
	durationMap := make(map[int][]float64)
	durationMinMap := make(map[int]float64)
	durationMaxMap := make(map[int]float64)
	workflowTotalBoard := make(map[int]map[int]bool)
	// totalBoardMap := make(map[int][]int64)

	for i := 0; i < len(workflows)-1; i++ {
		movedAt := time.Now().Unix() * 1000
		//workflows[i].TmMasterCategoryWorkflowID == workflows[i+1].TmMasterCategoryWorkflowID
		if workflows[i].TmBoardsID == workflows[i+1].TmBoardsID {
			movedAt = workflows[i+1].CreatedAt
		}
		duration := float64(movedAt-workflows[i].CreatedAt) / 1000 / (60 * 60)
		durationMap[workflows[i].TmMasterCategoryWorkflowID] = append(durationMap[workflows[i].TmMasterCategoryWorkflowID], duration)
		workflowCategoryMap[workflows[i].TmMasterCategoryWorkflowID] = masterBoard[workflows[i].TmBoardsID].CategoryName

		if min, ok := durationMinMap[workflows[i].TmMasterCategoryWorkflowID]; duration < min || !ok {
			durationMinMap[workflows[i].TmMasterCategoryWorkflowID] = duration
		}
		if max, ok := durationMaxMap[workflows[i].TmMasterCategoryWorkflowID]; duration > max || !ok {
			durationMaxMap[workflows[i].TmMasterCategoryWorkflowID] = duration
		}

		if _, ok := workflowTotalBoard[workflows[i].TmMasterCategoryWorkflowID]; !ok {
			workflowTotalBoard[workflows[i].TmMasterCategoryWorkflowID] = make(map[int]bool)
		}
		workflowTotalBoard[workflows[i].TmMasterCategoryWorkflowID][workflows[i].TmBoardsID] = true
	}

	if len(workflows) < 5 {
		log.Info("duration: %v", utils.SimplyToJson(durationMap))
	}

	for workflowID, durations := range durationMap {
		// Calculate average duration
		averageDuration := calculateAverage(durations)

		// Calculate median duration
		medianDuration := calculateMedian(durations)

		// Calculate standard deviation
		stdDevDuration := calculateStandardDeviation(durations, averageDuration)

		// Create TaskPerformanceResponse
		response := domain.TaskPerformanceResponse{
			TmMasterCategoryWorkflowID: workflowID,
			WorkflowName:               masterWorkflow[workflowID].Name,
			WorkflowPosition:           masterWorkflow[workflowID].Position,
			CategoryName:               workflowCategoryMap[workflowID],
			TotalBoards:                len(workflowTotalBoard[workflowID]),
			// BoardIds:                   cast.MapKeysToSlice[int, bool](workflowTotalBoard[workflowID]),
			AverageDuration: averageDuration,
			MedianDuration:  medianDuration,
			StdDevDuration:  stdDevDuration,
			RangeStart:      durationMinMap[workflowID],
			RangeEnd:        durationMaxMap[workflowID],
		}

		// fmt.Printf("total : %v, ids: %v \n", len(workflowTotalBoard[workflowID]), cast.MapKeysToSlice[int, bool](workflowTotalBoard[workflowID]))
		results = append(results, response)
	}

	// Print or process the results as needed
	for i, result := range results {
		log.Debug("Workflow ID: %d, Average: %v, Median: %v, Standard Deviation: %v", result.TmMasterCategoryWorkflowID, result.AverageDuration, result.MedianDuration, result.StdDevDuration)
		if i > 10 {
			break
		}
	}

	// Sorting based on WorkflowPosition and CategoryName
	sort.Slice(results, func(i, j int) bool {
		// First, compare by WorkflowPosition
		if results[i].WorkflowPosition != results[j].WorkflowPosition {
			return results[i].WorkflowPosition < results[j].WorkflowPosition
		}

		// If WorkflowPosition is the same, compare by CategoryName
		return results[i].CategoryName < results[j].CategoryName
	})

	return results, nil
}

// FetchReportTimeAnalysis implements domain.TaskUseCase.
func (t *taskUseCase) FetchReportTimeAnalysis(param *domain.TaskReportRequest) ([]models.TaskTimeAnalysisResponse, error) {
	results := make([]models.TaskTimeAnalysisResponse, 0)
	workflowsData, err := t.getWorkflowData(param)
	if workflowsData == nil || log.IfError(err) {
		return results, err
	}

	workflows := workflowsData.Workflows
	if log.IfError(err) {
		return nil, err
	}
	log.Info("total workflows: %v, request param: %v", len(workflows), cast.ToStringJson(param))

	if len(workflows) == 0 {
		return results, nil
	}

	masterWorkflowMap := workflowsData.MasterWorkflowMap
	boardsMap := workflowsData.BoardMap

	var workflowChanges []models.TimeAnalysisWorkflow
	var totalDuration float64

	for i := 0; i < len(workflows); i++ {
		endTime := time.Now().Unix() * 1000
		hasNext := false
		if hasNext = i < len(workflows)-1 && workflows[i+1].TmBoardsID == workflows[i].TmBoardsID; hasNext {
			endTime = workflows[i+1].CreatedAt
		}

		workflowChanges = append(workflowChanges, models.TimeAnalysisWorkflow{
			WorkflowID: workflows[i].TmMasterCategoryWorkflowID,
			Name:       masterWorkflowMap[workflows[i].TmMasterCategoryWorkflowID].Name,
			Duration:   float64(endTime-workflows[i].CreatedAt) / 1000 / (60 * 60), //get in hour
			TimeStart:  workflows[i].CreatedAt,
			TimeEnd:    endTime,
		})
		totalDuration += workflowChanges[len(workflowChanges)-1].Duration

		if !hasNext {
			results = append(results, models.TaskTimeAnalysisResponse{
				BoardsID:      workflows[i].TmBoardsID,
				Workflows:     workflowChanges,
				Name:          boardsMap[workflows[i].TmBoardsID].Name,
				Description:   boardsMap[workflows[i].TmBoardsID].Description,
				TotalDuration: totalDuration,
			})

			//reset...
			workflowChanges = make([]models.TimeAnalysisWorkflow, 0)
			totalDuration = 0
		}
	}

	// fmt.Println(">>> ", cast.ToStringJson(results))

	return results, nil
}

func (t *taskUseCase) getWorkflowData(param *domain.TaskReportRequest) (*domain.WorkflowDataWrapper, error) {
	workflows, err := t.repo.FetchTaskWorkflowChanges(param)
	if log.IfError(err) {
		return nil, err
	}
	log.Info("total workflows: %v", len(workflows))
	if len(workflows) == 0 {
		return nil, nil
	}

	boardsIdsMap := make(map[int]bool)
	workflowIdsMap := make(map[int]bool)

	for _, workflow := range workflows {
		boardsIdsMap[workflow.TmBoardsID] = true
		workflowIdsMap[workflow.TmMasterCategoryWorkflowID] = true
	}

	boardIds := cast.MapKeysToSlice[int, bool](boardsIdsMap)

	lastBoardStatus, err := t.repo.FetchLastWorkflowBoard(boardIds...)
	log.IfError(err)

	lastBoardWorkflowmap := make(map[int]models.BoardWorkflowEntity)
	for _, workflow := range lastBoardStatus {
		workflowIdsMap[workflow.TmMasterCategoryWorkflowID] = true
		lastBoardWorkflowmap[workflow.TmBoardsID] = workflow
	}

	boards, err := t.repo.FetchBoardTimeAnalysis(boardIds...)
	log.IfError(err)

	workflowIds := cast.MapKeysToSlice[int, bool](workflowIdsMap)
	masterWorkflow, err := t.repo.FetchWorkflow(workflowIds...)
	log.IfError(err)

	masterWorkflowMap := make(map[int]models.MasterWorkflowEntity)
	for _, workflow := range masterWorkflow {
		masterWorkflowMap[workflow.ID] = workflow
	}

	boardsMap := make(map[int]models.TimeAnalysisBoardResponse)
	for _, board := range boards {
		board.Status = masterWorkflowMap[lastBoardWorkflowmap[board.ID].TmMasterCategoryWorkflowID].Name
		boardsMap[board.ID] = board
	}

	return &domain.WorkflowDataWrapper{
		Workflows:         workflows,
		BoardMap:          boardsMap,
		MasterWorkflowMap: masterWorkflowMap,
	}, nil
}

// FetchReportPerformanceByWorkflow implements domain.TaskUseCase.
func (t *taskUseCase) FetchReportPerformanceByWorkflow(param *domain.TaskReportRequest) ([]domain.TaskPerformanceWorkflowResponse, error) {
	results := make([]domain.TaskPerformanceWorkflowResponse, 0)
	if param.WorkflowId == 0 {
		return results, &exception.ValidationError{Data: map[string]interface{}{
			"workflow_id": "workflow_id can not be empty",
		}}
	}

	workflowsData, err := t.getWorkflowData(param)
	if log.IfError(err) || workflowsData == nil {
		return results, err
	}

	workflows := workflowsData.Workflows
	boardMap := workflowsData.BoardMap
	log.Info("total workflows: %v, with params: %v", len(workflows), cast.ToStringJson(param))

	var lastBoardId int
	for i := 0; i < len(workflows)-1; i++ {
		if lastBoardId == workflows[i].TmBoardsID {
			continue
		}
		if workflows[i].TmMasterCategoryWorkflowID == param.WorkflowId {
			movedAt := time.Now().Unix() * 1000
			if workflows[i].TmBoardsID == workflows[i+1].TmBoardsID {
				movedAt = workflows[i+1].CreatedAt
			}
			duration := float64(movedAt-workflows[i].CreatedAt) / 1000 / (60 * 60)
			lastBoardId = workflows[i].TmBoardsID
			log.Debug("%v --> %v", workflows[i].TmBoardsID, duration)

			results = append(results, domain.TaskPerformanceWorkflowResponse{
				TimeAnalysisBoardResponse: boardMap[lastBoardId],
				Duration:                  duration,
				TimeStart:                 workflows[i].CreatedAt,
				TimeEnd:                   movedAt,
			})
		}
	}
	return results, nil
}
