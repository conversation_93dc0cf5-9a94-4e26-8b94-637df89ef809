package usecase

import (
	"fmt"
	"math"
	"os"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func recalculateHpp(hpps []domain.Hpp) ([]domain.Hpp, map[int][]models.HppUpdate) {
	//calculate the stock out, for testing at the end
	originStockOutTotal := sumStockOut(hpps)
	hppUpdates := make(map[int][]models.HppUpdate)

	logLine := log.Line()
	defer logLine.PrintDebug()

	logLine.AddLog("recalculateHpp, total hpp: %v", len(hpps))
	logLine.AddLog("original hpp: %v", utils.SimplyT<PERSON><PERSON><PERSON>(hpps))
	for i, hpp := range hpps {
		//in case in greater than out
		if hpps[i].StockIn > hpps[i].StockOut && i < len(hpps)-1 {
			logLine.AddLog("---- Go Up Up...")
			need := hpps[i].StockIn - hpps[i].StockOut
			logLine.AddLog("at %v need: %v, in %v, out %v", hpps[i].StockPriceId, need, hpps[i].StockIn, hpps[i].StockOut)
			if need == 0 {
				logLine.AddLog(">>> skip need %v", need)
				continue
			}
			for j := i + 1; j < len(hpps); j++ {
				hppN := (hpps)[j]
				if hppN.StockOut == 0 { //if current stockOut already taken all, continue to next
					logLine.AddLog("continue.... need: %v, stockOut: %v", need, hppN.StockOut)
					continue
				}
				taken := hppN.StockOut
				if taken >= need {
					logLine.AddLog("take all, as needed, out: %v, need: %v, inId: %v", hppN.StockOut, need, hppN.StockInId)
					taken = need
				}

				hpps[j].StockOut = hppN.StockOut - taken    //update the taken stock
				hpps[i].StockOut = hpps[i].StockOut + taken //update the needed stock

				// if taken all, reset to 0
				if hppN.StockOut < taken {
					logLine.AddLog("reset stockOut to 0, out: %v, taken: %v", hpps[j].StockOut, taken)
					hpps[j].StockOut = 0
				}

				hppUpdate := models.HppUpdate{
					StockPriceId:    hpps[j].StockPriceId,
					Price:           hpp.Price,
					Qty:             taken,
					ProductDetailId: hpp.ProductDetailFkid,
				}

				hppUpdates[hpp.StockPriceId] = append(hppUpdates[hpp.StockPriceId], hppUpdate)
				need -= taken
				logLine.AddLog("at %v taken %v, QtyLeft: %v, need: %v", hppN.StockPriceId, taken, hpps[j].StockOut, need)
				if need <= 0 {
					logLine.AddLog("finish at %v", hppN.StockPriceId)
					break
				}
			}
		} else if hpp.StockIn < hpp.StockOut && hpp.StockInSource != "" {
			logLine.AddLog("------ go down... stockOut its bigger than in... %v, in: %v, out: %v", hpp.StockPriceId, hpp.StockIn, hpp.StockOut)
			hpps[i].StockOut = hpp.StockIn
			borrowedItems := make(map[int]float32)
			borrowedItems[i] = hpp.StockOut - hpp.StockIn
			hpps[i].StockOut = hpp.StockIn //make it equal
			tmpHpp := domain.Hpp{
				Price:             hpps[i].Price,
				StockOut:          0,
				ProductDetailFkid: hpps[i].ProductDetailFkid,
			}

			lastPrice := hpps[i].Price
			stockOutAdded := make(map[int]float32)
			for j := i; j < len(hpps); j++ {
				left := borrowedItems[j] - stockOutAdded[j]
				if borrowedItems[j] < stockOutAdded[j] {
					left = borrowedItems[j]
				}
				// riginalStockOut := hpps[j].StockOut - stockOutAdded[j]
				logLine.AddLog("|| %v need %v || added: %v", hpps[j].StockPriceId, left, stockOutAdded[j])
				if left <= 0 {
					logLine.AddLog(">>> skip need %v", left)
					continue
				}
				for k := j + 1; k < len(hpps); k++ {
					logLine.AddLog(">> %v in: %v, borrow: %v, added: %v", hpps[k].StockPriceId, hpps[k].StockIn, borrowedItems[k], stockOutAdded[k])
					if stockLeft := (hpps[k].StockIn - (borrowedItems[k] + stockOutAdded[k])); stockLeft <= 0 && !hpps[k].HasNoStockIn() {
						logLine.AddLog("%v empty... %v - (%v + %v) = %v", hpps[k].StockPriceId, hpps[k].StockIn, borrowedItems[k], stockOutAdded[k], stockLeft)
						continue
					}

					// taken := hpps[k].StockOut + (hpps[k].StockIn - hpps[k].StockOut) - stockOutAdded[k] //- borrowedItems[k]
					taken := hpps[k].StockIn - stockOutAdded[k] - borrowedItems[k]
					if taken > left || hpps[k].HasNoStockIn() {
						taken = left
					}

					if !hpps[k].HasNoStockIn() {
						originalStockOut := hpps[k].StockOut - stockOutAdded[k]
						logLine.AddLog("%v out: %v, added: %v, original: %v, taken: %v", hpps[k].StockPriceId, hpps[k].StockOut, stockOutAdded[k], originalStockOut, taken)

						if hpps[k].StockOut < hpps[k].StockIn {
							if hpps[k].StockOut+taken <= hpps[k].StockIn {
								stockOutAdded[k] += taken //add to stock added
							} else {
								stockOutAdded[k] += hpps[k].StockIn - hpps[k].StockOut //add all as stockAdded
							}
						}

						// if add := taken - originalStockOut; add > 0 {
						// 	// stockOutAdded[k] += add
						// }
						if stockOutAdded[k] != 0 {
							logLine.AddLog("%v, stockAdded now: %v | taken: %v", hpps[k].StockPriceId, stockOutAdded[k], taken)
						}

						borrowedItems[k] += taken //- stockOutAdded[k]
						hpps[k].StockOut += taken
						borrowed := float32(0)
						if hpps[k].StockOut > hpps[k].StockIn {
							logLine.AddLog("-- set StockOut same as stockIn, before: %v | now: %v | diff: %v", hpps[k].StockOut, hpps[k].StockIn, hpps[k].StockOut-hpps[k].StockIn)
							borrowed = hpps[k].StockOut - hpps[k].StockIn
							hpps[k].StockOut = hpps[k].StockIn
						}

						if borrowedItems[k] < 0 {
							logLine.AddLog("[WARN] borrowed %v becomes minus: %v, set to zero", hpps[k].StockPriceId, borrowedItems[k])
							borrowedItems[k] = 0
						}

						//if has borrowed, but stock still left, use that
						if borrowedItems[k] > 0 && (hpps[k].StockIn-hpps[k].StockOut) >= borrowedItems[k] {
							remaining := (hpps[k].StockIn - hpps[k].StockOut)
							// borrowedItems[k] -= remaining
							// hpps[k].StockOut += remaining
							// stockOutAdded[k] += remaining
							logLine.AddLog("adjust borrowed items..., minus: %v", remaining)
						}

						//if current stock still smaller than stock in, no borrow item
						if (originalStockOut + taken) < hpps[k].StockIn {
							// borrowedItems[k] = borrowed + stockOutAdded[k]
							borrowedItems[k] = borrowed
							logLine.AddLog("borrowsed set: %v", borrowed)
							logLine.AddLog("set borrowedItems %v to %v, originalStock: %v, taken: %v, added: %v,stockIn: %v", hpps[k].StockPriceId, borrowedItems[k], originalStockOut, taken, stockOutAdded[k], hpps[k].StockIn)
						}
					} else {
						logLine.AddLog("%v has no stockIn : %v", hpps[k].StockPriceId, utils.SimplyToJson(hpps[k]))
						hpps[k].StockOut += taken
						stockOutAdded[k] += taken
					}

					left -= taken
					logLine.AddLog("at %v taken: %v (stock_out now %v) by %v | left: %v | borrowed: %v", hpps[k].StockPriceId, taken, hpps[k].StockOut, j, left, borrowedItems[k])
					lastPrice = hpps[k].Price
					hppUpdates[hpps[k].StockPriceId] = append(hppUpdates[hpps[k].StockPriceId], models.HppUpdate{
						StockPriceId:    hpps[j].StockPriceId,
						Price:           hpps[k].Price,
						Qty:             taken,
						ProductDetailId: hpps[k].ProductDetailFkid,
					})

					if left <= 0 {
						logLine.AddLog("finish at %v", hpps[k].StockPriceId)
						break
					}
					logLine.AddLog("### %v", k)
				}

				//if there is still qty left, add new row
				if left > 0 {
					tmpHpp.Price = hpps[len(hpps)-1].Price
					tmpHpp.StockOut += left
					hppUpdates[tmpHpp.StockPriceId] = append(hppUpdates[tmpHpp.StockPriceId], models.HppUpdate{
						StockPriceId:    hpps[j].StockPriceId,
						Price:           tmpHpp.Price,
						Qty:             left,
						ProductDetailId: hpps[j].ProductDetailFkid,
					})

					logLine.AddLog("add left to tmp-hpp: %v, total out tmp: %v, lastPrice: %v", left, tmpHpp.StockOut, lastPrice)
				}
				logLine.AddLog("-----------------------------------")
			}
			logLine.AddLog("tmpHpp: %v", utils.SimplyToJson(tmpHpp))

			if tmpHpp.StockOut > 0 {
				hpps = append(hpps, tmpHpp)
			}
		} else {
			logLine.AddLog(">> %v is normal, in: %v, out: %v, inId: %v", hpp.StockPriceId, hpp.StockIn, hpp.StockOut, hpp.StockInId)
		}
		logLine.AddLog("------------")
	}

	//compare new total stockOut
	if newTotal := sumStockOut(hpps); newTotal != originStockOutTotal || isStockNotMatch(hpps, hppUpdates) {
		logLine.AddLog("stockOut calculate after adjust not the same, %v now: %v", originStockOutTotal, newTotal)
		log.IfError(fmt.Errorf("stockOut calculate after adjust not the same, %v now: %v", originStockOutTotal, newTotal))
		log.Info("hpps: %v", utils.SimplyToJson(hpps))
		logLine.AddLog("updates: %v", cast.ToStringJson(hppUpdates))
		fmt.Println(logLine.String())
	}

	return hpps, hppUpdates
}

func (h *hppUseCase) AdjustStockOpname(stockInId, stockInSource string, stockInNew float32) error {
	logLine := log.Line()
	defer logLine.Print()

	log.Info("AdjustStockOpname, %v #%v - qty: %v", stockInSource, stockInId, stockInNew)
	//fetch original stock_in
	stockPrices, err := h.repo.FetchStockPriceByIdAndSource(stockInId, stockInSource)
	logLine.AddLog("stockPrice of %v (%v) %v", stockInId, stockInSource, stockPrices)
	if log.IfError(err) {
		return err
	}
	if stockPrices == nil || len(*stockPrices) == 0 {
		logLine.AddLog(">>> no stockPriceFound: %v (%v)", stockInId, stockInSource)
		return fmt.Errorf("no stock_price found for %v (%v)", stockInId, stockInSource)
	}

	//compare different
	stockInOld := float32(0)
	for _, stock := range *stockPrices {
		stockInOld += stock.StockIn
	}

	diff := stockInNew - stockInOld
	logLine.AddLog("stockInOld: %v VS new: %v || diff: %v", stockInOld, stockInNew, diff)

	//get nearest stockOpname
	stockOpnamesIn, err := h.repo.FetchNearestStockPrice((*stockPrices)[0].ProductDetailFkid, (*stockPrices)[0].CreatedAt, domain.StockOpname.String())
	logLine.AddLog("stockOpnames for %v after %v : %v", (*stockPrices)[0].ProductDetailFkid, (*stockPrices)[0].CreatedAt, (stockOpnamesIn))
	if log.IfError(err) {
		return err
	}

	stockOpnames := make([]domain.Hpp, 0)

	//fetch stock_price_detail, then filter only for stock_opname
	details, err := h.repo.FetchStockPriceDetail((*stockPrices)[0].StockPriceId)
	log.IfError(err)
	if details == nil {
		details = &[]models.StockPriceDetailEntity{}
	}

	for _, detail := range *details {
		if detail.StockOutSource == domain.StockOpname.String() {
			// stockOpnames = append(stockOpnames, domain.Hpp{
			// 	StockInId:     detail.StockOutID,
			// 	StockInSource: detail.StockOutSource,
			// 	StockIn:       detail.StockOut,
			// })
		}
	}

	logLine.AddLog("stockOpname from its details: %v", len(stockOpnames))
	stockOpnames = append(stockOpnames, stockOpnamesIn...)

	//if no stockOpname data, no need to continue the process
	if len(stockOpnames) == 0 || diff == 0 {
		log.Info(">> skip no stockOpname")
		return nil
	}

	//---- if newStocknIn > oldStockIn
	//#Reducing existing StockOpname
	// 1. Reduce stock_in opname (by the diff)
	// 2. if still remain, use the remaining to add stock_price_detail

	//----- if newStockIn < oldStockIn
	//#Increase existing StockOpname
	// 1. Reduce stock_price_detail (*also adjust stock_price stock_out column)
	// 2. if still remain, use that to add to stock_in

	if stockInNew > stockInOld {
		for i, hpp := range stockOpnames {
			if hpp.StockIn == 0 {
				continue
			}
			taken := hpp.StockIn
			if hpp.StockIn > diff {
				taken = diff
			}
			diff -= taken
			logLine.AddLog("diff now: %v, in %v (%v), taken %v, new stockIn: %v", diff, hpp.StockIn, hpp.StockPriceId, taken, hpp.StockIn-taken)

			stockOpnames[i].StockIn -= taken
			log.IfError(h.repo.UpdateStockPriceQtyIn(hpp.StockPriceId, stockOpnames[i].StockIn))

			if diff <= 0 {
				logLine.AddLog("finish.... at %v, index: %v", hpp.StockPriceId, i)
				return nil
			}
		}

		logLine.AddLog("after reducing stock_in opname, left: %v", diff)
		//add to stock_price_detail
		//first fetch, stock_price after stock_opname, we will take
		// stockPrice, err := h.repo.FetchNearestStockPrice(stockOpnames[0].ProductDetailFkid, stockOpnames[0].CreatedAt, "")
		// if log.IfError(err) {
		// 	return nil
		// }

		// //if no data, just insert new one
		// if len(stockPrice) == 0 {
		// 	id, err := h.repo.InsertStockPrice(domain.Hpp{
		// 		StockOut:          diff,
		// 		StockOutId:        stockOpnames[0].StockInId,
		// 		StockOutSource:    domain.StockOpname.String(),
		// 		ProductDetailFkid: stockOpnames[0].ProductDetailFkid,
		// 	})
		// 	log.IfError(err)
		// 	logLine.AddLog("add left to new stockPrice, qty: %v, id: %v", diff, id)
		// } else {
		// 	err = h.repo.InsertStockOutDetail(domain.StockPriceDetail{
		// 		StockPriceId:   stockOpnames[0].StockPriceId,
		// 		Qty:            diff,
		// 		StockOutId:     stockOpnames[0].StockInId,
		// 		StockOutSource: domain.StockOpname.String(),
		// 	})
		// 	log.IfError(err)
		// 	logLine.AddLog("add stockPriceDetail to: %v, qty: %v", stockOpnames[0].StockPriceId, diff)
		// }

		if diff > 0 {
			err = h.repo.InsertStockOutDetail(domain.StockPriceDetail{
				StockPriceId:   (*stockPrices)[0].StockPriceId,
				Qty:            diff,
				StockOutId:     stockOpnames[0].StockInId,
				StockOutSource: domain.StockOpname.String(),
			})
			log.IfError(err)
			logLine.AddLog("add stockPriceDetail to: %v, qty: %v", (*stockPrices)[0].StockPriceId, diff)
		}

	} else if stockInNew < stockInOld {
		//make the number absolute
		diff = float32(math.Abs(float64(diff)))
		//fetch stockPriceDetail
		stockDetails, err := h.repo.FetchStockPriceDetailByOutSourceId(stockOpnames[0].StockInId, stockOpnames[0].StockInSource)
		logLine.AddLog("total stockPriceDetail opname of %v: %v", stockOpnames[0].StockInId, len(stockDetails))
		if log.IfError(err) {
			return nil
		}

		for _, detail := range stockDetails {
			taken := detail.StockOut
			if detail.StockOut > diff {
				taken = diff
			}
			diff -= taken
			logLine.AddLog("from %v, qty: %v, taken: %v, left: %v", detail.StockPriceDetailID, detail.StockOut, taken, diff)
			err = h.repo.UpdateStockPriceDetailQty(detail.StockPriceDetailID, detail.StockOut-taken)
			log.IfError(err)
		}

		// if still remain, use that to add to stock_in
		logLine.AddLog("after taken from detail, diff remains: %v", diff)
		if diff > 0 {
			err = h.repo.UpdateStockPriceQtyIn(stockOpnames[0].StockPriceId, stockOpnames[0].StockIn+diff)
			log.IfError(err)
			logLine.AddLog("stockPrice of %v updated to %v, from %v", stockOpnames[0].StockPriceId, diff, stockOpnames[0].StockIn)
		}

	}
	return nil
}

func sumStockOut(hpps []domain.Hpp) float32 {
	var totalStockOut float32
	for _, hpp := range hpps {
		totalStockOut += hpp.StockOut
	}
	return totalStockOut
}

func isStockNotMatch(hpps []domain.Hpp, changes map[int][]models.HppUpdate) bool {
	hasErr := false
	outChanges := make(map[int]float32)
	for id, details := range changes {
		for _, stock := range details {
			outChanges[id] += stock.Qty
		}
	}

	for _, hpp := range hpps {
		if outChanges[hpp.StockPriceId] > hpp.StockOut {
			log.Info("recalculateHpp error, stockGreater (%v) %v to %v", hpp.StockPriceId, hpp.StockOut, outChanges[hpp.StockPriceId])
			hasErr = true
		}
	}

	if hasErr && os.Getenv("ENV") == "localhost" {
		panic(fmt.Errorf("changes not match..."))
	}
	return hasErr
}
