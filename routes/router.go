package routes

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/controller/stock"
)

func CreateRoutes() *fasthttprouter.Router {
	router := fasthttprouter.New()
	router.GET("/", Index)

	//router = SetWelcomeRoutes(router)
	router = SetAuthRoutes(router)
	router = SetReportSalesRoutes(router)
	router = SetReportFinanceRoutes(router)
	router = SetDashboardRoutes(router)

	//testing purpose
	router.GET("/stock/summary/:adminId/:email", stock.TestStockReport)
	router.GET("/stock/summary/:adminId", stock.TestStockReport)
	router.POST("/stock/summary-check", stock.CheckDiffStock)

	router.GET("/test/pending", TestPending)

	return router
}

func Index(ctx *fasthttp.RequestCtx) {
	ctx.SetContentType("text/plain")
	ctx.Write([]byte(`hello world`))
	ctx.SetStatusCode(fasthttp.StatusOK)
}

func TestPending(ctx *fasthttp.RequestCtx) {
	id := string(ctx.QueryArgs().Peek("id"))
	err := NewSales(id)
	ctx.Write([]byte(fmt.Sprintf("error: %v", err)))
}

func NewSales(salesID string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	if !pollForSalesData(ctx, salesID) {
		fmt.Println("time out....")
		return errors.New("Sales data not found or pending after polling")
	}
	return nil
}

func pollForSalesData(ctx context.Context, salesID string) bool {
	for i := 0; i < 90; i++ {
		fmt.Println("loop...", salesID)
		if isSalesDataAvailable(salesID) {
			return true
		}
		select {
		case <-ctx.Done():
			return false
		case <-time.After(1 * time.Second):
		}
	}
	return false
}

func isSalesDataAvailable(salesId string) bool {
	fmt.Println("checking sales...")
	return false
}
