package mysql

import (
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// sales by
func (s salesRepository) FetchSalesTagByOutlet(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesTagByOutlet, error) {
	sql := `SELECT sum(s.grand_total) as total, count(s.sales_id) as total_transaction, s.outlet_fkid, s.sales_tag_fkid, o.name as outlet_name 
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.status='Success' and s.sales_tag_fkid is not null 
	and o.admin_fkid=@adminId
	and s.time_created BETWEEN @startDate AND @endDate
	$WHERE
	group by s.outlet_fkid, s.sales_tag_fkid`

	var sqlWhere strings.Builder
	if len(request.SalesTagId) > 0 {
		sqlWhere.WriteString(" AND s.sales_tag_fkid IN @salesTagIds ")
	}
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":     user.BusinessId,
		"startDate":   request.StartDate,
		"endDate":     request.EndDate,
		"salesTagIds": request.SalesTagId,
		"outletIds":   request.Outlet,
		"WHERE":       sqlWhere.String(),
	})

	var result []models.SalesTagByOutlet
	// err := s.Query(sql, params...).Model(&result)
	err := s.QueryCache("sales-tag-by-outlet", sql, params...).Model(&result)
	return &result, err
}

func (s salesRepository) FetchSalesByOrderTypeGroup(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesTagByDate, error) {
	sql := `SELECT 
	SUM(s.grand_total) AS total, 
	COUNT(s.sales_id) AS total_transaction,
	s.outlet_fkid, 
	s.sales_tag_fkid, 
	o.name AS outlet_name, 
	FROM_UNIXTIME(
	  s.time_created / 1000 + @timeZone, $dateFormat
	) AS date
  FROM 
	sales s 
	JOIN outlets o ON o.outlet_id = s.outlet_fkid 
  WHERE 
	o.admin_fkid = @adminId
	and s.status = 'Success' 
	and s.data_status = 'on' 
	AND s.sales_tag_fkid IS NOT NULL 
	and s.time_created BETWEEN @startDate AND @endDate
	$where 
  GROUP BY 
	s.outlet_fkid, 
	s.sales_tag_fkid, 
	date
  `
	dateFormatMap := map[string]string{
		"monthly": "'%M/%Y'",
		"daily":   "'%d/%m/%Y'",
	}

	var sqlWhere strings.Builder
	if len(request.SalesTagId) > 0 {
		sqlWhere.WriteString(" AND s.sales_tag_fkid IN @salesTagIds ")
	}
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" AND s.outlet_fkid IN @outletIds ")
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":     user.BusinessId,
		"timeZone":    request.TimeZone,
		"startDate":   request.StartDate,
		"endDate":     request.EndDate,
		"dateFormat":  dateFormatMap[request.GroupBy],
		"salesTagIds": request.SalesTagId,
		"outletIds":   request.Outlet,
		"where":       sqlWhere.String(),
	})

	var result []models.SalesTagByDate
	// err := s.Query(sql, params...).Model(&result)
	err := s.QueryCache("sales-by-order-type-group", sql, params...).Model(&result)
	return &result, err
}

func (s salesRepository) FetchSalesTotalByOutlet(startDate, endDate int64, outletIds ...any) ([]map[string]any, error) {
	sql := `SELECT * FROM 
	(SELECT sum(grand_total) as total, count(sales_id) as total_transaction, 
	outlet_fkid, o.name as outlet_name 
	FROM sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.status='Success'
	and s.time_created BETWEEN @startDate AND @endDate
	group by s.outlet_fkid) tmp 
	WHERE outlet_fkid in @outletIds `

	sql, params := db.MapParam(sql, map[string]any{
		"outletIds": outletIds,
		"startDate": startDate,
		"endDate":   endDate,
	})
	return s.QueryCache("sales-total-by-outlet", sql, params...).MapArray()
}
