variables:
  DOCKER_DRIVER: "overlay2"
  IMAGE_NAME: gcr.io/${G_PROJECT_ID}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}-latest
  GITLAB_IMAGE_NAME: ${CI_REGISTRY}/${CI_PROJECT_PATH}/${CI_COMMIT_REF_NAME}:latest
  DOCKER_BUILDKIT: "1"         


stages:
  - build
  - deploy

.baseBuildImage:
  stage: build
  image: docker:26.1.1-alpine3.19
  rules:
    - if: $SKIP_BUILD == "true"
      when: never
    - if: '$CI_COMMIT_BRANCH == "dev" || $CI_COMMIT_BRANCH == "staging" || $CI_COMMIT_BRANCH == "staging-test"'
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'
      when: manual
  services:
    - docker:26.1.1-dind-alpine3.19
  before_script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
  script:
    - df -h 
    - free -h
    - docker build -t ${GITLAB_IMAGE_NAME} --pull .
    - docker push ${GITLAB_IMAGE_NAME}
  after_script:
    - docker logout ${CI_REGISTRY}
  cache:
    paths:
      - vendor/  

buildImagePublicRunner:
  extends: .baseBuildImage
  tags:
    - saas-linux-medium-amd64

buildImageVmRunner:
  extends: .baseBuildImage 
  needs:
    - job: buildImagePublicRunner
  when: on_failure
  tags:
    - testing-docker


.deploy:
  stage: deploy
  image: docker/compose:alpine-1.29.2
  script:    
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - mkdir -p /data/api-report
    - echo ${RSAKEY_PRIV} | base64 -d > app.rsa
    - echo ${RSAKEY_PUB} | base64 -d > app.rsa.pub
    - cat $GOOGLE_CREDENTIAL >  /data/api-report/google_credential.json
    - cat $ENV > .env
    - cat $BIGQUERY_CREDENTIAL > /data/api-report/bigquery_credential.json
    - docker pull ${GITLAB_IMAGE_NAME}
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} --restart unless-stopped -p 3102:80 --env-file .env --network uniq-network -v /docker/runner/data/api-report/:/credential/ --log-driver=gcplogs --log-opt gcp-project=uniq-187911 -d $GITLAB_IMAGE_NAME
    - docker cp app.rsa $CI_PROJECT_NAME:/app.rsa
    - docker cp app.rsa.pub $CI_PROJECT_NAME:/app.rsa.pub
    - docker logout ${CI_REGISTRY}
  tags:
    - testing-docker

deployDevelopment:
  extends: 
    - .deploy
  environment:
    name: development
  variables:
    CI_PROJECT_NAME: uniqpos-api-report
  only:
    - dev

deployStaging:
  extends: 
    - .deploy
  environment:
    name: staging
  only:
    - staging
  tags:
    - staging

deploy_staging:
  stage: deploy
  image: docker:19.03.10
  when: manual
  environment:
    name: staging
  only:
    - staging
    - staging-test
  script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - mkdir -p /data/api-report
    - echo ${RSAKEY_PRIV} | base64 -d > app.rsa
    - echo ${RSAKEY_PUB} | base64 -d > app.rsa.pub
    - cat $GOOGLE_CREDENTIAL >  /data/api-report/google_credential.json
    - cat $ENV > .env
    - cat $BIGQUERY_CREDENTIAL > /data/api-report/bigquery_credential.json
    - docker pull ${GITLAB_IMAGE_NAME}
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run --name ${CI_PROJECT_NAME} --network=uniq-network -d -p 3102:80 --env-file .env -v /docker/runner/data/api-report/:/credential/ --restart unless-stopped --log-driver=gcplogs --log-opt gcp-project=uniq-187911 -m 600m --cpus="0.3"  $GITLAB_IMAGE_NAME    
    - docker cp app.rsa $CI_PROJECT_NAME:/app.rsa
    - docker cp app.rsa.pub $CI_PROJECT_NAME:/app.rsa.pub
    - docker logout ${CI_REGISTRY}
  tags:
    - staging

deploy_production:
  stage: deploy
  image: docker:19.03.10
  environment:
    name: production
  only:
    - master
  script:
    - docker login -u ${CI_REGISTRY_USER} -p ${CI_REGISTRY_PASSWORD} ${CI_REGISTRY}
    - mkdir -p /data/api-report/config/auth
    - echo ${RSAKEY_PRIV_PRODUCTION} | base64 -d > /data/api-report/config/auth/app.rsa
    - echo ${RSAKEY_PUB_PRODUCTION} | base64 -d > /data/api-report/config/auth/app.rsa.pub
    - echo ${RSAKEY_PRIV_PRODUCTION} | base64 -d > app.rsa
    - echo ${RSAKEY_PUB_PRODUCTION} | base64 -d > app.rsa.pub
    - cat $GOOGLE_CREDENTIAL >  /data/api-report/google_credential.json
    - cat $ENV > .env
    - docker pull $GITLAB_IMAGE_NAME
    - docker container rm -f ${CI_PROJECT_NAME} || true
    - docker run -d --network uniq-network --restart unless-stopped --name ${CI_PROJECT_NAME} -p 3102:3100 --env-file .env -v /data/api-report/config:/config -v /docker/runner/data/api-report/:/credential/  --log-driver=gcplogs -m 600m --cpus="0.5"  $GITLAB_IMAGE_NAME
    - docker cp app.rsa $CI_PROJECT_NAME:/app.rsa
    - docker cp app.rsa.pub $CI_PROJECT_NAME:/app.rsa.pub
  tags:
    - production


deploy_cloudrun_staging:
  stage: deploy
  image: google/cloud-sdk:alpine
  only:
    - staging/run
  script:
    - echo ${ENV_STAGING_UNIX} | base64 -d > .env
    - echo ${RSAKEY_PRIV} | base64 -d > config/auth/app.rsa
    - echo ${RSAKEY_PUB} | base64 -d > config/auth/app.rsa.pub
    - echo $G_GCLOUD_RUN_KEY | base64 -d > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud container images delete $IMAGE_NAME --quiet || true
    - gcloud builds submit --tag $IMAGE_NAME  .
    - gcloud run deploy ${CI_PROJECT_NAME}-staging --image $IMAGE_NAME --platform managed --region asia-east1 --allow-unauthenticated --add-cloudsql-instances uniq-187911:asia-southeast1:primary-db


deploy_cloudrun:
  stage: deploy
  image: google/cloud-sdk:alpine
  only:
    - master/run
  script:
    - echo ${ENV_PRODUCTION_UNIX} | base64 -d > .env
    - echo ${RSAKEY_PRIV_PRODUCTION} | base64 -d > config/auth/app.rsa
    - echo ${RSAKEY_PUB_PRODUCTION} | base64 -d > config/auth/app.rsa.pub
    - echo $G_GCLOUD_RUN_KEY | base64 -d > ${HOME}/gcloud-service-key.json
    - gcloud auth activate-service-account --key-file ${HOME}/gcloud-service-key.json
    - gcloud config set project $G_PROJECT_ID
    - gcloud config set gcloudignore/enabled false
    - gcloud container images delete $IMAGE_NAME --quiet || true
    - gcloud builds submit --tag $IMAGE_NAME  .
    - gcloud run deploy ${CI_PROJECT_NAME} --image $IMAGE_NAME --platform managed --region asia-east1 --allow-unauthenticated --add-cloudsql-instances uniq-187911:asia-southeast1:primary-db
