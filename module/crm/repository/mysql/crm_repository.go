package mysql

import (
	"database/sql"
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
)

type crmRepository struct {
	db         mysql.Repository
	queryParam domain.RequestParam
}

func NewMysqlCrmRepository(db *sql.DB) crm.Repository {
	return &crmRepository{db: mysql.Repository{Conn: db}}
}

func (c crmRepository) QueryParam(param domain.RequestParam) crm.Repository {
	c.queryParam = param
	return c
}

func (c crmRepository) FetchCustomerList(user domain.User) ([]map[string]interface{}, error) {
	sql := `SELECT
	SUM(grand_total) AS grand_total,
	MIN(s.time_created) as date_transaction_min,
	MAX(s.time_created) as date_transaction_max,
	COUNT(*) as total_bill,
	ANY_VALUE(customer_name) AS customer_name, ANY_VALUE(receipt_receiver) as receipt_receiver 
  FROM
	sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
  WHERE
	status='Success'  
	AND member_fkid IS NULL
	and o.admin_fkid = ?
	{{SEARCH}}
  GROUP BY
  {{GROUPBY}}
	`
	//COALESCE(receipt_receiver, customer_name)

	params := make([]interface{}, 0)
	params = append(params, user.BusinessId)

	//handle query params
	if strings.TrimSpace(c.queryParam.Search) != "" {
		c.queryParam.Search = strings.ToLower(c.queryParam.Search)
		c.queryParam.Search = " AND (LOWER(s.customer_name) LIKE '%" + c.queryParam.Search + "%' OR LOWER(s.receipt_receiver) LIKE '%" + c.queryParam.Search + "%' ) "
	}

	if len(c.queryParam.OutletId) > 0 {
		c.queryParam.Search += " AND o.outlet_id in " + db.WhereIn(len(c.queryParam.OutletId))
		params = append(params, cast.ToInterfaceArray(c.queryParam.OutletId)...)
	}

	if c.queryParam.DateStart > 0 && c.queryParam.DateEnd > 0 {
		c.queryParam.Search += " and s.time_created >=? AND s.time_created <=? "
		params = append(params, c.queryParam.DateStart, c.queryParam.DateEnd)
	}

	//modify query
	order := "customer_name ASC"
	if c.queryParam.Order.Column != "" {
		order = fmt.Sprintf("%s %s", c.queryParam.Order.Column, c.queryParam.Order.Direction)
	}

	if c.queryParam.GroupBy == "transaction" {
		sql = strings.Replace(sql, "{{GROUPBY}}", "s.sales_id", 1)
	} else {
		sql = strings.Replace(sql, "{{GROUPBY}}", "if(receipt_receiver is null or LENGTH(receipt_receiver) = 0, customer_name, receipt_receiver)", 1)
	}

	sql += " ORDER BY " + order

	sql = strings.Replace(sql, "{{SEARCH}}", c.queryParam.Search, 1)

	if c.queryParam.IsCount {
		sql = fmt.Sprintf("select count(*) as cnt from (%v) customers", sql)
	} else {
		sql += c.queryParam.LimitQuery()
	}

	return db.QueryArray(sql, params...)
}

func (c crmRepository) FetchCustomerTransactionList(user domain.User) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmRepository) FetchSalesMember(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	sql := `SELECT FROM_UNIXTIME(ANY_VALUE(s.time_created)/1000+25200, '$dateFormat') as 'date',
	s.member_fkid as member_id, min(s.time_created) as m_time_created
	from sales s
	join outlets o on o.outlet_id=s.outlet_fkid
	join members m on m.member_id=s.member_fkid
	join members_detail md on md.member_fkid
	where s.member_fkid is not null
	and s.status='Success'
	and o.admin_fkid= @adminId 
	and s.time_created BETWEEN @dateStart and @dateEnd
	group by s.member_fkid, FROM_UNIXTIME(s.time_created/1000+25200, '$dateFormat')
	order by m_time_created `

	dateFormat := "%m/%Y"
	if request.Period == "daily" {
		dateFormat = "%d/%m/%Y"
	} else if request.Period == "weekly" {
		dateFormat = "%v/%Y"
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    user.BusinessId,
		"dateStart":  request.DateStart,
		"dateEnd":    request.DateEnd,
		"dateFormat": dateFormat,
	})

	return db.QueryArray(sql, params...)
}

func (c crmRepository) FetchMemberRegisterRange(adminId int, param domain.RequestParam) ([]map[string]interface{}, error) {
	sql := `SELECT m.member_id, 
	FROM_UNIXTIME(md.register_date/1000+25200, '$dateFormat') as 'register_date',
	md.register_date as register_date_millis
	 from members m 
	join members_detail md on m.member_id=md.member_fkid
	where md.admin_fkid = @adminId 
	AND md.register_date BETWEEN @dateStart and @dateEnd `

	dateFormat := "%m/%Y"
	if param.Period == "daily" {
		dateFormat = "%d/%m/%Y"
	} else if param.Period == "weekly" {
		dateFormat = "%v/%Y"
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    adminId,
		"dateStart":  param.DateStart,
		"dateEnd":    param.DateEnd,
		"dateFormat": dateFormat,
	})

	return db.QueryArray(sql, params...)
}
