package http

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/a8m/djson"
	"github.com/buaazp/fasthttprouter"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-report/controller/dashboard/v1"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/exception"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type dashboardHandler struct {
	uc domain.DashboardUseCase
}

func NewHttpDashboardHandler(router *fasthttprouter.Router, useCase domain.DashboardUseCase) {
	handler := &dashboardHandler{useCase}
	//auth.ValidateToken(auth.AccessRole(handler.FetchCashFlow,"dashboard","access", "view_cashflow"))
	router.POST("/v1/dashboard/cash_flow/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchCashFlow, "dashboard", "access", "view_cashflow")))
	router.POST("/v1/dashboard/entertain_income/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchEntertainIncome, "dashboard", "access", "view_entertainincome")))

	//router.POST("/v1/dashboard/sales_transaction/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesTransactionInfo_v1, "dashboard", "access", "view_sales_transaction")))
	router.POST("/v1/dashboard/sales_transaction/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchSalesTransaction, "dashboard", "access", "view_sales_transaction")))

	//per category
	router.POST("/v1/dashboard/sales_category/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchItemSalesGroup, "dashboard", "access", "view_sales_categories")))

	//media
	router.POST("/v1/dashboard/by_media", auth.ValidateToken(handler.FetchSalesByMedia))

	//router.POST("/v1/dashboard/sales_analysis/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesAnalysis_v1, "dashboard", "access", "view_sales_analysis")))
	router.POST("/v2/dashboard/sales_analysis/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchSalesAnalysis, "dashboard", "access", "view_sales_analysis")))

	//dashboard
	//router.POST("/v1/dashboard/sales_top/:limit/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesTop_v1, "dashboard", "access", "view_sales_top")))
	router.POST("/v2/dashboard/sales_top/:limit/by_sales", auth.ValidateToken(auth.AccessRole(handler.FetchTopSales, "dashboard", "access", "view_sales_top")))
}

func (h *dashboardHandler) FetchCashFlow(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost v1.ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := v1.MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
		return
	}

	//proses
	dateOption := cast.ToString(ctx.UserValue("dateOption"))
	if strings.TrimSpace(dateOption) == "" {
		dateOption = "by_sales"
	}
	if dateOption != "by_sales" && dateOption != "by_shift" {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Message: "by_sales or by_shift",
			Status:  false,
			Code:    400,
			Data:    err,
		})
		return
	}

	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := string(ctx.Request.Header.Peek("outlet_access"))
	//get post value
	timeStart := cast.ToInt64(myPost.TimeStart)
	timeEnd := cast.ToInt64(myPost.TimeEnd)
	timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])
	if outletIds == nil {
		oa := strings.Split(outletAccess, ",")
		names := make([]interface{}, len(oa))
		for i, v := range oa {
			names[i] = v
		}
		outletIds = names
	}

	//check min.date data yang bisa diambil
	role := string(ctx.Request.Header.Peek("role"))
	timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

	//adminId, timeStart, timeEnd, outletIds
	data, err1 := h.uc.FetchCashFlow(adminId, timeStart, timeEnd, outletIds, dateOption)

	//output
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   data,
	})
}

func (h *dashboardHandler) FetchEntertainIncome(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost v1.ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := v1.MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
		return
	}

	//proses
	dateOption := cast.ToString(ctx.UserValue("dateOption"))
	if strings.TrimSpace(dateOption) == "" {
		dateOption = "by_sales"
	}
	if dateOption != "by_sales" && dateOption != "by_shift" {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Message: "by_sales or by_shift",
			Status:  false,
			Code:    400,
			Data:    err,
		})
		return
	}

	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

	//get post value
	timeStart := cast.ToInt64(myPost.TimeStart)
	timeEnd := cast.ToInt64(myPost.TimeEnd)
	timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])
	if outletIds == nil {
		oa := strings.Split(outletAccess, ",")
		names := make([]interface{}, len(oa))
		for i, v := range oa {
			names[i] = v
		}
		outletIds = names
	}

	//check min.date data yang bisa diambil
	role := string(ctx.Request.Header.Peek("role"))
	timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

	data, err1 := h.uc.FetchEntertainIncome(adminId, timeStart, timeEnd, outletIds, dateOption)

	//output
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   data,
	})
}

func (h *dashboardHandler) FetchSalesTransaction(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	log.IfError(err1)

	//validation struct
	var myPost v1.ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := v1.MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
		return
	}

	dateOption := cast.ToString(ctx.UserValue("dateOption"))
	if strings.TrimSpace(dateOption) == "" {
		dateOption = "by_sales"
	}
	if dateOption != "by_sales" && dateOption != "by_shift" {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Message: "by_sales or by_shift",
			Status:  false,
			Code:    400,
			Data:    err,
		})
		return
	}

	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

	//get post value
	timeStart := cast.ToInt64(myPost.TimeStart)
	timeEnd := cast.ToInt64(myPost.TimeEnd)
	timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])
	if outletIds == nil {
		oa := strings.Split(outletAccess, ",")
		names := make([]interface{}, len(oa))
		for i, v := range oa {
			names[i] = v
		}
		outletIds = names
	}

	//check min.date data yang bisa diambil
	role := string(ctx.Request.Header.Peek("role"))
	timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

	//proses pengambilan data
	timeStartBenchmark := time.Now()
	fmt.Println(adminId, timeStart, timeEnd, outletIds)
	data, err1 := h.uc.FetchSalesTransaction(adminId, timeStart, timeEnd, outletIds, dateOption)
	fmt.Printf("BENCHMARK sales_transaction_info : %v\n", time.Since(timeStartBenchmark))

	//output
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   data,
	})

}

func (h *dashboardHandler) FetchItemSalesGroup(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost v1.ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := v1.MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
		return
	}

	adminId := string(ctx.Request.Header.Peek("admin_id"))
	outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

	//get post value
	timeStart := cast.ToInt64(myPost.TimeStart)
	timeEnd := cast.ToInt64(myPost.TimeEnd)
	timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])
	if outletIds == nil {
		oa := strings.Split(outletAccess, ",")
		names := make([]interface{}, len(oa))
		for i, v := range oa {
			names[i] = v
		}
		outletIds = names
	}

	//check min.date data yang bisa diambil
	role := string(ctx.Request.Header.Peek("role"))
	timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

	//proses pengambilan data
	response, _ := h.uc.FetchItemSalesGroup(adminId, timeStart, timeEnd, outletIds, "sales")
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   response,
	})
}

func (h *dashboardHandler) FetchSalesByMedia(ctx *fasthttp.RequestCtx) {
	param, err := parser.RequestParamDashboard(ctx)
	if err != nil {
		var errData interface{}
		if errValidation, ok := err.(*exception.ValidationError); ok {
			errData = errValidation
		}
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   errData,
		})
		return
	}

	user := domain.GetUserSessionOfFastHttp(ctx)
	response, err := h.uc.FetchSalesByMedia(param, user)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   response,
	})
}

func (h *dashboardHandler) FetchSalesAnalysis(ctx *fasthttp.RequestCtx) {
	param, err := parser.RequestParamDashboard(ctx)
	if err != nil {
		var errData interface{}
		if errValidation, ok := err.(*exception.ValidationError); ok {
			errData = errValidation
		}
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   errData,
		})
		return
	}

	user := domain.GetUserSessionOfFastHttp(ctx)
	response, err := h.uc.FetchSalesAnalysis(param, user)
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   response,
	})
}

func (h *dashboardHandler) FetchTopSales(ctx *fasthttp.RequestCtx) {
	limit := ctx.UserValue("limit")
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost v1.ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := v1.MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		log.Info("bad request: %v", err)
		return
	}

	request := domain.DashboardRequest{
		TimeStart: cast.ToInt64(myPost.TimeStart),
		TimeEnd:   cast.ToInt64(myPost.TimeEnd),
		TimeDiff:  cast.ToInt64(myPost.TimeDiff),
		OutletIds: cast.ToStringSlice(post["outlet_ids"]),
	}

	log.Info("req: %v", request)
	user := domain.GetUserSessionOfFastHttp(ctx)
	result, errResult := h.uc.FetchTopSales(request, cast.ToInt(limit), user)
	if errResult != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}
	_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
		Status: true,
		Code:   200,
		Data:   result,
	})
}
