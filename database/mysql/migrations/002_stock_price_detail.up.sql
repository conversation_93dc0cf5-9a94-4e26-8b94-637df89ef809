CREATE TABLE `stock_price_detail` (
  `stock_price_detail_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `stock_price_fkid` bigint(20) NOT NULL,
  `stock_out` float NOT NULL,
  `stock_out_id` varchar(25) NOT NULL,
  `stock_out_source` varchar(50) NOT NULL,
  `created_at` bigint(20) NOT NULL,
  `meta_data` json DEFAULT NULL,
  PRIMARY KEY (`stock_price_detail_id`),
  KEY `stock_price_fkid` (`stock_price_fkid`),
  CONSTRAINT `stock_price_detail_ibfk_1` FOREIGN KEY (`stock_price_fkid`) REFERENCES `stock_price` (`stock_price_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=latin1;