package take

func Greater[T int | int64 | float32 | float64](numbs ...T) T {
	if len(numbs) == 0 {
		panic("Greater called with an empty slice")
	}

	greatest := numbs[0]
	for _, n := range numbs[1:] {
		if n > greatest {
			greatest = n
		}
	}
	return greatest
}

func Smaller[T int | int64 | float32 | float64](numbs ...T) T {
	if len(numbs) == 0 {
		panic("Smaller called with an empty slice")
	}

	smallest := numbs[0]
	for _, n := range numbs[1:] {
		if n < smallest {
			smallest = n
		}
	}
	return smallest
}
