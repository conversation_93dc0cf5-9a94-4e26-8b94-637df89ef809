package mysql

import (
	"context"
	"database/sql"
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/constant"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type salesRepository struct {
	mysql.Repository
	cache domain.CacheInterface
}

func NewMysqlSalesRepository(db *sql.DB, cache domain.CacheInterface) domain.SalesRepositoryPrimary {
	return &salesRepository{mysql.Repository{Conn: db, CacheDb: cache}, cache}
}

func (s *salesRepository) FetchSalesHistoryTransaction(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `
SELECT s.sales_id, any_value(s.display_nota) AS nomor_nota, any_value(s.display_nota) AS display_nota, any_value(s.time_created) AS tanggal, any_value(s.status) AS status, 
any_value(s.qty_customers) AS pax, any_value(sp.promotion_fkid) as promotion_fkid, sales_tag_fkid,
(SELECT GROUP_CONCAT(DISTINCT promotion_fkid) from sales_detail_promotion where sales_fkid = s.sales_id) as promotion_fkids,
(select GROUP_CONCAT(DISTINCT voucher_code) from [DB].sales_promotion where sales_fkid = s.sales_id) as voucher_codes, 
SUM(s.discount) AS discountSales, SUM(sd.discount) AS discountSd, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'discount' AND sales_fkid = s.sales_id), 0) AS discountTax, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'service' AND sales_fkid = s.sales_id), 0) AS service, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'tax' and sales_fkid = s.sales_id), 0) AS tax, IFNULL((SELECT SUM(sales_tax.total)
        FROM [DB].sales_tax where category = 'voucher' AND sales_fkid = s.sales_id), 0) AS voucherTax,
         any_value(s.grand_total) AS grand_total, any_value(s.voucher) AS voucherSales, any_value(s.voucher_info) AS keterangan_voucherS,any_value( s.discount_info) AS keterangan_discountS, MIN(sd.discount_info) AS keterangan_discountSd, sum(sd.sub_total) AS sub_total, MIN(o.name) AS outlet_name, any_value(s.time_created) AS time_created, (
            SELECT
                sum(sub_total)
            from
                [DB].sales_void sv
            where
                sv.sales_fkid = s.sales_id) as sub_void, sum((select sum(total) FROM [DB].sales_detail_discount where sales_detail_fkid = sd.sales_detail_id and type = 'discount')) as discount_sales, (ifnull((select SUM(promotion_value) FROM [DB].sales_promotion where sales_fkid = s.sales_id), 0)+ifnull((select SUM(promotion_value) FROM [DB].sales_detail_promotion where sales_fkid=s.sales_id and sales_detail_fkid is not null), 0)) as promo,
         os.shift_fkid, s.employee_fkid
FROM [DB].sales s
LEFT JOIN [DB].outlets o ON s.outlet_fkid = o.outlet_id
LEFT JOIN [DB].open_shift os ON s.open_shift_fkid = os.open_shift_id
LEFT JOIN [DB].sales_detail sd ON sd.sales_fkid = s.sales_id
LEFT JOIN [DB].sales_promotion sp on sp.sales_fkid=s.sales_id
WHERE o.admin_fkid = @adminId
AND s.data_status = 'on'
{{WHERE}}
GROUP BY s.sales_id
ORDER BY s.time_created `

	maxLimit := 150
	whereQuery := ""
	limitQuery := fmt.Sprintf(" LIMIT %v OFFSET %v ", maxLimit, maxLimit*request.Page)

	if len(request.Outlet) > 0 {
		whereQuery += " and s.outlet_fkid in @outletId "
	}
	if request.OffsetId > 0 {
		whereQuery += " and s.time_created > @offsetId "
		limitQuery = fmt.Sprintf(" LIMIT %v ", maxLimit)
	}
	if (request.StartDate > 0 || request.EndDate > 0) && request.EndDate >= request.StartDate {
		whereQuery += " and s.time_created BETWEEN @startDate AND @endDate "
	}
	if request.PromotionId > 0 {
		whereQuery += " AND (sp.promotion_fkid=@promotionId  OR s.sales_id in (select sales_fkid from sales_detail_promotion where promotion_fkid=@promotionId )) "
	}
	if strings.ToLower(request.DataStatus) == "all" {
		request.DataStatus = ""
	}
	if request.DataStatus != "" {
		whereQuery += " AND s.status = @status "
	}

	//by customer
	if request.Contact != "" {
		whereQuery += " AND s.receipt_receiver = @contact "
	} else if request.Customer != "" {
		whereQuery += " AND s.customer_name = @customerName "
	}

	// if whereQuery != "" {
	// 	whereQuery = " AND " + whereQuery
	// }

	sql = strings.ReplaceAll(sql, "[DB]", s.DbName())
	sql = strings.Replace(sql, "{{WHERE}}", whereQuery, 1)
	sql += limitQuery

	sql, params := db.MapParam(sql, map[string]any{
		"startDate":    request.StartDate,
		"endDate":      request.EndDate,
		"adminId":      cast.ToInt64(user.BusinessId),
		"outletId":     request.Outlet,
		"offsetId":     request.OffsetId,
		"promotionId":  request.PromotionId,
		"contact":      request.Contact,
		"customerName": request.Customer,
		"status":       request.DataStatus,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSummarySales(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `SELECT
	SUM(discount) AS discount,
	SUM(voucher) AS voucher,
	SUM(qty_customers) AS pax,
	COUNT(*) AS total_bill,
	SUM(grand_total) AS grand_total,
	status
  FROM
	[db].sales s
  JOIN
    [db].outlets o  
  ON s.outlet_fkid=o.outlet_id
  JOIN [db].open_shift os  
  ON os.open_shift_id=s.open_shift_fkid   
  WHERE
    os.time_open BETWEEN @startDate AND @endDate
	AND o.admin_fkid = @adminId 
	[where]
  GROUP BY status`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery += " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", os.Getenv("db_name"), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	// params := make([]any, 0)
	// params = append(params, request.StartDate, request.EndDate, cast.ToInt(user.BusinessId))
	// params = append(params, array.TransformIntToInterface(request.Outlet)...)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSummaryItem(user domain.User, request domain.SalesReportRequest) (map[string]any, error) {
	sql := `SELECT
	SUM(sd.discount - COALESCE((
		SELECT
		  SUM(discount) AS total
		FROM
		  [db].sales_void
		WHERE
		  sales_detail_fkid=sd.sales_detail_id), 0)) AS discount
  FROM
	[db].sales_detail sd
  JOIN
  [db].sales s
  ON
	s.sales_id=sd.sales_fkid
  JOIN
  [db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid   
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open BETWEEN @startDate AND  @endDate
	[where] `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return db.Query(sql, params...)
}

func (s *salesRepository) FetchSummaryPromo(user domain.User, request domain.SalesReportRequest) (map[string]any, error) {
	sql := `SELECT
	SUM(sp.promotion_value) as total
  FROM
	[db].sales_promotion sp
  JOIN
	[db].sales s
  ON
	s.sales_id = sp.sales_fkid
  JOIN
	[db].outlets o
  ON
	o.outlet_id=s.outlet_fkid
  JOIN
	[db].open_shift os
  ON
	os.open_shift_id=s.open_shift_fkid
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId 
	AND os.time_open between @startDate and @endDate 
	[where]`
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})
	return db.Query(sql, params...)
}

func (s *salesRepository) FetchSummaryPromoItem(user domain.User, request domain.SalesReportRequest) (map[string]any, error) {
	sql := `
	SELECT
	SUM(total) AS total
	FROM
	(
		SELECT
			SUM(sdp.promotion_value) as total
		FROM
			[db].sales_detail_promotion sdp
		JOIN
			[db].sales s
		ON
			s.sales_id = sdp.sales_fkid
		JOIN
			[db].outlets o
		ON
			o.outlet_id=s.outlet_fkid
		JOIN
			[db].open_shift os
		ON
			os.open_shift_id=s.open_shift_fkid
		WHERE
			s.status = 'Success'
			AND o.admin_fkid = @adminId 
			AND os.time_open between @startDate and @endDate 
			[where]
		GROUP BY
			sales_fkid
	)
	promo_item `
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})

	return db.Query(sql, params...)
}

func (s *salesRepository) FetchSummaryGratuity(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `SELECT
	SUM(total) AS total,
	category
  FROM
	[db].sales_tax st
  JOIN
	[db].sales s
  ON
	s.sales_id=st.sales_fkid
  JOIN
	[db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid  
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId 
	AND os.time_open between @startDate and @endDate 
	[where]
  GROUP BY
	st.category `
	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})
	// fmt.Println(">>>> ", sql, params)
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSummaryPayment(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `SELECT
	sp.method,
	SUM(sp.total) AS total,
	spb.bank_fkid
  FROM
	[db].sales_payment sp
  JOIN
	[db].sales s
  ON
	s.sales_id=sp.sales_fkid
	JOIN [db].open_shift os ON os.open_shift_id=s.open_shift_fkid  
  JOIN
	[db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
  LEFT JOIN
	[db].sales_payment_bank spb
  ON
	spb.sales_payment_fkid=sp.payment_id
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND os.time_open between @startDate and @endDate 
	[where]
  GROUP BY
	sp.method,
	spb.bank_fkid `

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " AND s.outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereQuery += " AND os.shift_fkid in @shiftIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
	})
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchBank(user domain.User) ([]map[string]any, error) {
	sql := "select * from payment_media_bank where admin_fkid = ?"
	return db.QueryArray(sql, user.BusinessId)
}

func (s *salesRepository) FetchSalesRankingPlain(request domain.SalesRankingRequest, user domain.User) ([]map[string]any, error) {
	sql := `SELECT ANY_VALUE(sd.product_fkid) as product_fkid, ANY_VALUE(pdv.variant_name) variant_name, 
	sum(sd.price) AS price, sum(s.qty_customers) AS pax,		
	sum(sd.sub_total) AS sub_total,
	sum(sd.qty) AS qty,
	GROUP_CONCAT(DISTINCT s.outlet_fkid) outlet_ids,
	GROUP_CONCAT(DISTINCT os.shift_fkid) shift_ids
	from sales_detail sd 
	join sales s on s.sales_id=sd.sales_fkid    
	join open_shift os on os.open_shift_id=s.open_shift_fkid 
	join outlets o on o.outlet_id=s.outlet_fkid
	join products_detail pd on pd.product_detail_id=sd.product_detail_fkid
	left join products_detail_variant pdv on pd.variant_fkid=pdv.variant_id	
	$JOIN
	WHERE s.data_status='on' and s.status='Success'
	 and s.time_created BETWEEN @timeStart AND @timeEnd
	 and o.admin_fkid=@adminId
	$WHERE
	GROUP by COALESCE(pdv.variant_id, sd.product_fkid)
	`

	var sqlWhere strings.Builder
	var sqlJoin strings.Builder
	limit := "LIMIT @limitNumber OFFSET @limitOffset"

	if len(request.Shift) > 0 {
		sqlWhere.WriteString(" and os.shift_fkid in @shiftIds ")
	}
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" and pd.outlet_fkid in @outletIds ")
	}

	//remove limit if filter present, to make filter works
	//filter done programatically in code (usecase)
	if len(request.Category) > 0 || len(request.SubCategory) > 0 {
		request.Limit = 9999
		request.Offset = 0
		limit = ""
	}

	//to avoid caching returning old data, if time end is greater than now, set to now
	if request.EndDate > time.Now().UnixMilli() {
		request.EndDate = time.Now().UnixMilli()
	}

	sql += limit
	sql, params := db.MapParam(sql, map[string]any{
		"timeStart":   request.StartDate,
		"timeEnd":     request.EndDate,
		"shiftIds":    request.Shift,
		"outletIds":   request.Outlet,
		"adminId":     user.BusinessId,
		"JOIN":        sqlJoin.String(),
		"WHERE":       sqlWhere.String(),
		"limitOffset": request.Offset,
		"limitNumber": request.Limit,
	})
	return s.QueryCache("SalesRankingPlain", sql, params...).MapArray()
	// return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesRanking(request domain.SalesRankingRequest, user domain.User) ([]map[string]any, error) {
	sql := `SELECT
	p.product_id AS id,
	sum(sd.price) AS price,
	sum(sd.qty) AS qty,
	p.name AS product_name,
	ps.product_subcategory_id,
	group_concat(DISTINCT p.sku) AS sku,
	sum(sd.sub_total) AS sub_total,
	group_concat(DISTINCT pc.name) AS category,
	group_concat(DISTINCT ps.name) AS sub_category,
	sum(s.qty_customers) AS pax,
	group_concat(pdv.variant_name) AS variant,
	group_concat(s.sales_id) AS sales_id,
	group_concat(sd.product_detail_fkid) AS product_detail_fkid,
	group_concat(DISTINCT REPLACE(o.name, ',', '')) outlet,
	group_concat(DISTINCT REPLACE(sf.name, ',', '')) AS shift,
	group_concat(sd.sales_detail_id) AS sales_detail_id
FROM
	sales_detail sd
	LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
	LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
	LEFT JOIN open_shift os ON s.open_shift_fkid = os.open_shift_id
	LEFT JOIN shift sf ON os.shift_fkid = sf.shift_id
	LEFT JOIN products p ON sd.product_fkid = p.product_id
	LEFT JOIN products_detail pd ON pd.product_detail_id = sd.product_detail_fkid
	LEFT JOIN products_category pc ON p.product_category_fkid = pc.product_category_id
	LEFT JOIN products_subcategory ps ON ps.product_subcategory_id = p.product_subcategory_fkid
	LEFT JOIN products_detail_variant pdv ON pdv.variant_id = pd.variant_fkid
WHERE
	o.admin_fkid = ?
	AND s.status = 'Success'
	AND s.data_status = 'on'
	AND s.time_created BETWEEN ? AND ?`
	params := make([]any, 0)
	params = append(params, user.BusinessId, request.StartDate, request.EndDate)

	if len(request.Shift) > 0 {
		sql += fmt.Sprintf(" AND sf.shift_id in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ","))
		for _, id := range request.Shift {
			params = append(params, id)
		}
	}

	if len(request.Category) > 0 {
		sql += fmt.Sprintf(" AND pc.product_category_id in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Category)), ","))
		for _, id := range request.Category {
			params = append(params, id)
		}
	}

	if len(request.Outlet) > 0 {
		sql += fmt.Sprintf(" AND s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ","))
		for _, id := range request.Outlet {
			params = append(params, id)
		}
	}

	//doing filter from code (usecase)
	// if len(request.SubCategory) > 0 {
	// 	sql += fmt.Sprintf(" AND ps.product_subcategory_id in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.SubCategory)), ","))
	// 	for _, id := range request.SubCategory {
	// 		params = append(params, id)
	// 	}
	// }

	sql += fmt.Sprintf(` GROUP BY p.product_id LIMIT 30 OFFSET %d `, request.Offset)
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchOutletByIds(id ...any) ([]map[string]any, error) {
	sql := "select * from outlets where outlet_id in @ids "

	if array.Contain([]string{"development", "localhost"}, constant.GetEnv()) {
		return s.QueryCacheList("outlet", "outlet_id", id, 60*8, sql, nil).MapArray()
	}

	//first get from cache
	keyPrefix := fmt.Sprintf("%s_outlet", constant.GetEnv())
	keyField := "outlet_id"
	resultCached, err := s.cache.GetMapArray(keyPrefix, id...)
	idsLeft := removeDuplicateIds(resultCached, keyField, id...)
	log.Info("got from cached: %v, allIds: %v, left: %v, err: %v", len(resultCached), len(id), len(idsLeft), err)

	if len(idsLeft) == 0 {
		fmt.Println("----- should return ------")
		return changeMapType(resultCached), err
	}

	sql, params := db.MapParam(sql, map[string]any{
		"ids": idsLeft,
	})
	result, err := db.QueryArray(sql, params...)

	go s.cache.SetMapBatch(keyPrefix, keyField, result, 60*time.Minute*24)
	return append(changeMapType(resultCached), result...), err
}

func (s *salesRepository) FetchShiftByIds(id ...any) ([]map[string]any, error) {
	sql := "select * from shift "
	if len(id) > 0 {
		whereIn := strings.Repeat("?,", len(id))
		whereIn = strings.TrimRight(whereIn, ",")
		sql += "where shift_id in (" + whereIn + ")"
	}

	//TODO: caching

	return db.QueryArray(sql, id...)
}

func (s *salesRepository) FetchProductByIds(id ...any) ([]map[string]any, error) {
	sql := `SELECT
	p.product_id,
	p.name as name,
	COALESCE(p.sku) as sku,
	pc.name AS category_name,
	ps.name AS subcategory_name,
	pc.product_category_id, ps.product_subcategory_id
FROM
	products p
	JOIN products_category pc ON p.product_category_fkid = pc.product_category_id
	JOIN products_subcategory ps ON ps.product_subcategory_id = p.product_subcategory_fkid	
WHERE p.product_id in @ids `

	if constant.GetEnv() == "development" || constant.GetEnv() == "staging" {
		return s.QueryCacheList("product", "product_id", id, 60, sql, nil).MapArray()
	}

	//first get from cache
	keyPrefix := fmt.Sprintf("%s_product", constant.GetEnv())
	keyField := "product_id"
	resultCached, err := s.cache.GetMapArray(keyPrefix, id...)
	log.Info("product - got from cached: %v, allIds: %v", len(resultCached), len(id))
	idsLeft := removeDuplicateIds(resultCached, keyField, id...)
	log.Info("product - should get from mysql: %v", len(idsLeft))

	if len(idsLeft) == 0 {
		return changeMapType(resultCached), err
	}

	sql, params := db.MapParam(sql, map[string]any{
		"ids": idsLeft,
	})
	result, err := db.QueryArray(sql, params...)

	//save to cache
	go s.cache.SetMapBatch(keyPrefix, keyField, result, 60*time.Minute)

	//merged
	mergedResult := append(changeMapType(resultCached), result...)
	return mergedResult, err
}

func (s *salesRepository) FetchProductByDetailIds(ids ...any) ([]map[string]any, error) {
	sql := `SELECT
	concat(p.name, COALESCE(concat(' (', pdv.variant_name, ')'), '')) as name,
	p.name as product_name, pdv.variant_name,
	p.product_id, pd.product_detail_id,
	pc.name AS category_name,
	ps.name AS subcategory_name,
	COALESCE(p.sku, pdv.variant_sku) as sku
FROM
	products_detail pd
	JOIN products p ON p.product_id = pd.product_fkid
	JOIN products_category pc ON p.product_category_fkid = pc.product_category_id
	JOIN products_subcategory ps ON ps.product_subcategory_id = p.product_subcategory_fkid 
	LEFT JOIN products_detail_variant pdv ON pd.variant_fkid = pdv.variant_id
WHERE pd.product_detail_id in @ids `

	return s.QueryCacheList("productDetailByIds", "product_detail_id", ids, 60*4, sql, nil).MapArray()
	// return db.QueryArray(sql, ids...)
}

func (s *salesRepository) FetchSalesAnalysisOutletByNominal(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]any{}, nil
	}

	whereIns := make([]string, 0)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, (" s.outlet_fkid in @outletIds "))
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, (" os.shift_fkid in @shiftIds "))
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)                                                   as product_fkid,
       any_value(s.outlet_fkid)                                                     as outlet_fkid,
       sum(s.grand_total)                                                           as total_grandtotal,
       sum(sd.price * (sd.qty - coalesce(sv.qty, 0)))                               as subtotal,
       sum(sd.discount - coalesce(sv.discount, 0) + coalesce(sdd.total, 0))         as discount,
       sum(if(sdt.category = 'tax' or sdt.category = 'service', sdt.total, 0))      as gratuity_tax,
       sum(if(sdt.category = 'voucher' or sdt.category = 'discount', sdt.total, 0)) as gratuity_discount
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         join outlets o on s.outlet_fkid = o.outlet_id
         left join (
    select sum(total) as total, sales_detail_fkid from sales_detail_discount group by sales_detail_fkid
) sdd on sd.sales_detail_id = sdd.sales_detail_fkid
         left join (
    select sum(total) as total, category, sales_detail_fkid from sales_detail_tax group by sales_detail_fkid, category
) sdt on sd.sales_detail_id = sdt.sales_detail_fkid
         left join (select sum(qty) as qty, sum(discount) as discount, sales_detail_fkid
                    from sales_void
                    group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
$joinQuery
where s.time_created between @startDate and @endDate
  and s.status = 'Success'
  and s.data_status = 'on'
  and o.admin_fkid = @adminId
  $whereQuery
group by sd.product_detail_fkid
order by product_fkid ` + limitQuery

	sql, params := db.MapParam(sql, map[string]any{
		"startDate":  request.StartDate,
		"endDate":    request.EndDate,
		"adminId":    adminId,
		"outletIds":  request.Outlet,
		"shiftIds":   request.Shift,
		"joinQuery":  joinQuery,
		"whereQuery": whereQuery,
	})

	return s.QueryCache("salesAnalysisOutletByNominal", sql, params...).MapArray()
}

func (s *salesRepository) FetchSalesAnalysisOutletByNominalFast(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	// we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]any{}, nil
	}

	whereIns := make([]string, 0)
	joinQuery := ""
	whereQueryOut := "" //where query outside of the sub query

	if len(request.Outlet) > 0 {
		whereQueryOut += " and outlet_fkid in @outletIds "
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, (" os.shift_fkid in @shiftIds "))
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	sql := `SELECT * FROM (select sd.product_detail_fkid,
   any_value(sd.product_fkid)                                                   as product_fkid,
   any_value(s.outlet_fkid)                                                     as outlet_fkid,
   sum(s.grand_total)                                                           as total_grandtotal,
   sum(sd.price * (sd.qty - coalesce(sv.qty, 0)))                               as subtotal,
   sum(sd.discount - coalesce(sv.discount, 0))                                  as discount
from sales s
	 join sales_detail sd on s.sales_id = sd.sales_fkid
	 join outlets o on s.outlet_fkid = o.outlet_id
	 left join (select sum(qty) as qty, sum(discount) as discount, sales_detail_fkid
				from sales_void
				group by sales_detail_fkid) sv
			   on sv.sales_detail_fkid = sd.sales_detail_id
$joinQuery
where s.time_created between @startDate and @endDate
and s.status = 'Success'
and s.data_status = 'on'
and o.admin_fkid = @adminId
$whereQuery
group by sd.product_detail_fkid
$limitQuery) as tmp WHERE 1 $queryOut`

	sql, params := db.MapParam(sql, map[string]any{
		"startDate":  request.StartDate,
		"endDate":    request.EndDate,
		"adminId":    adminId,
		"outletIds":  request.Outlet,
		"shiftIds":   request.Shift,
		"joinQuery":  joinQuery,
		"whereQuery": whereQuery,
		"queryOut":   whereQueryOut,
		"limitQuery": limitQuery,
	})

	return s.QueryCache("salesAnalysisOutletByNominalFast", sql, params...).MapArray()
}

func (s *salesRepository) FetchSalesAnalysisOutletByQty(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]any{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + ` 
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + ` 
group by sd.product_detail_fkid
order by product_fkid ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisByNominal(groupBy string, request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, request.TimeZone, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	dateFormat := ""
	if groupBy == "hour" {
		dateFormat = "%H"
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)                         as product_fkid,
       any_value(s.outlet_fkid)                           as outlet_fkid,
       sum(s.grand_total)                                 as total,
       from_unixtime(s.time_created / 1000 + ?, '` + dateFormat + `') as day
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         join outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + ` 
group by day
order by product_detail_fkid, day ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisByQty(groupBy string, request domain.DataTableRequest, adminId any) ([]map[string]any, error) {
	if request.Offset > 0 {
		return []map[string]any{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, request.TimeZone, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	dateFormat := ""
	if groupBy == "hour" {
		dateFormat = "%H"
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total,
       from_unixtime(s.time_created / 1000 + ?, '` + dateFormat + `') as day
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join outlets o on s.outlet_fkid = o.outlet_id 
         ` + joinQuery + ` 
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisDayByNominal(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	//we are not using pagination here... so return empty
	//if request.Offset > 0 {
	//	return []map[string]any{}, nil
	//}

	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, request.TimeZone, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)                         as product_fkid,
       any_value(s.outlet_fkid)                           as outlet_fkid,
       sum(s.grand_total)                                 as total,
       from_unixtime(s.time_created / 1000 + ?, '%w') as day
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         join outlets o on s.outlet_fkid = o.outlet_id
         ` + joinQuery + `
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + ` 
group by sd.product_detail_fkid, day
order by product_detail_fkid, day ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisDayByQty(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	//we are not using pagination here... so return empty
	if request.Offset > 0 {
		return []map[string]any{}, nil
	}

	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, request.TimeZone, request.StartDate, request.EndDate, adminId)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" s.outlet_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		params = append(params, array.TransformIntToInterface(request.Shift)...)
		joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select sd.product_detail_fkid,
       any_value(sd.product_fkid)        as product_fkid,
       any_value(s.outlet_fkid)          as outlet_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as total,
       from_unixtime(s.time_created / 1000 + ?, '%w') as day
from sales s
         join sales_detail sd on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         join outlets o on s.outlet_fkid = o.outlet_id 
         ` + joinQuery + ` 
where s.time_created between ? and ?
  and s.status = 'Success'
  and o.admin_fkid = ? 
` + whereQuery + `
group by sd.product_detail_fkid, day
order by product_detail_fkid, day ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchTransferOutByQty(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, adminId, request.OutletOrigin, request.StartDate, request.EndDate)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" t.outlet_destination_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		//whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		//params = append(params, array.TransformIntToInterface(request.Shift)...)
		//joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
		fmt.Println(joinQuery)
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select concat(any_value(p.name), coalesce(concat(' (', any_value(pdv.variant_name), ')'), '')) as product_name,
       any_value(pc.name)                                                                      as category,
       any_value(ps.name)                                                                      as subcategory,
       t.outlet_destination_fkid as outlet_fkid,
       tp.product_detail_fkid,
       sum(tp.qty)                                                                             as qty,
       sum(tc.qty_confirm)                                                                     as total
from transfer_products tp
         join transfer_confirm tc on tp.transfer_product_id = tc.transfer_product_fkid
         join transfer t on tp.transfer_fkid = t.transfer_id
         join products_detail pd on tp.product_detail_des_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         join products_category pc on p.product_category_fkid = pc.product_category_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
where p.admin_fkid = ? 
  and t.outlet_origin_fkid = ? 
  and tc.data_created between ? and ?
  ` + whereQuery + ` 
group by tp.product_detail_fkid, t.outlet_destination_fkid
order by tp.product_detail_fkid ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchTransferInByQty(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	whereIns := make([]string, 0)
	params := make([]any, 0)
	params = append(params, adminId, request.OutletOrigin, request.StartDate, request.EndDate)
	joinQuery := ""

	if len(request.Outlet) > 0 {
		whereIns = append(whereIns, fmt.Sprintf(" t.outlet_origin_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Outlet)), ",")))
		params = append(params, array.TransformIntToInterface(request.Outlet)...)
	}
	if len(request.Shift) > 0 {
		//whereIns = append(whereIns, fmt.Sprintf(" os.shift_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(request.Shift)), ",")))
		//params = append(params, array.TransformIntToInterface(request.Shift)...)
		//joinQuery += "  join open_shift os on s.open_shift_fkid = os.open_shift_id "
		fmt.Println(joinQuery)
	}

	limitQuery := ""
	if request.Limit > 0 {
		limitQuery = fmt.Sprintf(" limit %d offset %d", request.Limit, request.Offset)
	}

	whereQuery := ""
	if len(whereIns) > 0 {
		whereQuery = " and " + strings.Join(whereIns, " AND ")
	}

	sql := `select concat(any_value(p.name), coalesce(concat(' (', any_value(pdv.variant_name), ')'), '')) as product_name,
       any_value(pc.name)                                                                      as category,
       any_value(ps.name)                                                                      as subcategory,
       t.outlet_origin_fkid as outlet_fkid,
       tp.product_detail_fkid,
       sum(tp.qty)                                                                             as qty,
       sum(tc.qty_confirm)                                                                     as total
from transfer_products tp
         join transfer_confirm tc on tp.transfer_product_id = tc.transfer_product_fkid
         join transfer t on tp.transfer_fkid = t.transfer_id
         join products_detail pd on tp.product_detail_des_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         join products_category pc on p.product_category_fkid = pc.product_category_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
where p.admin_fkid = ?
  and t.outlet_destination_fkid = ?
  and tc.data_created between ? and ?
  ` + whereQuery + `
group by tp.product_detail_fkid, t.outlet_origin_fkid
order by tp.product_detail_fkid ` + limitQuery

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesByMedia(user domain.User, request domain.SalesReportMediaRequest) ([]map[string]any, error) {
	sql := `SELECT
	sp.method as payment,
	SUM(sp.total) AS total,
	spb.bank_fkid,
	count(distinct s.sales_id) as total_transaction,
	[SELECT_TIME] as created_at,
	max(s.time_created) as max_time_created,
	any_value(o.name) as outlet_name, any_value(s.outlet_fkid) as outlet_fkid,
	any_value(s.display_nota) as display_nota,
	any_value(spb.account_number) as account_number
  FROM
	[db].sales_payment sp
  JOIN
	[db].sales s
  ON
	s.sales_id=sp.sales_fkid
  JOIN
  [db].outlets o
  ON
	s.outlet_fkid=o.outlet_id
	JOIN [db].open_shift os  
	ON os.open_shift_id=s.open_shift_fkid
  LEFT JOIN
  [db].sales_payment_bank spb
  ON
	spb.sales_payment_fkid=sp.payment_id	
  WHERE
	s.status = 'Success'
	AND o.admin_fkid = @adminId
	AND [dateOption] BETWEEN @startDate AND @endDate
	AND s.time_created > @offsetId
	[where] 
  GROUP BY 
	[group]
  ORDER BY created_at, sp.method, spb.bank_fkid`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery += " AND s.outlet_fkid in (@outletIds) "
	}
	if len(request.BankId) > 0 {
		request.PaymentMethod = append(request.PaymentMethod, "card")
		whereQuery += " AND (spb.bank_fkid IS NULL or spb.bank_fkid in (@bankIds)) "
	}
	if len(request.PaymentMethod) > 0 {
		whereQuery += " AND sp.method in (@paymentMethods) "
	}

	groupFields := make([]string, 0)
	if request.GroupBy == "media" && request.GroupKey != "outlet" {
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid")
	} else if request.GroupBy == "transaction" {
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid", "s.sales_id")
	} else {
		groupFields = append(groupFields, "sp.method", "spb.bank_fkid", "FROM_UNIXTIME(s.time_created / 1000 + @timeZone, @dateFormat)")

		if request.GroupKey == "outlet" {
			groupFields = append(groupFields, "s.outlet_fkid")
		}
	}

	selectTime := `FROM_UNIXTIME(ANY_VALUE(s.time_created) / 1000 + @timeZone, @dateFormat)`
	dateFormat := "%d-%m-%Y" //daily
	if request.DateGroup == "monthly" {
		dateFormat = "%m-%Y"
	} else if request.DateGroup == "yearly" {
		dateFormat = "%Y"
	} else if request.DateGroup == "hourly" {
		dateFormat = "%H"
	} else if request.DateGroup == "quarterly" {
		selectTime = `LPAD(quarter(from_unixtime(((ANY_VALUE(s.time_created) / 1000 + @timeZone)))), 2, '0')`
	}

	dateOption := "os.time_open"
	if request.DataType == 1 {
		dateOption = "s.time_created"
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)
	sql = strings.Replace(sql, "[group]", strings.Join(groupFields, ","), 1)
	sql = strings.Replace(sql, "[dateOption]", dateOption, 1)
	sql = strings.Replace(sql, "[SELECT_TIME]", selectTime, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate":      request.StartDate,
		"endDate":        request.EndDate,
		"adminId":        cast.ToInt(user.BusinessId),
		"outletIds":      request.Outlet,
		"timeZone":       request.TimeZone,
		"offsetId":       request.OffsetId,
		"dateFormat":     dateFormat,
		"bankIds":        request.BankId,
		"paymentMethods": request.PaymentMethod,
	})

	// return db.QueryArray(sql, params...)

	result, err := s.QueryCache(fmt.Sprintf("SalesByMedia_%s_%s", request.GroupBy, request.DateGroup), sql, params...).MapArray()
	if !log.IfError(err) {
		return result, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	// result, err := s.QueryCache("", sql, params...).MapArray()
	result, err = db.QueryArrayContext(ctx, sql, params...)
	if err != nil {
		if err == context.DeadlineExceeded {
			fmt.Println("Query timed out")
		} else {
			fmt.Println("Query error:", err)
		}
	}
	return result, err
}

func (s *salesRepository) FetchSalesDetailDiscount(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `select sum((sdd.total)) as total, sdd.type, product_detail_fkid
	from sales_detail_discount sdd 
	join sales_detail sd on sd.sales_detail_id=sdd.sales_detail_fkid
	join sales s on s.sales_id=sd.sales_fkid
	join open_shift os on os.open_shift_id=s.open_shift_fkid
	join outlets o on s.outlet_fkid=o.outlet_id
	where s.status = 'Success'
	and o.admin_fkid = @adminId
	and s.outlet_fkid in @outletIds
	and os.time_open between @startDate and @endDate
	$whereQuery
	group by sdd.type, sd.product_detail_fkid
	order by sd.product_detail_fkid `

	whereQuery := ""
	if len(request.ProductDetailIds) > 0 {
		whereQuery = " and sd.product_detail_fkid in @productDetailIds "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":          user.BusinessId,
		"outletIds":        request.Outlet,
		"startDate":        request.StartDate,
		"endDate":          request.EndDate,
		"productDetailIds": request.ProductDetailIds,
		"whereQuery":       whereQuery,
	})

	return s.QueryCache("salesDetailDiscount", sql, params...).MapArray()
}

func (s *salesRepository) FetchSalesDetailTax(adminId int, request domain.SalesReportRequest) ([]map[string]any, error) {
	whereQuery := ""
	if len(request.ProductDetailIds) > 0 {
		whereQuery = " and sd.product_detail_fkid in @productDetailIds "
	}
	if len(request.Outlet) > 0 {
		whereQuery += " and s.outlet_fkid in @outletIds "
	}

	sql := `select sum(st.total) as total, st.category, product_detail_fkid
	from sales_detail_tax st
	join sales_detail sd on sd.sales_detail_id=st.sales_detail_fkid
	join sales s on s.sales_id=sd.sales_fkid
	join outlets o on s.outlet_fkid=o.outlet_id
	where s.status = 'Success'
	and o.admin_fkid = @adminId
	and s.time_created between @startDate and @endDate
	$whereQuery
	group by st.category, sd.product_detail_fkid
	 `

	sql, params := db.MapParam(sql, map[string]any{
		"adminId":          adminId,
		"outletIds":        request.Outlet,
		"startDate":        request.StartDate,
		"endDate":          request.EndDate,
		"productDetailIds": request.ProductDetailIds,
		"whereQuery":       whereQuery,
	})

	return s.QueryCache("salesDetailTax", sql, params...).MapArray()
}

func (s *salesRepository) FetchDiscountSalesDetail(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	return nil, nil
}

func (s *salesRepository) FetchSalesPromotion(user domain.User, request domain.SalesReportRequest) ([]map[string]any, error) {
	sql := `SELECT
	sd.sales_id,
	sd.sales_detail_id,
	sd.product_detail_fkid,
	sd.sales_employee_fkid,
	COALESCE(sd.employee_fkid, sd.sales_employee_fkid) AS employee_fkid,
	sd.price,
	(sd.qty - COALESCE(sd.sv_qty, 0)) AS qty,
	(sd.discount - COALESCE(sd.sv_discount, 0) + sd.tx_discount + sd.sdd_discount) AS discount,
	sd.tx_tax,
	sd.tx_service,
	(sd.tx_voucher + sd.sdd_voucher) AS voucher,
	(COALESCE(sd.sdp_promo_value, 0) + COALESCE(sd.sdd_promo, 0)) AS total_promo,
	COALESCE(sd.sp_promo_id, sd.sdp_promo_id) AS promo_fkid,
	sd.outlet_fkid,
	sd.time_created
  FROM
	(
	
	SELECT
	  sd.sales_detail_id,
	  sd.product_detail_fkid,
	  sd.discount,
	  s.sales_id,
	  s.outlet_fkid,
	  sd.time_created,
	  s.customer_name,
	  s.employee_fkid AS sales_employee_fkid,
	  (
	  SELECT
		SUM(discount) discount
	  FROM
		sales_void
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) sv_discount,
		 (
	  SELECT
		SUM(qty) qty
	  FROM
		sales_void
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) sv_qty,
	   
	  (SELECT
		SUM(promotion_value) promo_value
	  FROM
		sales_detail_promotion
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) AS sdp_promo_value,
		(SELECT
		GROUP_CONCAT(promotion_fkid) promo_id
	  FROM
		sales_detail_promotion
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) AS sdp_promo_id,
		
	  (
	  SELECT
		SUM(
		IF
		  (type='promotion', total, 0)) promo
	  FROM
		sales_detail_discount
	  WHERE
		type='promotion'
		AND sales_detail_fkid=sd.sales_detail_id) sdd_promo,
		
		(
	  SELECT
		SUM(
		IF
		  (type='discount', total, 0)) discount
	  FROM
		sales_detail_discount
	  WHERE
		type='promotion'
		AND sales_detail_fkid=sd.sales_detail_id) sdd_discount,
		
		 (
	  SELECT
		SUM(
		IF
		  (type='voucher', total, 0)) voucher
	  FROM
		sales_detail_discount
	  WHERE
		type='promotion'
		AND sales_detail_fkid=sd.sales_detail_id) sdd_voucher,
				   
	  (
	  SELECT
		GROUP_CONCAT(COALESCE(voucher_code, '')) voucher_code
	  FROM
		sales_promotion
	  WHERE
		sales_fkid = s.sales_id) sp_voucher_code,
		
		(
	  SELECT
		GROUP_CONCAT(promotion_fkid) promo_id
	  FROM
		sales_promotion
	  WHERE
		sales_fkid = s.sales_id) sp_promo_id,
		
	  (
	  SELECT
		 SUM(
		IF
		  (category = 'tax', total, 0)) tax
	  FROM
		sales_detail_tax
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) tx_tax,
		
		
		 (
	  SELECT     
		SUM(
		IF
		  (category = 'service', total, 0)) service
	  FROM
		sales_detail_tax
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) tx_service,
		
		
		 (
	  SELECT
		SUM(
		IF
		  (category = 'discount', total, 0)) discount
	  FROM
		sales_detail_tax
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) tx_discount,
		
		
		 (
	  SELECT      
		SUM(
		IF
		  (category = 'voucher', total, 0)) voucher
	  FROM
		sales_detail_tax
	  WHERE
		sales_detail_fkid=sd.sales_detail_id) tx_voucher,            
	  sd.employee_fkid,
	  sd.price,
	  sd.qty
	FROM
	  sales_detail sd
	JOIN
	  sales s
	ON
	  s.sales_id=sd.sales_fkid
	JOIN
	  outlets o
	ON
	  s.outlet_fkid=o.outlet_id
	WHERE
	  s.status = 'Success'
	  AND o.admin_fkid = @adminId
	  AND s.time_created BETWEEN @startDate AND @endDate 
	  [where]
	  )sd
  WHERE
	(COALESCE(sd.sdp_promo_value, 0) + COALESCE(sd.sdd_promo, 0)) > 0
  ORDER BY
	sd.sales_detail_id 
	LIMIT 100`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in (@outletIds)"
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.Outlet,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisPromotionByQty(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	sql := `select any_value(o.outlet_id) as outlet_id, GROUP_CONCAT(DISTINCT sp.voucher_code) as voucher_code_list, 
COUNT(DISTINCT s.sales_id) as total, sp.promotion_fkid
 from sales_promotion sp
join sales s on s.sales_id=sp.sales_fkid
join outlets o on o.outlet_id=s.outlet_fkid
where o.admin_fkid=@adminId
and (sp.promotion_buy_fkid is not null or sp.voucher_code is not null)
and s.status='Success'
and s.time_created between @startDate and @endDate
[where]
group by sp.promotion_fkid, s.outlet_fkid 
order by total desc, sp.promotion_fkid `

	//new query
	sql = `SELECT * FROM 
	(SELECT COALESCE(sp.promotion_fkid, sdp.promotion_fkid) as promotion_fkid,s.outlet_fkid as outlet_id,
	GROUP_CONCAT(DISTINCT sp.voucher_code) as voucher_code_list, COUNT(DISTINCT s.sales_id) as total
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	left join sales_promotion sp on sp.sales_fkid =s.sales_id 
	left join sales_detail_promotion sdp on sdp.sales_fkid =s.sales_id 
	where o.admin_fkid=@adminId
	and s.status='Success'
	and s.time_created  BETWEEN @startDate and @endDate
	and (sp.sales_promotion_id is not null or sdp.sales_detail_promotion_id  is not null)
	group by COALESCE(sp.promotion_fkid, sdp.promotion_fkid), s.outlet_fkid
	order by promotion_fkid desc) tbl [WHERE]`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = " WHERE outlet_id in @outletIds "
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[WHERE]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchPromotion(promotionIds ...any) ([]map[string]any, error) {
	sql := "select promotion_id,name from promotions "
	if len(promotionIds) > 0 {
		sql += " where promotion_id in @promotionIds "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"promotionIds": promotionIds,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisPromotionByNominal(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	sql := `select sp.promotion_fkid, s.outlet_fkid as outlet_id, 
group_concat(distinct sp.voucher_code) as voucher_code_list, 
sum(sp.promotion_value) as total
from [db].sales_promotion sp
join [db].sales s on s.sales_id=sp.sales_fkid
join [db].outlets o on o.outlet_id=s.outlet_fkid
where o.admin_fkid=@adminId
and s.status='Success'
and s.time_created between @startDate and @endDate
[where]
group by sp.promotion_fkid, s.outlet_fkid
order by sp.promotion_fkid desc`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in @outletIds"
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesAnalysisPromotionDetailByNominal(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	sql := `SELECT sdp.promotion_fkid as promotion_fkid, s.outlet_fkid as outlet_id,
	sum(sdp.promotion_value) as total
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	join sales_detail_promotion sdp on sdp.sales_fkid =s.sales_id 
	where o.admin_fkid=@adminId
	and s.status='Success'
	and s.time_created  BETWEEN  @startDate and @endDate
	[where]
	group by sdp.promotion_fkid, s.outlet_fkid
	order by promotion_fkid desc`

	whereQuery := ""
	if len(request.Outlet) > 0 {
		whereQuery = "AND s.outlet_fkid in @outletIds"
	}

	sql = strings.Replace(sql, "[db]", s.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	sql, params := db.MapParam(sql, map[string]any{
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"adminId":   cast.ToInt(adminId),
		"outletIds": request.Outlet,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchEmployee(employeeIds ...any) ([]map[string]any, error) {
	sql := "select employee_id,name,email from employee"
	if len(employeeIds) > 0 {
		sql += " where employee_id in @employeeIds "
	}

	sql, params := db.MapParam(sql, map[string]any{
		"employeeIds": employeeIds,
	})

	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSalesPayment(salesIds ...any) ([]map[string]any, error) {
	//adding cache
	keyPrefix := fmt.Sprintf("%s_payment", constant.GetEnv())
	keyField := "sales_fkid"
	resultCached, err := s.cache.GetMapArray(keyPrefix, salesIds...)
	log.Info("got from cached: %v, allIds: %v, err: %v", len(resultCached), len(salesIds), err)

	idsLeft := removeDuplicateIds(resultCached, keyField, salesIds...)
	log.Info("should get from mysql: %v", len(idsLeft))
	if len(idsLeft) == 0 {
		fmt.Println("----- should return ------")
		return changeMapType(resultCached), err
	}

	sql := `SELECT coalesce(pmb.name, sp.method) as method, sp.total, sp.sales_fkid
	FROM sales_payment sp
	LEFT JOIN sales_payment_bank spb ON spb.sales_payment_fkid = sp.payment_id
	LEFT JOIN payment_media_bank pmb ON pmb.bank_id = spb.bank_fkid	
	[[WHERE]]
	ORDER BY sp.sales_fkid ASC `

	whereSql := ""
	if len(idsLeft) > 0 {
		whereSql = " WHERE sp.sales_fkid IN @salesIds "
	}

	sql = strings.Replace(sql, "[[WHERE]]", whereSql, 1)
	sql, params := db.MapParam(sql, map[string]any{
		"salesIds": idsLeft,
	})

	result, err := db.QueryArray(sql, params...)
	go s.cache.SetMapBatch(keyPrefix, keyField, result, 24*time.Hour*7)

	return append(changeMapType(resultCached), result...), err
}

func (s *salesRepository) FetchSalesTagByIds(tagIDs []int) (*[]models.SalesTagEntity, error) {
	var results []models.SalesTagEntity
	if len(tagIDs) == 0 {
		return &results, nil
	}

	// ids := array.TransformToInterface(tagIDs)
	// keyPrefix := fmt.Sprintf("%s_payment", os.Getenv("ENV"))
	// keyField := "sales_tag_id"
	// resultCached, err := s.cache.GetMapArray(keyPrefix, ids...)
	// idsLeft := removeDuplicateIds(resultCached, keyField, ids...)

	// log.Info("got from cached: %v, allIds: %v, err: %v, idsLeft: %v", len(resultCached), len(tagIDs), err, idsLeft)
	// if len(idsLeft) == 0 {
	// 	fmt.Println("----- should return ------")
	// 	resultCachedByte, err := json.Marshal(resultCached)
	// 	log.IfError(err)
	// 	err = json.Unmarshal(resultCachedByte, &results)
	// 	if err == nil && results != nil && len(results) > 0 {
	// 		return &results, err
	// 	}
	// }

	sql := `SELECT sales_tag_id, name, admin_fkid FROM sales_tag WHERE sales_tag_id IN @ids `
	sql, params := db.MapParam(sql, map[string]any{
		"ids": tagIDs,
	})

	err := s.QueryCache("sales-tag-by-ids", sql, params...).Model(&results)
	return &results, err
}

// FetchSalesOfAdmin implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSalesOfAdmin(timeStart int64) ([]int, error) {
	query := `SELECT DISTINCT o.admin_fkid from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.time_created > ?`

	var result []int
	rows, err := s.Conn.Query(query, timeStart)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var adminFkid int
		if err := rows.Scan(&adminFkid); err != nil {
			return nil, err
		}
		result = append(result, adminFkid)
	}

	if err := rows.Err(); err != nil {
		return nil, err
	}

	return result, nil
}

func (s *salesRepository) CountOutletsWithTransactionsSince(adminID int, startDate int64) (int, error) {
	query := `
SELECT count(DISTINCT s.outlet_fkid)
FROM sales s 
JOIN outlets o ON o.outlet_id = s.outlet_fkid
WHERE o.admin_fkid = ?
AND s.time_created > ?
`

	var count int
	err := s.Conn.QueryRow(query, adminID, startDate).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("error querying count of outlets with transactions: %w", err)
	}

	return count, nil
}

// FetchDailySalesSumByAdmin fetches daily sales sum for a given admin ID since a specified start time.
func (s *salesRepository) FetchDailySalesSumByAdmin(adminID int, startTime, endTime int64) ([]map[string]any, error) {
	query := `
SELECT sum(s.grand_total) as total, count(DISTINCT s.sales_id) as total_transaction,
FROM_UNIXTIME(ANY_VALUE(s.time_created)/1000+25200, '%d/%m/%Y') as date
from sales s 
join outlets o on o.outlet_id=s.outlet_fkid
where o.admin_fkid=? and s.time_created BETWEEN ? AND ?
and s.status='Success' and s.data_status='on'
group by date
`
	return db.QueryArray(query, adminID, startTime, endTime)
}

// FetchTotalTransactionsByAdmin fetches the total number of distinct transactions per day for a given admin ID within a specified date range.
func (s *salesRepository) FetchTotalTransactionsByAdmin(adminID int, startTime, endTime int64) ([]map[string]any, error) {
	query := `
SELECT count(DISTINCT s.sales_id) as total, FROM_UNIXTIME(ANY_VALUE(s.time_created)/1000+25200, '%d/%m/%Y') as _date 
from sales s 
join outlets o on o.outlet_id=s.outlet_fkid
where o.admin_fkid=? and s.time_created BETWEEN ? and ?
and s.status='Success' and s.data_status='on'
group by _date
`
	return db.QueryArray(query, adminID, startTime, endTime)
}

func (s *salesRepository) AddScheduledMessage(data ...models.ScheduledMessageEntity) error {
	if len(data) == 0 {
		return nil
	}
	err := db.WithTransaction(func(tx db.Transaction) error {
		for _, row := range data {
			tx.Insert("scheduled_message", row.ToMap())
		}
		return nil
	})
	return err
}

// FetchAdmin implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchAdmin(adminId int) (models.AdminEntity, error) {
	sql := "select * from admin where admin_id = ?"
	var result models.AdminEntity
	err := s.Query(sql, adminId).Model(&result)
	return result, err
}
