### REQUIREMENTS     
**Private key** & **Public key** are required for authentication purpose. To generate them use the following scripts:   

```
openssl genrsa -out config/auth/app.rsa 2048
openssl rsa -in config/auth/app.rsa -pubout > config/auth/app.rsa.pub
```

### TESTING
[Testify](https://github.com/stretchr/testify) is a library used for testing purpose, to generate mocking implementation of interface we're using
[```mockery```](https://vektra.github.io/mockery/latest/). 
Mockery configuration is located at file **.mockery.yaml**.      

**Install Mockery** :    
```
brew install mockery
brew upgrade mockery
```    

     
To create mock implementation, just run:
```
mockery
```
