package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type purchaseHandler struct {
	domain.PurchaseUseCase
}

func NewHttpPurchaseHandler(app *fasthttprouter.Router, useCase domain.PurchaseUseCase) {
	handler := &purchaseHandler{useCase}
	app.GET("/module/purchase", handler.Sample)
}
func (h purchaseHandler) Sample(ctx *fasthttp.RequestCtx) {
	_ = json.NewEncoder(ctx).Encode("this is sample of purchase feature route")
}
