You are a Golang Backend engineer, your job is providing api for report. What I mean by report is report transaction for Point of sales. 
the way you should structure the app is as follow:
- <PERSON><PERSON>: handing request from client. for routing use github.com/buaazp/fasthttprouter
- Usecase: handler will call a function to usecase, which all business logic will be putting here.
- Repository: handle getting data from database. For this, we are using BigQuery and MySQL (v5.6). Our bigquery database is not always sync in realtime, so if the request end date is yesterday we can use bigquery.

What you don't understand from this?
_________________________________________

I'm gonna give you example. This request handle report related with sales.

- <PERSON><PERSON>
```
type salesHandler struct {
	uc domain.SalesUseCase
}

func NewHttpSalesHandler(app *fasthttprouter.Router, useCase domain.SalesUseCase) {
	handler := &salesHandler{useCase}
    app.POST("/v1/sales/summary", auth.ValidateToken(handler.FetchSalesSummary))
}

func (h salesHandler) FetchSalesSummary(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	result, err := h.uc.FetchSalesSummary(user, request)
	if err != nil {
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}
```

interfaces:
```
package domain

type SalesUseCase interface {
  FetchSalesSummary(user User, request SalesReportRequest) (map[string]interface{}, error)
}

type SalesRepository interface{
FetchSummarySales(user User, request SalesReportRequest) ([]map[string]interface{}, error)
}

//replica means using bigquery
type SalesRepositoryReplica interface {
	SalesRepository
	FetchSalesRankingPlain(request SalesRankingRequest, user User) ([]map[string]interface{}, error)
}

//primary: using mysql 5.6
type SalesRepositoryPrimary interface {
	SalesRepository
}
```

Usecase:
```
package usecase 

func (s salesUseCase) FetchSalesSummary(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
.....
}

```

what you don't understand from this?

________________________________________

note:
in replicate database (in this case bigquery), not all tables are synced, only sales and sales-related data are synced. For example, products, outlets, products_detail are not synced (just name a few).

what you don't understand from this?


________________________________________

For querying data from mysql, and convert it to struct, I have my own implementation of function. Here is the snipped of it:
```
package mysql

type Repository struct {
	Conn *sql.DB
	sql  string
	args []interface{}
}

type SqlResult struct {
	Data      interface{}
	Error     error
	SqlQuery  string
	sqlOrigin string
	args      []interface{}
	repo      *Repository
}

func (r Repository) DbName() string {
	return os.Getenv("db_name")
}


func (s *SqlResult) Model(model interface{}) error {
	if s.Error != nil {
		return s.Error
	}

	//return mapstructure.Decode(s.Result[0], model)
	//e, _ := json.Marshal(s.Data)
	//fmt.Println(string(e))

	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return cast.MapToStruct(m, model)
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				//jsonStr, err := json.Marshal(arr)
				//if err != nil {
				//	return err
				//}
				//return json.Unmarshal(jsonStr, model)
				return cast.MapArrayToStruct(arr, model)
			} else {
				return cast.MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("model is not single map")
}

```

what you don't understand from this?

____________________________________

I will give you another example, this is actually ongoing api, 
api to fetch sales by tag.

Repository:
```
type SalesRepository interface {
FetchSalesTagByOutlet(user *User, request *SalesReportOrderTypeRequst) (*[]models.SalesTagByOutlet, error)
//other codes..
}
```

Repository:
```
package mysql

type salesRepository struct {
	mysql.Repository
}

func NewMysqlSalesRepository(db *sql.DB) domain.SalesRepositoryPrimary {
	return &salesRepository{mysql.Repository{Conn: db}}
}

func (s salesRepository) FetchSalesTagByOutlet(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesTagByOutlet, error) {
	sql := `SELECT sum(s.grand_total) as total, s.outlet_fkid, s.sales_tag_fkid, o.name as outlet_name 
	from sales s 
	join outlets o on o.outlet_id=s.outlet_fkid
	where s.status='Success' and s.sales_tag_fkid is not null 
	and o.admin_fkid=@adminId
	and s.time_created BETWEEN @startDate AND @endDate
	group by s.outlet_fkid, s.sales_tag_fkid`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   user.BusinessId,
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
	})

	var result []models.SalesTagByOutlet
	err := s.Query(sql, params...).Model(&request)
	return &result, err
}


```