package domain

import "gitlab.com/uniqdev/backend/api-report/models"

type Task struct{}

type TaskReportRequest struct {
	User       `json:"user,omitempty"`
	StartDate  int64 `json:"start_date,omitempty"`
	EndDate    int64 `json:"end_date,omitempty"`
	TimeZone   int   `json:"time_zone,omitempty"`
	CategoryId int   `json:"category_id,omitempty"`
	WorkflowId int   `json:"workflow_id,omitempty"`
}

type TaskPerformanceResponse struct {
	TmMasterCategoryWorkflowID int     `json:"tm_master_category_workflow_id,omitempty"`
	CategoryName               string  `json:"category_name,omitempty"`
	WorkflowName               string  `json:"workflow_name,omitempty"`
	WorkflowPosition           int     `json:"workflow_position,omitempty"`
	TotalBoards                int     `json:"total_boards,omitempty"`
	BoardIds                   []int   `json:"board_ids,omitempty"`
	AverageDuration            float64 `json:"average_duration,omitempty"`
	MedianDuration             float64 `json:"median_duration,omitempty"`
	StdDevDuration             float64 `json:"std_dev_duration,omitempty"`
	RangeStart                 float64 `json:"range_start,omitempty"`
	RangeEnd                   float64 `json:"range_end,omitempty"`
}

type TaskPerformanceWorkflowResponse struct {
	models.TimeAnalysisBoardResponse
	Duration  float64 `json:"duration,omitempty"`
	TimeStart int64   `json:"time_start,omitempty"`
	TimeEnd   int64   `json:"time_end,omitempty"`
}

type TaskPerformanceWorkflow struct {
	TmMasterCategoryWorkflowID int    `json:"tm_master_category_workflow_id,omitempty"`
	CategoryName               string `json:"category_name,omitempty"`
	WorkflowName               string `json:"workflow_name,omitempty"`
	WorkflowPosition           int    `json:"workflow_position,omitempty"`
}

type TaskPerformanceBoard struct {
	models.TimeAnalysisBoardResponse
}

type WorkflowDataWrapper struct {
	Workflows         []models.BoardWorkflowEntity
	BoardMap          map[int]models.TimeAnalysisBoardResponse //key: board id
	MasterWorkflowMap map[int]models.MasterWorkflowEntity      //key: category id
}

type TaskTimeAnalysisResponse struct {
}

type TaskUseCase interface {
	FetchReportPerformance(param *TaskReportRequest) ([]TaskPerformanceResponse, error)
	FetchReportPerformanceByWorkflow(param *TaskReportRequest) ([]TaskPerformanceWorkflowResponse, error)
	FetchReportTimeAnalysis(param *TaskReportRequest) ([]models.TaskTimeAnalysisResponse, error)
}

type TaskRepository interface {
	FetchTaskWorkflowChanges(param *TaskReportRequest) ([]models.BoardWorkflowEntity, error)
	FetchWorkflow(ids ...int) ([]models.MasterWorkflowEntity, error)

	FetchBoardTimeAnalysis(boardIds ...int) ([]models.TimeAnalysisBoardResponse, error)
	FetchLastWorkflowBoard(boardIds ...int) ([]models.BoardWorkflowEntity, error)
}
