package usecase

import (
	"context"
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h *hppUseCase) recordPurchaseConfirm(purchaseConfirmId int, qtyConfirm float32) []interface{} {
	stockPriceIds := make([]interface{}, 0)

	//sometimes, we got data from pubsub, but purchase not yet inserted to database,
	//so, we use pooling method, to frequently fetch data from db, but with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	//purchaseProduct, err := h.repo.FetchPurchaseProductById(cast.ToInt(subs.Id))
	// purchaseProduct, err := h.repo.FetchPurchaseProductByConfirmId(purchaseConfirmId)
	purchaseProduct, err := h.poolPurchaseProduct(ctx, purchaseConfirmId)
	log.Info("purchaseProduct nil? %v", purchaseProduct == nil)
	if log.IfError(err) {
		return stockPriceIds
	}

	if purchaseProduct.PurchaseProductsID == 0 {
		log.IfError(fmt.Errorf("purchase confirm not found %v", purchaseConfirmId))
		return stockPriceIds
	}

	// productDetailId := cast.ToInt(purchaseProduct["products_fkid"])
	if !h.shouldRecordStockPrice(purchaseProduct.ProductsFkid) {
		log.Info("product id %v should not be recorded, purchaseProduct: %v", purchaseProduct.ProductsFkid, purchaseConfirmId)
		return stockPriceIds
	}

	if env == "development" || env == "staging" {
		log.Info("-- test purchase use logStockIn")
		h.logStockIn(&[]models.StockInProduct{*purchaseProduct.ToStockInProduct(qtyConfirm)}, domain.PurchaseConfirm.String(), cast.ToString(purchaseConfirmId))
		h.updateProductPriceBuy(purchaseProduct.ProductsFkid)
		return stockPriceIds
	}

	lastHpp, err := h.repo.FetchLastHppByProductId(purchaseProduct.ProductsFkid)
	if err != nil {
		return stockPriceIds
	}

	log.Info("purchase products: %s", cast.ToStringJson(purchaseProduct))
	// newPrice := purchaseProduct.PriceStok
	//if not found
	log.Info("last hpp: %v | new price: %v, StockPriceId: %v, StockInId: %v", lastHpp.Price, purchaseProduct.PriceStok, lastHpp.StockPriceId, lastHpp.StockInId)
	if lastHpp.StockOut == 0 && lastHpp.StockInId == utils.ToString(purchaseProduct.PurchaseProductsID) {
		_ = h.repo.UpdateStockPriceQtyIn(lastHpp.StockPriceId, lastHpp.StockIn+qtyConfirm)
		stockPriceIds = append(stockPriceIds, lastHpp.StockPriceId)
	} else if lastHpp.StockOut > 0 && lastHpp.StockIn == 0 {
		qty := qtyConfirm
		qtyIn := qty
		// if qtyIn > lastHpp.StockOut {
		// 	qtyIn = lastHpp.StockOut
		// }

		//price := h.getPriceByDataSource(subs.Id, domain.PurchaseConfirm)
		_ = h.repo.UpdateStockPrice(lastHpp.StockPriceId, map[string]interface{}{
			"stock_in":        qtyIn,
			"stock_in_id":     purchaseConfirmId,
			"price":           purchaseProduct.PriceStok,
			"created_at":      utils.CurrentMillis(),
			"stock_in_source": domain.PurchaseConfirm.String(),
		})
		stockPriceIds = append(stockPriceIds, lastHpp.StockPriceId)

		qty = qty - qtyIn
		if qty > 0 {
			id, err := h.repo.InsertStockPrice(domain.Hpp{
				ProductDetailFkid: purchaseProduct.ProductsFkid,
				StockIn:           qty,
				Price:             purchaseProduct.PriceStok,
				StockInId:         cast.ToString(purchaseConfirmId),
				StockInSource:     domain.PurchaseConfirm.String(),
				UpdatedAt:         utils.CurrentMillis(),
				CreatedAt:         utils.CurrentMillis(),
			})
			if err == nil {
				log.Info("stock price added: %v, in: %v, inId: %v", id, qty, purchaseConfirmId)
			}
			stockPriceIds = append(stockPriceIds, id)
		}

		log.Info("StockOut: %v | qty in: %v", lastHpp.StockOut, qtyIn)
		if lastHpp.StockOut > qtyIn {
			_ = h.repo.UpdateStockPrice(lastHpp.StockPriceId, map[string]interface{}{
				"stock_out": qtyIn,
			})

			stockPriceDetail, _ := h.repo.FetchStockPriceDetail(lastHpp.StockPriceId)

			stockAdjust := make([]models.StockPriceDetailEntity, 0)
			stockNew := make([]map[string]interface{}, 0)
			stockRemove := make([]int64, 0)

			qtyCalculate := float32(0)
			isEnough := false
			for _, detail := range *stockPriceDetail {
				qtyCalculate += detail.StockOut

				if isEnough {
					stockRemove = append(stockRemove, detail.StockPriceDetailID)
					stockNew = append(stockNew, detail.ToMap())
				}

				if qtyCalculate >= qtyIn && !isEnough {
					isEnough = true
					if qtyCalculate > qtyIn {
						newStockOut := qtyIn - (qtyCalculate - detail.StockOut)
						stockAdjust = append(stockAdjust, detail)
						stockAdjust[0].StockOut = newStockOut

						newDetail := array.Copy(detail.ToMap())
						newDetail["stock_out"] = detail.StockOut - newStockOut
						stockNew = append(stockNew, newDetail)
					}
				}
			}

			log.Info("adjust price --> [adjust] %v | [remove] %v | [new] %v", cast.ToStringJson(stockAdjust), cast.ToStringJson(stockRemove), cast.ToStringJson(stockNew))
			id, _ := h.repo.AdjustStockPriceDetail(stockAdjust, stockRemove, stockNew)
			stockPriceIds = append(stockPriceIds, id)
		}
	} else {
		log.Info("adding new stockPrice from purchase %v, lastStockPrice: %v", purchaseConfirmId, utils.SimplyToJson(lastHpp))
		id, err := h.repo.InsertStockPrice(domain.Hpp{
			ProductDetailFkid: purchaseProduct.ProductsFkid,
			StockIn:           qtyConfirm,
			Price:             purchaseProduct.PriceStok,
			StockInId:         cast.ToString(purchaseConfirmId),
			StockInSource:     domain.PurchaseConfirm.String(),
			UpdatedAt:         utils.CurrentMillis(),
			CreatedAt:         utils.CurrentMillis(),
			StockInMetaData: map[string]interface{}{
				"purchase_products_id": purchaseProduct.PurchaseProductsID,
			},
		})
		if err == nil {
			log.Info("stock price added: %v, in: %v, inId: %v", id, qtyConfirm, purchaseConfirmId)
			stockPriceIds = append(stockPriceIds, id)
		}
	}

	h.updateProductPriceBuy(purchaseProduct.ProductsFkid)
	return stockPriceIds
}

func (h *hppUseCase) poolPurchaseProduct(ctx context.Context, purchaseConfirmId int) (*models.PurchaseProductStockIn, error) {
	for i := 0; i < 90; i++ {
		fmt.Printf("pooling purchase product %v - %v attempt", purchaseConfirmId, i+1)
		purchaseProduct, err := h.repo.FetchPurchaseProductByConfirmId(purchaseConfirmId)
		if err != nil {
			return nil, err
		}
		if purchaseProduct != nil && len(*purchaseProduct) > 0 {
			return &(*purchaseProduct)[0], nil
		}
		select {
		case <-ctx.Done():
			return nil, fmt.Errorf("timeout")
		case <-time.After(1 * time.Second):
		}
	}
	return nil, fmt.Errorf("exceed max pool attempts")
}

func (h *hppUseCase) recordPurchaseRetur(returProductId, qty int) []interface{} {
	stockPriceIds := make([]interface{}, 0)
	purchaseRetur, err := h.repo.FetchPurchaseRetur(returProductId)
	if log.IfError(err) {
		return stockPriceIds
	}
	if len(purchaseRetur) == 0 {
		log.IfError(fmt.Errorf("not purchase retur found: %v", returProductId))
		return stockPriceIds
	}

	if cast.ToFloat32(purchaseRetur["qty_retur"]) != float32(qty) {
		log.IfError(fmt.Errorf("[HPP FIFO - recordPurchaseRetur] qty from pubsub & db not the same, %v VS %v", qty, purchaseRetur["qty_retur"]))
		// qty = cast.ToInt(purchaseRetur["qty_retur"])
	}

	//get all purchase confirm by purchase_product_id
	purchaseConfirms, err := h.repo.FetchPurchaseConfirmByPurchaseProduct(cast.ToInt(purchaseRetur["purchase_product_fkid"]))
	if log.IfError(err) {
		return stockPriceIds
	}

	log.Info("purchaseConfirms: %v", len(purchaseConfirms))
	stockPrices := make([]domain.Hpp, 0)
	for _, confirm := range purchaseConfirms {
		//purchase_confrim_id
		hpps, err := h.repo.FetchHppByStockInId(cast.ToString(confirm["purchase_confrim_id"]), domain.PurchaseConfirm.String())
		stockPrices = append(stockPrices, hpps...)
		if log.IfError(err) || len(hpps) == 0 {
			log.Info("WARNING! can not find stock_in with id: %v of purchase", confirm["purchase_confrim_id"])
		}
	}

	fmt.Println("stockPrices: ", len(stockPrices))
	if len(stockPrices) == 0 {
		return stockPriceIds
	}

	qtyRemain := float32(qty)
	for _, hpp := range stockPrices {
		stockIn := qtyRemain
		if hpp.StockIn-stockIn < hpp.StockOut {
			stockIn = hpp.StockIn - hpp.StockOut
		}
		err = h.repo.UpdateStockPrice(hpp.StockPriceId, map[string]interface{}{
			"stock_in": hpp.StockIn - stockIn, // hpps[0].StockIn - float32(subs.Qty),
		})
		log.Info("update stock in: %v, to: %v", hpp.StockPriceId, hpp.StockIn-stockIn)
		// log.Info("add stockOut: %v", stockIn)
		// err = h.repo.InsertStockOutDetail(hpp.StockPriceId, stockIn, cast.ToString(returProductId), domain.PurchaseRetur.String())
		log.IfError(err)
		qtyRemain -= stockIn

		if qtyRemain <= 0 {
			break
		}
	}

	// if qtyRemain > 0 {
	// 	id, _ := h.repo.InsertStockPrice(domain.Hpp{
	// 		ProductDetailFkid: hpps[0].ProductDetailFkid,
	// 		StockIn:           qtyRemain,
	// 		Price:             hpps[0].Price,
	// 		StockInId:         hpps[0].StockInId,
	// 		StockInSource:     domain.PurchaseConfirm.String(),
	// 		UpdatedAt:         time.Now().Unix() * 1000,
	// 		CreatedAt:         time.Now().Unix() * 1000,
	// 	})
	// 	stockPriceIds = append(stockPriceIds, id)
	// }

	h.updateProductPriceBuy(stockPrices[0].ProductDetailFkid)
	return stockPriceIds
}

func (h *hppUseCase) HandlePurchaseRetur(returProductId, qtyRetur int) {
	// - get all total retur
	// purchaseReturs, err := h.repo.FetchPurchaseRetur(returProductId)
	// if err != nil {
	// 	return
	// }

	// totalRetur := 0
	// for _, raw := range purchaseReturs {
	// 	totalRetur += cast.ToInt(raw["qty_retur"])
	// }
	// totalRetur -= qtyRetur

	// // -  get all  purchase confirm
	// purchaseConfirms, err := h.repo.FetchPurchaseConfirmByPurchaseProduct(purchaseProductId)
	// if err != nil {
	// 	return
	// }

	// // - determine which purchase confirm has been used (based on total retur)
	// // this way, we can one or more purchase_confirm_id
	// purchaseConfirmIds := make(map[int]int) //key: purchaseConfirmId, value: qty
	// for _, raw := range purchaseConfirms {
	// 	qtyArrived := cast.ToInt(raw["qty_arive"])
	// 	canGet := qtyArrived > totalRetur

	// 	if canGet {
	// 		qtyUsed := qtyArrived - totalRetur
	// 		if qtyRetur < qtyUsed {
	// 			qtyUsed = qtyRetur
	// 		}
	// 		qtyRetur -= qtyUsed

	// 		purchaseConfirmIds[cast.ToInt(raw["purchase_confrim_id"])] = qtyUsed
	// 		totalRetur = 0
	// 	} else {
	// 		totalRetur -= cast.ToInt(raw["qty_arive"])
	// 	}

	// 	if qtyRetur <= 0 {
	// 		break
	// 	}
	// }

	// log.Info("confirmIds: %v", utils.SimplyToJson(purchaseConfirmIds))
	// for id, qty := range purchaseConfirmIds {
	// 	h.HandleNewStockInOut(domain.HppSubscription{
	// 		Source: domain.PurchaseRetur,
	// 		Id:     cast.ToString(id),
	// 		Qty:    cast.ToInt(qty),
	// 	})
	// }
}

func (h *hppUseCase) FetchPurchaseConfirmId(purchaseProductId int) (int, error) {
	data, err := h.repo.FetchPurchaseConfirmByPurchaseProduct(purchaseProductId)
	if err != nil {
		return 0, err
	}

	if len(data) > 1 || len(data) == 0 {
		err = fmt.Errorf("can not determine purchase confirm id, its already been confirmed multiple times. (id: %v)", purchaseProductId)
		log.IfError(err)
		return 0, err
	}

	return cast.ToInt(data[0]["purchase_confrim_id"]), nil
}

func (h *hppUseCase) HandlePurchaseConfirmUpdate(ids []int) {
	//fetch data from db
	purchaseProduct, err := h.repo.FetchPurchaseProductByConfirmId(ids...)
	log.IfError(err)

	if purchaseProduct == nil {
		purchaseProduct = &[]models.PurchaseProductStockIn{}
	}

	log.Info("purchaseConfirms size: %v, ids: %v", len(*purchaseProduct), ids)

	//adjust fifo
	for _, purchase := range *purchaseProduct {
		log.Info("updatePurchase, %v qtyNew: %v", purchase.PurchaseConfrimId, purchase.QtyArive)
		h.AdjustStockOpname(cast.ToString(purchase.PurchaseConfrimId), domain.PurchaseConfirm.String(), purchase.QtyArive)
		h.UpdateStockPriceQtyIn(cast.ToString(purchase.PurchaseConfrimId), domain.PurchaseConfirm.String(), purchase.QtyArive)
	}

}
