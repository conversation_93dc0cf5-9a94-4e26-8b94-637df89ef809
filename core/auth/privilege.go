package auth

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strings"
)

func AccessRole(next fasthttp.RequestHandler, url, actionViewAddEditDeleteAccess string, accessDetail ...string) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		//initial
		headerKey := "role"
		action := strings.ToLower(actionViewAddEditDeleteAccess)
		accessValid := false

		//jwt data
		userType := string(ctx.Request.Header.Peek("user_type"))
		userId := string(ctx.Request.Header.Peek("user_id"))

		if userType=="admin" {
			accessValid = true
		}else{
			dataRole, err := db.Query(`SELECT * FROM employee_role WHERE url=? AND employee_fkid=?`, url,userId)
			if err != nil {
				utils.CheckErr(err)
			}
			if len(dataRole) > 0 {
				roleView := false
				if utils.ToString(dataRole["role_view"])=="1" {
					roleView = true
					if action=="view" {
						accessValid = true
					}
				}
				roleAdd := false
				if utils.ToString(dataRole["role_add"])=="1" {
					roleAdd=true
					if action=="add" {
						accessValid = true
					}
				}
				roleEdit := false
				if utils.ToString(dataRole["role_edit"])=="1" {
					roleEdit=true
					if action=="edit" {
						accessValid = true
					}
				}
				roleDelete := false
				if utils.ToString(dataRole["role_delete"])=="1" {
					roleDelete=true
					if action=="delete" {
						accessValid = true
					}
				}



				//decode access_detail
				var roleAccess map[string]interface{}
				_ = json.Unmarshal([]byte(utils.ToString(dataRole["role_access"])), &roleAccess) //decode json to map
				if accessDetail!=nil {
					access := utils.ToString(accessDetail[0])
					if roleAccess[access] != nil {
						if roleAccess[access] == true || utils.ToString(roleAccess[access]) != "" {
							fmt.Println("ada akses ke: ",access)
							accessValid = true
						}
					}

				}

				role := utils.SimplyToJson(map[string]interface{}{
					"view": roleView,
					"add": roleAdd,
					"edit": roleEdit,
					"delete": roleDelete,
					"access": roleAccess,
				})
				ctx.Request.Header.Set(headerKey, role)
			}
		}

		//access status
		if accessValid {
			//fmt.Println(string(ctx.Request.Header.Peek(headerKey)))
			next(ctx)
		}else{
			ctx.SetStatusCode(fasthttp.StatusForbidden)
		}
	})
}