package log

type logger struct {
	Config LoggerConfig
	hook   hook
}

type LoggerConfig struct {
	DisableColor bool
	HideTime     bool
	TimeOffset   int64
}

type logOutput struct {
	Message string
	Stacks  string
	Level   string
	Err     error
}

type hook interface {
	initHook()
	send(logOutput) error
}

var (
	sdt = New()
	//prefixError = "\u001B[1;31m[ERROR]\u001B[0m"
)

func New() *logger {
	return &logger{}
}

//func SetLogger(config LoggerConfig) {
//	sdt.setLogConfig(config)
//}

func AddHook(h hook) {
	sdt.hook = h
	sdt.hook.initHook()
}

func (l *logger) setLogConfig(config LoggerConfig) {
	l.Config = config
}
