package usecase

import (
	"encoding/json"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
	"gitlab.com/uniqdev/backend/api-report/module/crm/mocks"
)

func Test_crmUseCase_FetchCohortAnalysis(t *testing.T) {
	salesJson := `[{"date":"01/2023","member_id":"3"},{"date":"01/2023","member_id":"4"},{"date":"01/2023","member_id":"5"},{"date":"01/2023","member_id":"6"},{"date":"01/2023","member_id":"7"},{"date":"01/2023","member_id":"8"},{"date":"01/2023","member_id":"9"},{"date":"01/2023","member_id":"10"},{"date":"01/2023","member_id":"11"},{"date":"01/2023","member_id":"12"},{"date":"02/2023","member_id":"4"},{"date":"02/2023","member_id":"6"},{"date":"02/2023","member_id":"8"},{"date":"02/2023","member_id":"10"},{"date":"02/2023","member_id":"12"},{"date":"02/2023","member_id":"13"},{"date":"02/2023","member_id":"14"},{"date":"03/2023","member_id":"3"},{"date":"03/2023","member_id":"5"},{"date":"03/2023","member_id":"13"},{"date":"03/2023","member_id":"15"},{"date":"03/2023","member_id":"16"},{"date":"03/2023","member_id":"17"}]`
	memberJson := `[{"member_id":"5","register_date":"01/2023"},{"member_id":"6","register_date":"01/2023"},{"member_id":"7","register_date":"01/2023"},{"member_id":"8","register_date":"01/2023"},{"member_id":"9","register_date":"01/2023"},{"member_id":"10","register_date":"01/2023"},{"member_id":"11","register_date":"01/2023"},{"member_id":"12","register_date":"01/2023"},{"member_id":"13","register_date":"02/2023"},{"member_id":"14","register_date":"02/2023"},{"member_id":"15","register_date":"03/2023"},{"member_id":"16","register_date":"03/2023"},{"member_id":"17","register_date":"03/2023"},{"member_id":"16","register_date":"03/2023"},{"member_id":"17","register_date":"03/2023"}]`
	resultJson := `[{"date":"01/2023","period_0":8,"period_1":4,"period_2":1,"total_user":8},{"date":"02/2023","period_0":2,"period_1":1,"total_user":2},{"date":"03/2023","period_0":5,"total_user":5}]`

	var resultMap []map[string]interface{}
	err := json.Unmarshal([]byte(resultJson), &resultMap)
	assert.Nil(t, err)

	resultTest := make([]map[string]interface{}, 3)
	resultTest[0] = resultMap[0]
	resultTest[1] = resultMap[2]
	resultTest[2] = resultMap[1]

	var salesMap []map[string]interface{}
	err = json.Unmarshal([]byte(salesJson), &salesMap)
	assert.Nil(t, err)

	var memberMap []map[string]interface{}
	err = json.Unmarshal([]byte(memberJson), &memberMap)
	assert.Nil(t, err)

	repo := mocks.NewRepository(t)
	repo.On("FetchSalesMember", mock.Anything, mock.Anything).Return(salesMap, nil)
	repo.On("FetchMemberRegisterRange", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(memberMap, nil)

	reqParam := domain.RequestParam{
		RequestParamFilter: domain.RequestParamFilter{
			DateStart: 1672506000000,
			DateEnd:   1703869200000,
			Period:    "monthly",
		},
	}

	type fields struct {
		repoPrimary crm.Repository
		repoReplica crm.Repository
	}
	type args struct {
		user    domain.User
		request domain.RequestParam
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []map[string]interface{}
		wantErr bool
	}{
		{"test1", fields{repoPrimary: repo, repoReplica: repo}, args{request: reqParam}, resultMap, false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := crmUseCase{
				repoPrimary: tt.fields.repoPrimary,
				repoReplica: tt.fields.repoReplica,
			}
			got, err := c.FetchCohortAnalysis(tt.args.user, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("crmUseCase.FetchCohortAnalysis() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(resultTest, tt.want) {
				t.Errorf("crmUseCase.FetchCohortAnalysis() = %v, want %v", utils.SimplyToJson(got), utils.SimplyToJson(tt.want))
			}
		})
	}
}

func Test_generatePeriods(t *testing.T) {
	monthly := []string{"09/2023", "10/2023", "11/2023", "12/2023", "01/2024", "02/2024"}
	daily := []string{"28/12/2023", "29/12/2023", "30/12/2023", "31/12/2023", "01/01/2024", "02/01/2024"}
	weekly := []string{"01", "02", "03", "04", "05", "06", "07"}

	type args struct {
		startDate  int64
		endDate    int64
		timeOffset int64
		period     string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{"monthly", args{startDate: 1693501200000, endDate: 1707930000000, timeOffset: 0, period: "monthly"}, monthly},
		{"daily", args{startDate: 1703696400000, endDate: 1704182400000, timeOffset: 0, period: "daily"}, daily},
		{"weekly", args{startDate: 1672592400000, endDate: 1676221200000, timeOffset: 0, period: "weekly"}, weekly},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generatePeriods(tt.args.startDate, tt.args.endDate, tt.args.timeOffset, tt.args.period); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("generatePeriods() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_weekToDateRange(t *testing.T) {
	type args struct {
		startDate  int64
		endDate    int64
		timeOffset int64
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		{"test1", args{startDate: 1672592400000, endDate: 1674147600000}, map[string]string{"01/2023": "02/01/2023 - 07/01/2023", "02/2023": "08/01/2023 - 14/01/2023"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := weekToDateRange(tt.args.startDate, tt.args.endDate, tt.args.timeOffset); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("weekToDateRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
