package parser

import (
	"encoding/json"
	"testing"
)

func TestCalculatePeriods(t *testing.T) {
	// Example usage with time offset of 7 hours
	timeOffset := int64(7 * 60 * 60 * 1000) // 7 hours in milliseconds
	// Example start and end times (representing 1/06/2024 and 30/06/2024 in milliseconds)
	timeStart := int64(1720603087602)       //Wed Jul 10 2024 16:18:07
	timeEnd := timeStart + 29*24*60*60*1000 // Add 29 days

	type args struct {
		timeOffset int64
		timeStart  int64
		timeEnd    int64
	}
	tests := []struct {
		name string
		args args
		want TimePeriod
	}{
		{"test1", args{timeOffset, timeStart, timeEnd}, TimePeriod{}},
		{"one-day", args{timeOffset, 1720630800000, 1720717199999}, TimePeriod{}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := CalculatePeriods(tt.args.timeOffset, tt.args.timeStart, tt.args.timeEnd)
			if got != tt.want {
				gotJson, _ := json.Marshal(got)
				t.<PERSON>("CalculatePeriods() got = %s, want %v", gotJson, tt.want)
			}
		})
	}
}
