package utils

import (
	"encoding/json"
	"fmt"
	"time"

	"gitlab.com/uniqdev/backend/api-report/models"
)

type Privilege struct {
	ExampleParam string
	Role string
	TimeDiff int64
}

func (p Privilege) DashboardDateMin(timestartInMillis int64) int64 {
	timeStart := timestartInMillis

	var access map[string]interface{}
	json.Unmarshal([]byte(p.Role), &access)

	var dashboardAccess models.PrivilegeDashboard
	json.Unmarshal([]byte(SimplyToJson(access["access"])), &dashboardAccess)
	if dashboardAccess.DateMin!="" {
		//disini kodingan untuk cek minimal...
		dateStartString,_ := MillisToDateTime(timeStart+p.TimeDiff)
		dateStart,_ := time.Parse("2006-01-02 15:04:05", dateStartString)
		dateStartMin,_ := time.Parse("2006-01-02", dashboardAccess.DateMin)
		if dateStartMin.After(dateStart) {
			//replace ambil data dari start min di DB
			timeStart = dateStartMin.Unix()*1000 - p.TimeDiff
		}
	}
	return timeStart
}



type Report struct {
	Token string
}

func (r Report) RequestToken() string {
	return "xxxx"+r.Token
}

func (Report) Tokenizer(token string) string {
	return token
}

func FilterAccessibleOutlets(outletRequests []string, outletAccess []int) []string {
	// Use a map for efficient lookup of accessible outlet IDs
	accessibleOutlets := make(map[string]bool)
	for _, outletID := range outletAccess {
		accessibleOutlets[fmt.Sprint(outletID)] = true
	}

	var filteredOutlets []string
	for _, requestID := range outletRequests {
		// Check if the requested outlet ID is in the accessible map
		if _, exists := accessibleOutlets[requestID]; exists {
			filteredOutlets = append(filteredOutlets, requestID)
		}
	}

	return filteredOutlets
}