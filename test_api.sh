#!/bin/bash

# Test script for the new sales report by product API
# Make sure your API server is running before executing this script

BASE_URL="http://localhost:8080"
ENDPOINT="/v1/sales/report-by-product"

echo "Testing Sales Report by Product API"
echo "===================================="

# Test 1: Basic request with all parameters
echo "Test 1: Basic request with all parameters"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&category=27,283&outlet=29,41&dataType=1&shift=12,10" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 2: Request with dataType=0 (by shift)
echo "Test 2: Request with dataType=0 (by shift)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&outlet=29,41&dataType=0&shift=12,10" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 3: Request without filters (only date and admin)
echo "Test 3: Request without filters (only date and admin)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 4: Request with invalid parameters (should handle gracefully)
echo "Test 4: Request with invalid parameters"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=invalid&endDate=invalid&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\nTesting completed!"
