package google

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"cloud.google.com/go/bigquery"
	"google.golang.org/api/option"
)

func InitBigQueryClient() *bigquery.Client {
	ctx := context.Background()
	credentialPath := os.Getenv("BIGQUERY_CREDENTIAL_PATH")
	projectId := ""

	credJson, err := os.ReadFile(credentialPath)
	if err == nil {
		var serviceAccount ServiceAccount
		err = json.Unmarshal(credJson, &serviceAccount)
		if err == nil {
			projectId = serviceAccount.ProjectID
		}
	} else {
		fmt.Println("read credential path err: ", err)
	}

	opt := option.WithCredentialsFile(credentialPath)
	client, err := bigquery.NewClient(ctx, projectId, opt)
	if err != nil {
		fmt.Println("init bigquery client err: ", err)
	}
	return client
}
