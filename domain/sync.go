package domain

type Sync struct{}
type SyncUseCase interface {
	SyncDataToBigQuery()
	UpdateMaxTimeSync()
}
type SyncRepository interface {
	FetchLastSyncSalesFromBigQuery() (int64, error)
	FetchSalesAfter(lastSync int64) ([]map[string]interface{}, error)
	FetchLastSyncSalesDetailFromBigQuery() (int64, error)
	FetchSalesDetailAfter(lastSync int64) ([]map[string]interface{}, error)
	FetchLastSyncOpenShiftFromBigQuery() (int64, error)
	FetchOpenShiftAfter(lastSync int64) ([]map[string]interface{}, error)
	FetchLastSalesDetailIdFromBigQuery() (int64, error)
	FetchSalesDetailAfterId(lastId int64) ([]map[string]interface{}, error)
	FetchLastVoidIdFromBigQuery() (int64, error)
	FetchSalesVoidAfterId(lastId int64) ([]map[string]interface{}, error)
	FetchLastSalesTaxIdFromBigQuery() (int64, error)
	FetchSalesTaxAfterId(lastId int64) ([]map[string]interface{}, error)
	UpdateMaxTimeSync()
	FetchLastSalesDetailTaxIdFromBigQuery() (int64, error)
	FetchSalesDetailTaxAfterId(id int64) ([]map[string]interface{}, error)
	FetchLastSalesDetailDiscIdFromBigQuery() (int64, error)
	FetchSalesDetailDiscAfterId(lastId int64) ([]map[string]interface{}, error)
	FetchLastSalesPromotionIdFromBigQuery() (int64, error)
	FetchSalesPromotionAfterId(id int64) ([]map[string]interface{}, error)
	FetchLastSalesDetailPromotionIdFromBigQuery() (int64, error)
	FetchSalesDetailPromotionAfterId(id int64) ([]map[string]interface{}, error)
}
