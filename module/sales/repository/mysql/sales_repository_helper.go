package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func changeMapType(data []map[string]string) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)
	for _, row := range data {
		rowData := make(map[string]interface{})
		for k, v := range row {
			rowData[k] = v
		}
		result = append(result, rowData)
	}
	return result
}

func removeDuplicateIds(cacheData []map[string]string, keyField string, ids ...interface{}) []interface{} {
	idMap := make(map[string]bool)
	for _, row := range ids {
		idMap[cast.ToString(row)] = true
	}

	for _, cache := range cacheData {
		delete(idMap, cast.ToString(cache[keyField]))
	}

	return array.GetKeys(idMap)
}
