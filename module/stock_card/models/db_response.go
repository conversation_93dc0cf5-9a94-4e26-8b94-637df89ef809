package models

type StockCardTransactionDBResponse struct {
	ParentID    string  `json:"parent_id"`
	DataCreated int64   `json:"data_created"`
	ID          string  `json:"id"`
	AdminFkid   string  `json:"admin_fkid"`
	DataSource  string  `json:"data_source"`
	Qty         float32 `json:"qty"`
	Calculate   float32 `json:"calculate"`
	StockIn     float32 `json:"stock_in"`
	StockOut    float32 `json:"stock_out"`
	Opname      float32 `json:"opname"`
	Operator    string  `json:"operator"`
}

type StockCardJsonResponse struct {
	ID          string  `json:"id"`
	ParentID    string  `json:"parent_id"`
	TimeCreated int64   `json:"time_created"`
	DateCreated string  `json:"date_created"`
	Open        float32 `json:"open"`
	StockIn     float32 `json:"stock_in"`
	StockOut    float32 `json:"stock_out"`
	Closing     float32 `json:"closing"`
	Opname      float32 `json:"opname"`
	Balance     float32 `json:"balance"`
	DataSource  string  `json:"data_source"`
	Operator    string  `json:"operator"`
}
