package http

import (
	"encoding/json"
	"fmt"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type closeShiftHandler struct {
	uc domain.CloseShiftUseCase
}

func NewHttpCloseShiftHandler(app *fasthttprouter.Router, useCase domain.CloseShiftUseCase) {
	handler := &closeShiftHandler{useCase}
	app.GET("/v1/close-shift/recap", auth.ValidateToken(handler.FetchCloseShiftRecap))
	app.POST("/v1/close-shift/recap", auth.ValidateToken(handler.FetchCloseShiftRecap))
}

func (h *closeShiftHandler) FetchCloseShiftRecap(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	request := parser.RequestParamSales(ctx)

	result, err := h.uc.FetchCloseShiftRecap(request, user)
	if err != nil {
		fmt.Println(">> err: ", err)
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{"error": err.Error()})
		return
	}
	// fmt.Println(">>> result: ", result)
	_ = json.NewEncoder(ctx).Encode(result)
}
