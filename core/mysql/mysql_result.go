package mysql

import (
	"errors"
	"reflect"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func (s *SqlResult) Model(model interface{}) error {
	if s.Error != nil {
		return s.Error
	}

	//return mapstructure.Decode(s.Result[0], model)
	//e, _ := json.Marshal(s.Data)
	//fmt.Println(string(e))

	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return cast.MapToStruct(m, model)
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				//jsonStr, err := json.<PERSON>(arr)
				//if err != nil {
				//	return err
				//}
				//return json.Unmarshal(jsonStr, model)
				return cast.MapArrayToStruct(arr, model)
			} else {
				return cast.MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("model is not single map")
}

func (s *SqlResult) Map() (map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr[0], nil
		}
	}
	return nil, nil
}

func (s *SqlResult) MapArray() ([]map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return []map[string]interface{}{m}, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr, nil
		}
	} else if arr, ok := s.Data.([]map[string]string); ok {
		return array.ChangeMapType(arr), nil
	}
	return nil, nil
}

func (s *SqlResult) IsEmpty() bool {
	if s.Error != nil {
		return false
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return len(m) == 0
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		return len(arr) == 0
	} else if arr, ok := s.Data.([]map[string]string); ok {
		return len(arr) == 0
	}

	return false
}
