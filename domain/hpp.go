package domain

import (
	"encoding/json"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type Hpp struct {
	StockPriceId      int                    `json:"stock_price_id,omitempty"`
	ProductDetailFkid int                    `json:"product_detail_fkid,omitempty"`
	StockIn           float32                `json:"stock_in"`
	StockOut          float32                `json:"stock_out"`
	Price             float32                `json:"price,omitempty"`
	StockInId         string                 `json:"stock_in_id,omitempty"`
	StockOutId        string                 `json:"stock_out_id,omitempty"`
	StockInSource     string                 `json:"stock_in_source,omitempty"`
	StockOutSource    string                 `json:"stock_out_source,omitempty"`
	UpdatedAt         int64                  `json:"updated_at,omitempty"`
	CreatedAt         int64                  `json:"created_at,omitempty"`
	StockInMetaData   map[string]interface{} `json:"stock_in_meta_data,omitempty"`
	StockOutMetaData  map[string]interface{} `json:"stock_out_meta_data,omitempty"`
}

type StockPriceDetail struct {
	StockPriceId   int
	Qty            float32
	StockOutId     string
	StockOutSource string
	CreatedAt      int64
	MetaData       map[string]interface{}
}

type StockPriceFilter struct {
	ProductDetailId   int
	ProductDetailIds  []int
	StockOut          FilterInt
	StockIn           FilterInt
	IsAvailable       bool //if stock_out < stock_in or stock_in_id null
	IsPending         bool //if stock_in_id is null
	DateStart         int64
	StockPriceId      int64
	StockPriceIdStart int64 //to get all stock_price after this id
}

type FilterInt struct {
	Value    int
	Operator string //options: >, <, <=, >=, =
}

type HppSource int

// NOTE WARNING!!! : when adding new variable, make sure adding in String() func below as well
const (
	PurchaseConfirm HppSource = iota
	PurchaseRetur
	Sale
	SaleRefund
	StockOpname
	Production
	TransferConfirm
	SaleBreakdown
	PurchaseConfirmUpdate
	Spoil
)

func (s HppSource) String() string {
	switch s {
	case PurchaseConfirm:
		return "purchase_confirm"
	case PurchaseRetur:
		return "purchase_retur"
	case Sale:
		return "sales"
	case SaleRefund:
		return "sales_refund"
	case StockOpname:
		return "stock_opname"
	case Production:
		return "production"
	case TransferConfirm:
		return "transfer_confirm"
	case SaleBreakdown:
		return "sales_breakdown"
	case PurchaseConfirmUpdate:
		return "purchase_confirm"
	case Spoil:
		return "spoil"
	}

	return "-"
}

func (s HppSource) IsStockIn() bool {
	switch s {
	case PurchaseConfirm, Production, TransferConfirm:
		return true
	default:
		return false
	}
}

func (hpp *Hpp) ToMap() map[string]interface{} {
	if hpp.CreatedAt == 0 {
		hpp.CreatedAt = time.Now().UnixNano() / int64(time.Millisecond)
	}
	if hpp.UpdatedAt == 0 {
		hpp.UpdatedAt = time.Now().UnixNano() / int64(time.Millisecond) //time.Now().Unix() * 1000
	}

	// data := make(map[string]interface{})
	data := map[string]interface{}{
		"product_detail_fkid": hpp.ProductDetailFkid,
		"stock_in":            hpp.StockIn,
		"stock_out":           hpp.StockOut,
		"price":               hpp.Price,
		"updated_at":          hpp.UpdatedAt,
		"created_at":          hpp.CreatedAt,
	}

	if hpp.StockInId != "" {
		data["stock_in_id"] = hpp.StockInId
	}
	if hpp.StockInSource != "" {
		data["stock_in_source"] = hpp.StockInSource
	}
	if len(hpp.StockInMetaData) > 0 {
		metaData, _ := json.Marshal(hpp.StockInMetaData)
		data["meta_data"] = string(metaData)
	}

	return data
}

func (h Hpp) HasNoStockIn() bool {
	return h.StockInId == "" && h.StockInSource == ""
}

func (s StockPriceDetail) ToMap() map[string]interface{} {
	data := map[string]interface{}{
		"stock_price_fkid": s.StockPriceId,
		"stock_out":        s.Qty,
		"stock_out_id":     s.StockOutId,
		"stock_out_source": s.StockOutSource,
		"created_at":       s.CreatedAt,
	}
	if s.CreatedAt == 0 {
		data["created_at"] = utils.CurrentMillis()
	}
	if len(s.MetaData) > 0 {
		metaData, _ := json.Marshal(s.MetaData)
		data["meta_data"] = metaData
	}
	return data
}

type HppSubscription struct {
	Source HppSource
	Id     string
	Qty    int
}

type HppSimulationResponse struct {
	UpdateHpp []Hpp
	Changes   map[int][]models.HppUpdate
}

type StockPriceRequest struct {
	ProductName string
	OutletName  string
	AdminId     int
	StartDate   int64
}

type StockPriceResponse struct {
	ProductName        string  `json:"product_name,omitempty"`
	OutletName         string  `json:"outlet_name,omitempty"`
	CreatedAtIn        int64   `json:"created_at_in,omitempty"`
	StockPriceID       int     `json:"stock_price_id,omitempty"`
	ProductDetailFkid  int     `json:"product_detail_fkid"`
	StockIn            int     `json:"stock_in"`
	StockOut           float32 `json:"stock_out"`
	StockOutTotal      float32 `json:"stock_out_total"`
	Price              float32 `json:"price"`
	StockInID          string  `json:"stock_in_id,omitempty"`
	StockInSource      string  `json:"stock_in_source,omitempty"`
	UpdatedAt          int64   `json:"updated_at,omitempty"`
	CreatedAt          int64   `json:"created_at,omitempty"`
	DeletedAt          int64   `json:"deleted_at,omitempty"`
	MetaDataIn         string  `json:"meta_data_in,omitempty"`
	MetaData           string  `json:"meta_data,omitempty"`
	StockPriceDetailID int     `json:"stock_price_detail_id,omitempty"`
	StockPriceFkid     int     `json:"stock_price_fkid,omitempty"`
	StockOutID         string  `json:"stock_out_id,omitempty"`
	StockOutSource     string  `json:"stock_out_source,omitempty"`
}

type StockPriceUpdateRequest struct {
	StockInSrouce string
	StockInId     string
	Qty           float32
	AdminId       int
}
