package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
)

type crmHandler struct {
	uc crm.UseCase
}

func NewHttpCrmHandler(app *fasthttprouter.Router, useCase crm.UseCase) {
	handler := &crmHandler{useCase}

	app.GET("/v1/crm/customer", auth.ValidateToken(handler.FetchCustomerList))
	app.POST("/v1/crm/customer/:format", auth.ValidateToken(handler.FetchCustomerList))

	app.GET("/v1/crm/customer-transaction", auth.ValidateToken(handler.FetchCustomerTransactionList))
	app.POST("/v1/crm/customer-transaction/:format", auth.ValidateToken(handler.FetchCustomerTransactionList))

	//acquisition
	app.GET("/v1/crm/acquisition", auth.ValidateToken(handler.FetchMemberAcquisition))

	//cohort
	app.GET("/v1/crm/cohort", auth.ValidateToken(handler.FetchCohortAnalysis))
	app.POST("/v1/crm/cohort/:format", auth.ValidateToken(handler.FetchCohortAnalysis))

}

func (h crmHandler) FetchCustomerList(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	format := cast.ToString(ctx.UserValue("format"))
	log.Info("format: %v ", format)

	//ctx.PostArgs().VisitAll(func(key, value []byte) {
	//	log.Info("%v -- %v", string(key), string(value))
	//})

	result, err := h.uc.FetchCustomerList(user, parser.RequestParam(ctx))
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(result)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h crmHandler) FetchCustomerTransactionList(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	format := cast.ToString(ctx.UserValue("format"))
	log.Info("format: %v", format)

	param := parser.RequestParam(ctx)
	result, err := h.uc.FetchCustomerTransactionList(user, param)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(result)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h crmHandler) FetchMemberAcquisition(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	format := cast.ToString(ctx.UserValue("format"))
	log.Info("format: %v", format)

	param := parser.RequestParam(ctx)
	result, err := h.uc.FetchMemberAcquisition(user, param)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(result)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h crmHandler) FetchCohortAnalysis(ctx *fasthttp.RequestCtx) {
	user := domain.GetUserSessionOfFastHttp(ctx)
	format := cast.ToString(ctx.UserValue("format"))
	log.Info("format: %v", format)

	param := parser.RequestParam(ctx)
	data, err := h.uc.FetchCohortAnalysis(user, param)
	if err != nil {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"message": err.Error(),
		})
		return
	}

	result := map[string]interface{}{"data": data, "offsetId": 0}
	_ = json.NewEncoder(ctx).Encode(result)
}
