package parser

import (
	"fmt"
	"time"
)

type TimePeriod struct {
	CurrentPeriodStart        string
	CurrentPeriodEnd          string
	PreviousPeriodStart       string
	PreviousPeriodEnd         string
	PreviousPeriod            string
	CurrentPeriod             string
	PreviousPeriodStartMillis int64
	PreviousPeriodEndMillis   int64
}

func CalculatePeriods(timeOffset, timeStart, timeEnd int64) TimePeriod {
	// Create a location with the specified offset
	loc := time.FixedZone("CustomZone", int(timeOffset/1000)) // Offset in seconds

	// Convert milliseconds to time.Time using the location
	startTime := time.UnixMilli(timeStart).In(loc)
	endTime := time.UnixMilli(timeEnd).In(loc)

	// Calculate the duration of the current period
	duration := endTime.Sub(startTime)

	// Calculate the start and end times of the previous period
	prevStartTime := startTime.Add(-duration)
	prevEndTime := endTime.Add(-duration)

	// Format the dates as dd-mm-yyyy
	currentPeriodStart := startTime.Format("02-01-2006")
	currentPeriodEnd := endTime.Format("02-01-2006")
	previousPeriodStart := prevStartTime.Format("02-01-2006")
	previousPeriodEnd := prevEndTime.Format("02-01-2006")

	currentPeriodFormat := fmt.Sprintf("%s to %s", currentPeriodStart, currentPeriodEnd)
	previousPeriodFormat := fmt.Sprintf("%s to %s", previousPeriodStart, previousPeriodEnd)

	if startTime.Day() == endTime.Day() && startTime.Month() == endTime.Month() && startTime.Year() == endTime.Year() {
		currentPeriodFormat = currentPeriodStart
		previousPeriodFormat = previousPeriodStart
	}

	return TimePeriod{
		PreviousPeriod:            previousPeriodFormat,
		CurrentPeriod:             currentPeriodFormat,
		CurrentPeriodStart:        currentPeriodStart,
		CurrentPeriodEnd:          currentPeriodEnd,
		PreviousPeriodStart:       previousPeriodStart,
		PreviousPeriodEnd:         previousPeriodEnd,
		PreviousPeriodStartMillis: prevStartTime.Unix() * 1000,
		PreviousPeriodEndMillis:   prevEndTime.Unix() * 1000,
	}
}
