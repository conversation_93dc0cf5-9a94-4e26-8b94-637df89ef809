package v1

import (
	"reflect"
	"testing"
	"time"

	"github.com/joho/godotenv"
)

func TestSalesAnalysis_mapByPeriod(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-report/.env")

	type args struct {
		adminId    string
		timeStart  int64
		timeEnd    int64
		timeDiff   int64
		outletIds  []interface{}
		timeFormat string
	}
	tests := []struct {
		name string
		args args
		want map[string]interface{}
	}{
		{"test1", args{
			adminId:    "1",
			timeStart:  time.Now().AddDate(0, -11, 0).Unix() * 1000,
			timeEnd:    time.Now().Unix() * 1000,
			timeDiff:   25200 * 1000,
			outletIds:  []interface{}{29},
			timeFormat: "quarter",
		}, map[string]interface{}{}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SalesAnalysis_mapByPeriod(tt.args.adminId, tt.args.timeStart, tt.args.timeEnd, tt.args.timeDiff, tt.args.outletIds, tt.args.timeFormat); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SalesAnalysis_mapByPeriod() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_generatePeriodRange(t *testing.T) {
	type args struct {
		period    string
		timeStart int64
		timeEnd   int64
		timeDiff  int64
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{"yearly", args{"yearly", 1609434000000, 1672203600849, 25200000}, []string{"2021", "2022"}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := generatePeriodRange(tt.args.period, tt.args.timeStart, tt.args.timeEnd, tt.args.timeDiff); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("generatePeriodRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
