package v1

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strings"
	"time"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// ambil data dalam satu hari, dan output per-jam
func querySalesHourOnDayByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
		date_format(from_unixtime(((s.time_created + ?) / 1000)), '%H') AS time,
		sum(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY time
		ORDER BY time ASC
	`, timeDiff, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}
func dataSalesHourOnDayByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	diff := timeEnd - timeStart
	timeStart2 := timeStart - 1 - diff
	timeEnd2 := timeStart - 1

	//exec query
	chanDataNow := make(chan []map[string]interface{})
	go func() {
		chanDataNow <- querySalesHourOnDayByShift(adminId, timeStart, timeEnd, timeDiff, outletIds)
	}()
	chanDataPrev := make(chan []map[string]interface{})
	go func() {
		chanDataPrev <- querySalesHourOnDayByShift(adminId, timeStart2, timeEnd2, timeDiff, outletIds)
	}()

	dataNow := <-chanDataNow
	dataPrev := <-chanDataPrev

	//map data
	chanDataNowMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataNow {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}
		chanDataNowMap <- temp
	}()
	chanDataPrevMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataPrev {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}
		chanDataPrevMap <- temp
	}()
	dataNowMap := <-chanDataNowMap
	dataPrevMap := <-chanDataPrevMap

	data := []map[string]interface{}{}
	for i := 0; i < 24; i++ {
		stringHour := cast.ToString(i)
		if len(stringHour) == 1 {
			stringHour = "0" + stringHour
		}

		grandNow := "0"
		if dataNowMap[stringHour] != nil {
			grandNow = cast.ToString(dataNowMap[stringHour])
		}

		grandPrev := "0"
		if dataPrevMap[stringHour] != nil {
			grandPrev = cast.ToString(dataPrevMap[stringHour])
		}

		if grandNow != "0" || grandPrev != "0" {
			data = append(data, map[string]interface{}{
				"time": stringHour + ":00",
				"now":  grandNow,
				"prev": grandPrev,
				"diff": utils.ToInt(grandNow) - utils.ToInt(grandPrev),
			})
		}
	}

	return data
}

// ambil data < 7 day & last 30 day, dan output tanggal
func querySalesByDateByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
		date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y-%m-%d') AS time,
		sum(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY time
		ORDER BY time ASC
	`, timeDiff, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}
func dataSalesByDateByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	diff := timeEnd - timeStart
	timeStart2 := timeStart - 1 - diff
	timeEnd2 := timeStart - 1

	//exec query
	chanDataNow := make(chan []map[string]interface{})
	go func() {
		chanDataNow <- querySalesByDateByShift(adminId, timeStart, timeEnd, timeDiff, outletIds)
	}()
	chanDataPrev := make(chan []map[string]interface{})
	go func() {
		chanDataPrev <- querySalesByDateByShift(adminId, timeStart2, timeEnd2, timeDiff, outletIds)
	}()

	dataNow := <-chanDataNow
	dataPrev := <-chanDataPrev

	//mapping data
	chanDataNowMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataNow {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartLocal := timeStart + timeDiff
		timeEndLocal := timeEnd + timeDiff

		timeStartTime := utils.MillisToTime(timeStartLocal)
		timeEndTime := utils.MillisToTime(timeEndLocal)

		dateStart := timeStartTime.Format("20060102")
		dateEnd := timeEndTime.Format("20060102")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006-01-02")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("02 Jan"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(0, 0, 1)
			dateStart = timeStartTime.Format("20060102")
			//fmt.Println("date start after: ", dateStart)
		}
		chanDataNowMap <- data
	}()
	chanDataPrevMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataPrev {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartLocal := timeStart2 + timeDiff
		timeEndLocal := timeEnd2 + timeDiff

		timeStartTime := utils.MillisToTime(timeStartLocal)
		timeEndTime := utils.MillisToTime(timeEndLocal)

		dateStart := timeStartTime.Format("20060102")
		dateEnd := timeEndTime.Format("20060102")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006-01-02")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("02 Jan"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(0, 0, 1)
			dateStart = timeStartTime.Format("20060102")
			//fmt.Println("date start after: ", dateStart)
		}
		chanDataPrevMap <- data
	}()
	dataNowMap := <-chanDataNowMap
	dataPrevMap := <-chanDataPrevMap

	//fmt.Println("now: ", utils.SimplyToJson(dataNowMap))
	//fmt.Println("prev: ", utils.SimplyToJson(dataPrevMap))

	data := []map[string]interface{}{}
	for i, _ := range dataNowMap {
		dataNow := dataNowMap[i]
		dataPrev := dataPrevMap[i]
		data = append(data, map[string]interface{}{
			"time": utils.ToString(dataNow["time"]) + " / " + utils.ToString(dataPrev["time"]),
			"now":  dataNow["grandtotal"],
			"prev": dataPrev["grandtotal"],
			"diff": utils.ToInt(dataNow["grandtotal"]) - utils.ToInt(dataPrev["grandtotal"]),
		})
	}

	return data
}

// ambil data this month
func querySalesByMonthByShift(adminId, dateMonth string, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
		`+ /*date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y-%m-%d') AS time,*/ `
		date_format(from_unixtime(((os.time_open + ?) / 1000)), '%Y-%m-%d') AS time,
		sum(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid = ?
		`+ /*AND date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y-%m') = ?*/ `
		AND date_format(from_unixtime(((os.time_open + ?) / 1000)), '%Y-%m') = ?
		`+whereInOutlet+`
		GROUP BY time
		ORDER BY time ASC
	`, timeDiff, adminId, timeDiff, dateMonth)
	utils.CheckErr(err)
	return query
}
func dataSalesByMonthByShift(adminId string, timeStart, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	timeNow := utils.MillisToTime(timeStart + timeDiff)
	timePrev := timeNow.AddDate(0, -1, 0)
	fmt.Println("date1: ", timeNow.Format("2006-01-02"))
	fmt.Println("date2: ", timePrev.Format("2006-01-02"))

	daysInTimeNow := utils.DaysInMonth(utils.ToInt(timeNow.Format("01")), utils.ToInt(timeNow.Format("2006")))
	daysInTimePrev := utils.DaysInMonth(utils.ToInt(timePrev.Format("01")), utils.ToInt(timePrev.Format("2006")))

	fmt.Println("jumlah hari 1: ", daysInTimeNow)
	fmt.Println("jumlah hari 2: ", daysInTimePrev)

	chanDataNow := make(chan []map[string]interface{})
	chanDataPrev := make(chan []map[string]interface{})
	go func() {
		dataSales := querySalesByMonthByShift(adminId, timeNow.Format("2006-01"), timeDiff, outletIds)
		temp := map[string]interface{}{}
		for _, data := range dataSales {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//init
		forStart := timeNow.Format("200601") + "01"
		getTime, _ := time.Parse("20060102", forStart)

		var data []map[string]interface{}
		for i := 0; i < daysInTimeNow; i++ {
			id := getTime.Format("2006-01-02")
			grandtotal := "0"
			if temp[id] != nil {
				grandtotal = utils.ToString(temp[id])
			}
			data = append(data, map[string]interface{}{
				"id":         i,
				"time":       getTime.Format("(Mon) 02 Jan"),
				"grandtotal": grandtotal,
			})

			//add time
			getTime = getTime.AddDate(0, 0, 1)
		}

		chanDataNow <- data
	}()
	go func() {
		dataSales := querySalesByMonthByShift(adminId, timePrev.Format("2006-01"), timeDiff, outletIds)
		temp := map[string]interface{}{}
		for _, data := range dataSales {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//init
		forStart := timePrev.Format("200601") + "01"
		getTime, _ := time.Parse("20060102", forStart)

		var data []map[string]interface{}
		for i := 0; i < daysInTimePrev; i++ {
			id := getTime.Format("2006-01-02")
			grandtotal := "0"
			if temp[id] != nil {
				grandtotal = utils.ToString(temp[id])
			}
			data = append(data, map[string]interface{}{
				"id":         i,
				"time":       getTime.Format("(Mon) 02 Jan"),
				"grandtotal": grandtotal,
			})

			//add time
			getTime = getTime.AddDate(0, 0, 1)
		}

		chanDataPrev <- data
	}()

	//tampung hasil
	dataNow := <-chanDataNow
	dataPrev := <-chanDataPrev

	//render hasil
	dataFinal := []map[string]interface{}{}
	for i := 0; i < len(dataNow) || i < len(dataPrev); i++ {
		timeNow := "-"
		grandNow := 0
		if i+1 <= len(dataNow) {
			timeNow = utils.ToString(dataNow[i]["time"])
			grandNow = utils.ToInt(dataNow[i]["grandtotal"])
		}

		timePrev := "-"
		grandPrev := 0
		if i+1 <= len(dataPrev) {
			timePrev = utils.ToString(dataPrev[i]["time"])
			grandPrev = utils.ToInt(dataPrev[i]["grandtotal"])
		}

		grandDiff := grandNow - grandPrev

		dataFinal = append(dataFinal, map[string]interface{}{
			"time": timeNow + " / " + timePrev,
			"now":  grandNow,
			"prev": grandPrev,
			"diff": grandDiff,
		})
	}

	return dataFinal
}

// ambil data bulanan dalam satu tahun
func querySalesByMonthOfYearByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
		`+ /*date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y-%m') AS time,*/ `
		date_format(from_unixtime(((os.time_open + ?) / 1000)), '%Y-%m') AS time,
		sum(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY time
		ORDER BY time ASC
	`, timeDiff, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}
func dataSalesByMonthOfYearByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	diff := timeEnd - timeStart
	timeStart2 := timeStart - 1 - diff
	timeEnd2 := timeStart - 1

	fmt.Println("timestart: ", timeStart2)
	fmt.Println("timeend: ", timeEnd2)

	//exec query
	chanDataNow := make(chan []map[string]interface{})
	go func() {
		chanDataNow <- querySalesByMonthOfYearByShift(adminId, timeStart, timeEnd, timeDiff, outletIds)
	}()
	chanDataPrev := make(chan []map[string]interface{})
	go func() {
		chanDataPrev <- querySalesByMonthOfYearByShift(adminId, timeStart2, timeEnd2, timeDiff, outletIds)
	}()

	dataNow := <-chanDataNow
	dataPrev := <-chanDataPrev

	//map data
	chanDataNowMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataNow {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartLocal := timeStart + timeDiff
		timeEndLocal := timeEnd + timeDiff

		timeStartTime := utils.MillisToTime(timeStartLocal)
		timeEndTime := utils.MillisToTime(timeEndLocal)

		dateStart := timeStartTime.Format("200601")
		dateEnd := timeEndTime.Format("200601")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006-01")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("Jan 2006"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(0, 1, 0)
			dateStart = timeStartTime.Format("200601")
			//fmt.Println("date start after: ", dateStart)
		}
		chanDataNowMap <- data
	}()
	chanDataPrevMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataPrev {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartTime := utils.MillisToTime(timeStart2 + timeDiff)
		timeEndTime := utils.MillisToTime(timeEnd2 + timeDiff)

		dateStart := timeStartTime.Format("200601")
		dateEnd := timeEndTime.Format("200601")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006-01")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("Jan 2006"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(0, 1, 0)
			dateStart = timeStartTime.Format("200601")
			//fmt.Println("date start after: ", dateStart)
		}
		chanDataPrevMap <- data
	}()
	dataNowMap := <-chanDataNowMap
	dataPrevMap := <-chanDataPrevMap

	//fmt.Println("hasil akhir finishing: ", utils.SimplyToJson(dataNowMap))
	//fmt.Println("panjang: ",len(dataNowMap))
	//fmt.Println("hasil akhir finishing: ", utils.SimplyToJson(dataPrevMap))
	//fmt.Println("panjang: ",len(dataPrevMap))

	//render data for json
	data := []map[string]interface{}{}
	for i := 0; i < len(dataNowMap) || i < len(dataPrevMap); i++ {
		dataNow := dataNowMap[i]
		dataPrev := dataPrevMap[i]
		data = append(data, map[string]interface{}{
			"time": utils.ToString(dataNow["time"]) + " / " + utils.ToString(dataPrev["time"]),
			"now":  dataNow["grandtotal"],
			"prev": dataPrev["grandtotal"],
			"diff": utils.ToInt(dataNow["grandtotal"]) - utils.ToInt(dataPrev["grandtotal"]),
		})
	}

	return data
}

// ambil data tahunan
func querySalesByYearByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
		`+ /*date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y') AS time,*/ `
		date_format(from_unixtime(((s.time_created + ?) / 1000)), '%Y') AS time,
		sum(IF(s.status='Success' AND s.data_status='on' AND s.payment NOT IN ('COMPLIMENT', 'DUTY MEALS'), s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY time
		ORDER BY time ASC
	`, timeDiff, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}
func dataSalesByYearByShift(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) []map[string]interface{} {
	diff := timeEnd - timeStart
	timeStart2 := timeStart - 1 - diff
	timeEnd2 := timeStart - 1

	//exec query
	chanDataNow := make(chan []map[string]interface{})
	go func() {
		chanDataNow <- querySalesByYearByShift(adminId, timeStart, timeEnd, timeDiff, outletIds)
	}()
	chanDataPrev := make(chan []map[string]interface{})
	go func() {
		chanDataPrev <- querySalesByYearByShift(adminId, timeStart2, timeEnd2, timeDiff, outletIds)
	}()

	dataNow := <-chanDataNow
	dataPrev := <-chanDataPrev

	fmt.Println("hasil query1: ", utils.SimplyToJson(dataNow))
	fmt.Println("hasil query2: ", utils.SimplyToJson(dataPrev))

	//map data
	chanDataNowMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataNow {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartLocal := timeStart + timeDiff
		timeEndLocal := timeEnd + timeDiff

		timeStartTime := utils.MillisToTime(timeStartLocal)
		timeEndTime := utils.MillisToTime(timeEndLocal)

		dateStart := timeStartTime.Format("2006")
		dateEnd := timeEndTime.Format("2006")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("2006"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(1, 0, 0)
			dateStart = timeStartTime.Format("2006")
			//fmt.Println("date start after: ", dateStart)
		}
		chanDataNowMap <- data
	}()
	chanDataPrevMap := make(chan []map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range dataPrev {
			id := cast.ToString(data["time"])
			temp[id] = data["sum_grandtotal"]
		}

		//convert time millis to Y-m-d H:i:s
		timeStartLocal := timeStart2 + timeDiff
		timeEndLocal := timeEnd2 + timeDiff

		timeStartTime := utils.MillisToTime(timeStartLocal)
		timeEndTime := utils.MillisToTime(timeEndLocal)

		dateStart := timeStartTime.Format("2006")
		dateEnd := timeEndTime.Format("2006")

		//render
		var data []map[string]interface{}
		for utils.ToInt(dateStart) <= utils.ToInt(dateEnd) {
			//fmt.Println("date start now: ", dateStart)

			id := timeStartTime.Format("2006")
			grandTotal := "0"
			if temp[id] != nil {
				grandTotal = utils.ToString(temp[id])
			}

			//render data
			data = append(data, map[string]interface{}{
				"time":       timeStartTime.Format("2006"),
				"grandtotal": grandTotal,
			})

			//add datestart
			timeStartTime = timeStartTime.AddDate(1, 0, 0)
			dateStart = timeStartTime.Format("2006")
			fmt.Println("date start after: ", dateStart)
		}
		chanDataPrevMap <- data
	}()
	dataNowMap := <-chanDataNowMap
	dataPrevMap := <-chanDataPrevMap

	//render data for json
	data := []map[string]interface{}{}
	for i, _ := range dataNowMap {
		dataNow := dataNowMap[i]
		dataPrev := dataPrevMap[i]
		data = append(data, map[string]interface{}{
			"time": utils.ToString(dataNow["time"]) + " / " + utils.ToString(dataPrev["time"]),
			"now":  dataNow["grandtotal"],
			"prev": dataPrev["grandtotal"],
			"diff": utils.ToInt(dataNow["grandtotal"]) - utils.ToInt(dataPrev["grandtotal"]),
		})
	}

	return data
}

func SalesAnalysisByShift_map(adminId string, timeStart, timeEnd, timeDiff int64, outletIds []interface{}) map[string]interface{} {
	//cek type
	format := "2006-01-02 15:04:05"
	d1, _ := utils.MillisToDateTime(timeStart + timeDiff)
	dateStart, _ := time.Parse(format, d1)
	dateStartOnly := dateStart.Format("02")

	d2, _ := utils.MillisToDateTime(timeEnd + timeDiff)
	dateEnd, _ := time.Parse(format, d2)
	dateDiff := dateEnd.Sub(dateStart)

	dayDiff := math.Round(dateDiff.Hours() / 24)

	dataResult := []map[string]interface{}{}
	dataType := ""
	if dayDiff == 1 {
		dataType = "1_day"
		dataResult = dataSalesHourOnDayByShift(cast.ToString(adminId), timeStart, timeEnd, timeDiff, outletIds)
	} else if dayDiff > 1 && dayDiff <= 7 {
		dataType = "7_day"
		dataResult = dataSalesByDateByShift(cast.ToString(adminId), timeStart, timeEnd, timeDiff, outletIds)
	} else if dayDiff >= 7 && dayDiff <= 30 {
		dataType = "30_day"
		dataResult = dataSalesByDateByShift(cast.ToString(adminId), timeStart, timeEnd, timeDiff, outletIds)
	} else if dayDiff > 28 && dayDiff <= 31 && utils.ToString(dateStartOnly) == "01" {
		dataType = "this_month"
		dataResult = dataSalesByMonthByShift(cast.ToString(adminId), timeStart, timeDiff, outletIds)
	} else if dayDiff > 31 && dayDiff <= 366 {
		dataType = "month_year"
		dataResult = dataSalesByMonthOfYearByShift(cast.ToString(adminId), timeStart, timeEnd, timeDiff, outletIds)
	} else {
		dataType = "yearly"
		dataResult = dataSalesByYearByShift(cast.ToString(adminId), timeStart, timeEnd, timeDiff, outletIds)
	}

	//set response
	response := map[string]interface{}{
		"data_type": dataType,
		"result":    dataResult,
	}

	return response
}

func SalesAnalysisByShift_v1(ctx *fasthttp.RequestCtx) {
	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
	} else {
		jsonPost := ctx.PostBody()
		post, err := djson.DecodeObject(jsonPost)
		utils.CheckErr(err)

		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(post["time_start"])
		timeEnd := cast.ToInt64(post["time_end"])
		timeDiff := cast.ToInt64(post["time_diff"]) //timezone
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess, ",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//set legend
		timeStartTime := utils.MillisToTime(timeStart + timeDiff)
		timeEndTime := utils.MillisToTime(timeEnd + timeDiff)
		timeStartPreviousTime := utils.MillisToTime(timeStart + timeDiff - (timeEnd - timeStart) - 1)
		legendNowText := ""
		legendPreviousText := ""
		if timeStartTime.Format("02-01-2006") == timeEndTime.Format("02-01-2006") {
			legendNowText = timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		} else {
			legendNowText = timeStartTime.Format("02-01-2006") + " to " + timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartPreviousTime.Format("02-01-2006") + " to " + timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		response := SalesAnalysisByShift_map(adminId, timeStart, timeEnd, timeDiff, outletIds)
		response["legend"] = map[string]interface{}{
			"now":  legendNowText,
			"prev": legendPreviousText,
		}

		//output
		ctx.SetStatusCode(http.StatusOK)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: true,
			Code:   200,
			Data:   response,
		})
	}

}
