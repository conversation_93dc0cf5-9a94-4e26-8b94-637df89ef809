package usecase

import (
	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/stock_card/models"
	"strings"
	"time"
)

type stockCardUseCase struct {
	repo domain.StockCardRepositoryMiddleware
}

func NewStockCardUseCase(repo domain.StockCardRepositoryMiddleware) domain.StockCardUseCase {
	return &stockCardUseCase{repo}
}

func (s stockCardUseCase) FetchStockCard(adminId string, timeStart int64, timeEnd int64, timeDiff int64, outletId string, productId string, variantId string) ([]models.StockCardJsonResponse, error) {
	//initial
	var stockCardList []models.StockCardJsonResponse
	product_detail_id := ""
	currentTimeMillis := time.Now().UnixMilli()
	dayTimeMillis := cast.ToInt64(86400 * 1000)
	timeEndMax := currentTimeMillis + dayTimeMillis

	log.Debug("Get Stock Card from %v to %v", timeStart, timeEnd)

	// VALIDATION START
	//--------------------------------------------------
	//validation time picker
	if timeStart > timeEnd {
		return stockCardList, nil
	}

	//validation product detail
	productDetail, _ := s.repo.FetchProductDetail(adminId, outletId, productId, variantId)
	if len(productDetail) == 0 {
		log.Debug("Product detail not found.")
		return stockCardList, nil
	}
	//--------------------------------------------------
	product_detail_id = cast.ToString(productDetail["product_detail_id"])
	log.Debug("PD.ID -> %v", product_detail_id)

	//make timeEnd to current time when over
	if timeEnd > timeEndMax {
		timeEnd = timeEndMax
		log.Debug("Make timeEnd from %v to %v", timeEnd, timeEndMax)
	}

	//ambil waktu terakhir opname sebelum tanggal mulai datepicker
	time_last_opname, err := s.repo.FetchLastOpname(product_detail_id, timeStart)
	if err != nil {
		log.Error("Error get last opname: %v", err)
		return nil, err
	}
	log.Debug("Last time opname: %v", time_last_opname)

	//ambil stock open dengan cara mengkalkulasi dari waktu terakhir opname sampai tanggal awal datepicker
	chanStockOpen := make(chan float32)
	go func() {
		stock_open, err := s.repo.FetchOpenAfterOpname(product_detail_id, time_last_opname, timeStart)
		if err != nil {
			log.Error("Error get stock open after last opname: %v", err)
		}
		log.Debug("stock open -> %v", stock_open)
		chanStockOpen <- stock_open
	}()

	//operasi stockcard transaction
	chanTransaction := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		log.Debug("Fetching Stock Card Transaction...")
		transaction, err := s.repo.FetchTransaction(product_detail_id, timeStart, timeEnd)
		if err != nil {
			log.Error("Error get stock card transaction: PDID -> %v from %v to %v", product_detail_id, timeStart, timeEnd)
		}
		log.Debug("total transaction: %v", len(transaction))
		chanTransaction <- transaction
	}()

	//olah stock card data
	stock_open := <-chanStockOpen
	transaction := <-chanTransaction
	for _, item := range transaction {
		closing := stock_open + item.StockIn - item.StockOut
		balance := cast.ToFloat32(0)
		if item.DataSource == "opname" {
			balance = item.Opname - closing
		}

		//formating data source
		data_source := strings.ReplaceAll(item.DataSource, "_", " ")
		data_source = strings.Title(data_source)

		//formatting time
		date_created := utils.MillisToTime(item.DataCreated + timeDiff)
		time_created := cast.ToTime(date_created)
		formattedTime := time_created.Format("2006-01-02 15:04:05")

		//append data
		stockCardList = append(stockCardList, models.StockCardJsonResponse{
			ID:          item.ID,
			ParentID:    item.ParentID,
			TimeCreated: item.DataCreated,
			DateCreated: cast.ToString(formattedTime),
			Open:        stock_open,
			StockIn:     item.StockIn,
			StockOut:    item.StockOut,
			Closing:     closing,
			Opname:      item.Opname,
			Balance:     balance,
			DataSource:  data_source,
			Operator:    item.Operator,
		})

		//set stock open untuk row selanjutnya
		stock_open = closing
		if item.DataSource == "opname" {
			stock_open = item.Opname
		}
	}

	return stockCardList, err
}
