package models

type TaskBoardEntity struct {
	ID                 int    `json:"id,omitempty"`
	Name               string `json:"name,omitempty"`
	Description        string `json:"description,omitempty"`
	Status             string `json:"status,omitempty"`
	Priority           string `json:"priority,omitempty"`
	OutletID           int    `json:"outlet_id,omitempty"`
	AdminID            int    `json:"admin_id,omitempty"`
	EmployeeID         int    `json:"employee_id,omitempty"`
	TmMasterCategoryID int    `json:"tm_master_category_id,omitempty"`
	TaskTimeStart      int64  `json:"task_time_start,omitempty"`
	TaskTimeEnd        int64  `json:"task_time_end,omitempty"`
	TaskTimeComplete   any    `json:"task_time_complete,omitempty"`
	CreatedAt          int64  `json:"created_at,omitempty"`
	UpdatedAt          int64  `json:"updated_at,omitempty"`
	DeletedAt          int64  `json:"deleted_at,omitempty"`
}

type TaskMasterCategory struct {
	ID         int    `json:"id,omitempty"`
	Name       string `json:"name,omitempty"`
	EmployeeID int    `json:"employee_id,omitempty"`
	AdminID    int    `json:"admin_id,omitempty"`
	Visibility string `json:"visibility,omitempty"`
	CreatedAt  int64  `json:"created_at,omitempty"`
	UpdatedAt  int64  `json:"updated_at,omitempty"`
	DeletedAt  int64  `json:"deleted_at,omitempty"`
}
