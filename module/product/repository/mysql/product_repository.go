package mysql

import (
	"database/sql"
	"fmt"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type productRepository struct {
	mysql.Repository
}

func NewMysqlProductRepository(db *sql.DB) domain.ProductRepository {
	return &productRepository{mysql.Repository{Conn: db}}
}

func (p productRepository) FetchProductByFilter(filter domain.ProductFilter) ([]map[string]interface{}, error) {
	log.Info("filter: %v", filter)
	sql := "select * from products p "

	params := make([]interface{}, 0)
	sqlWhere := ""
	if len(filter.CategoryId) > 0 {
		sqlWhere += fmt.Sprintf(" AND p.product_category_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(filter.CategoryId)), ","))
		params = append(params, filter.CategoryId...)
	}

	if len(filter.SubCategoryId) > 0 {
		sqlWhere += fmt.Sprintf(" AND p.product_subcategory_fkid in (%s) ", strings.TrimRight(strings.Repeat("?,", len(filter.SubCategoryId)), ","))
		params = append(params, filter.SubCategoryId...)
	}

	if filter.AdminId > 0 {
		sqlWhere += " AND admin_fkid = ?"
		params = append(params, filter.AdminId)
	}

	if sqlWhere != "" {
		sql += " WHERE " + strings.TrimLeft(sqlWhere, " AND")
	}

	// fmt.Println(sql)
	return db.QueryArray(sql, params...)
}

func (p productRepository) FetchProductDetail(ids []int) ([]models.ProductDetailEntity, error) {
	sql := `SELECT * from products_detail where product_detail_id in @ids`
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	var result []models.ProductDetailEntity
	err := p.Query(sql, params...).Model(&result)
	return result, err
}
