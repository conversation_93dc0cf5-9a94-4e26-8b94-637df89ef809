package transform

import (
	"regexp"

	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func MapByKey(key string, data []map[string]interface{}) map[string]map[string]interface{} {
	result := map[string]map[string]interface{}{}
	for _, row := range data {
		result[cast.ToString(row[key])] = row
	}
	return result
}

func MergeMap(dataA map[string]interface{}, dataB map[string]interface{}) map[string]interface{} {
	for k, v := range dataB {
		dataA[k] = v
	}
	return dataA
}

func ToIntArray(data string) []int {
	result := make([]int, 0)
	keys := make(map[string]bool)
	ids := regexp.MustCompile(`[0-9]+`).FindAllString(data, -1)
	for _, id := range ids {
		if !keys[id] {
			result = append(result, cast.ToInt(id))
		}
		keys[id] = true
	}
	return result
}
