package http

import (
	"encoding/json"
	fasthttprouter "github.com/buaazp/fasthttprouter"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type stockCardHandler struct {
	uc domain.StockCardUseCase
}

func NewHttpStockCardHandler(app *fasthttprouter.Router, useCase domain.StockCardUseCase) {
	handler := &stockCardHandler{useCase}
	app.GET("/v1/stock-card", auth.ValidateToken(handler.FetchStockCard))
}

func (h stockCardHandler) FetchStockCard(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))

	timeStart := cast.ToInt64(string(ctx.QueryArgs().Peek("time_start")))
	timeEnd := cast.ToInt64(string(ctx.QueryArgs().Peek("time_end")))
	timeDiff := cast.ToInt64(string(ctx.QueryArgs().Peek("time_diff")))
	outletId := string(ctx.QueryArgs().Peek("outlet_id"))
	productId := string(ctx.QueryArgs().Peek("product_id"))
	variantId := string(ctx.QueryArgs().Peek("variant_id"))

	//fmt.Println(user, request, timeStart, timeEnd, outletId, productId, variantId)

	result, err := h.uc.FetchStockCard(adminId, timeStart, timeEnd, timeDiff, outletId, productId, variantId)
	if err != nil {
		return
	}

	var response interface{}
	if result == nil {
		response = []map[string]interface{}{}
	} else {
		response = result
	}

	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status":     true,
		"code":       200,
		"data":       response,
		"data_total": len(result),
	})
}
