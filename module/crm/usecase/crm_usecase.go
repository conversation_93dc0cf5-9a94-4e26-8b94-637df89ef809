package usecase

import (
	"fmt"
	"sort"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
	bqRepo "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type crmUseCase struct {
	repoPrimary crm.Repository
	repoReplica crm.Repository
}

func NewCrmUseCase(repository crm.Repository, repoReplica crm.Repository) crm.UseCase {
	return &crmUseCase{repoPrimary: repository, repoReplica: repoReplica}
}

func (c crmUseCase) FetchCustomerList(user domain.User, param domain.RequestParam) (interface{}, error) {
	log.Info("param : %v", cast.ToStringJson(param))

	//if limit not given, default to 100
	// if param.Length == 0 {
	// 	param.Length = 100
	// }

	var response interface{}
	var repo crm.Repository
	if bqRepo.GetMaxTimeSync() > 0 && param.DateEnd <= bqRepo.GetMaxTimeSync() {
		repo = c.repoReplica
	} else {
		repo = c.repoPrimary
	}

	data, err := repo.QueryParam(param).FetchCustomerList(user)

	if param.Format == domain.RequestFormatDatatable {
		response = formatRequest(data, param, func(p domain.RequestParam) ([]map[string]interface{}, error) {
			customerList, err := repo.QueryParam(p).FetchCustomerList(user)
			for _, row := range customerList {
				if receiptReceiver := cast.ToString(row["receipt_receiver"]); row["receipt_receiver"] == nil || receiptReceiver == "" || receiptReceiver == "null" {
					row["receipt_receiver"] = ""
				}
			}
			return customerList, err
		})
	} else {
		response = data
	}

	return response, err
}

func (c crmUseCase) FetchCustomerTransactionList(user domain.User, param domain.RequestParam) (interface{}, error) {
	log.Info("param : %v", cast.ToStringJson(param))
	//if limit not given, default to 100
	if param.Length == 0 {
		param.Length = 100
	}

	var response interface{}
	var repo crm.Repository
	if bqRepo.GetMaxTimeSync() > 0 {
		repo = c.repoReplica
	} else {
		repo = c.repoPrimary
	}

	data, err := repo.QueryParam(param).FetchCustomerTransactionList(user)
	response = formatRequest(data, param, func(p domain.RequestParam) ([]map[string]interface{}, error) {
		return repo.QueryParam(p).FetchCustomerList(user)
	})

	return response, err
}

func formatRequest(data []map[string]interface{}, param domain.RequestParam, fetchCount func(p domain.RequestParam) ([]map[string]interface{}, error)) interface{} {
	if param.Format == domain.RequestFormatDatatable {
		datatable := make(map[string]interface{})
		datatable["data"] = data
		datatable["recordsFiltered"] = len(data)
		datatable["recordsTotal"] = len(data)

		param.IsCount = true
		cnts, err := fetchCount(param)
		log.IfError(err)
		if len(cnts) > 0 {
			datatable["recordsFiltered"] = cnts[0]["cnt"]
			datatable["recordsTotal"] = cnts[0]["cnt"]
		}

		if param.HasFilter() {
			cnts, err = fetchCount(param.ResetSearch())
			log.IfError(err)
			if len(cnts) > 0 {
				datatable["recordsTotal"] = cnts[0]["cnt"]
			}
		}

		return datatable
	}

	return data
}

func (c crmUseCase) FetchMemberAcquisition(user domain.User, request domain.RequestParam) (interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmUseCase) FetchCohortAnalysis(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	if request.Offset > 0 {
		return nil, nil
	}

	validatePeriods := []string{"daily", "monthly", "weekly"}
	if !array.IsIn(request.Period, validatePeriods) {
		return nil, fmt.Errorf("invalid period")
	}

	repo := c.repoPrimary
	sales, err := repo.FetchSalesMember(user, request)
	if err != nil {
		return nil, err
	}

	members, err := repo.FetchMemberRegisterRange(cast.ToInt(user.BusinessId), request)
	if err != nil {
		return nil, err
	}

	fmt.Println("total sales: ", len(sales), " | total member:", len(members))

	// memberMap := array.FlatMapArray(members, "member_id")
	memberByRegisterDate := array.GroupBy(members, "register_date")

	result := make([]map[string]interface{}, 0)

	periods := generatePeriods(request.DateStart, request.DateEnd, int64(0), request.Period)
	periodIndex := array.ToMapIndex(periods)
	// fmt.Println("idx: ", periodIndex)

	//grab unique dates from member
	dates := array.FlatMapArray(members, "register_date")
	for _, date := range dates {
		regDate := cast.ToString(date["register_date"])
		memberFilter := memberByRegisterDate[regDate]
		cols := make(map[string]interface{})

		startIdx := periodIndex[regDate]
		for _, member := range memberFilter {
			for _, sale := range sales {
				if cast.ToString(sale["member_id"]) == cast.ToString(member["member_id"]) {
					// cols[cast.ToString(sale["date"])] = cast.ToInt(cols[cast.ToString(sale["date"])]) + 1
					period := periodIndex[cast.ToString(sale["date"])] - startIdx
					if period < 0 {
						log.Info(">>> ERROR <<<< minuesss... member: %v | regAt: %v | saleAt: %v | startIdx: %v | idx: %v", sale["member_id"], regDate, sale["date"], startIdx, periodIndex[cast.ToString(sale["date"])])
						continue
					}

					key := fmt.Sprintf("period_%d", period)
					cols[key] = cast.ToInt(cols[key]) + 1
				}
			}
			if len(cols) == 0 {
				log.Info("member: %v has no transaction", member["member_id"])
			}
		}

		result = append(result, map[string]interface{}{
			"date":        regDate,
			"date_millis": cast.ToInt64(memberFilter[0]["register_date_millis"]),
			"total_user":  len(memberFilter),
		})

		array.MergeMapInplace(result[len(result)-1], cols)
	}

	//filling empty periods
	for _, row := range result {
		startIdx := periodIndex[cast.ToString(row["date"])]
		for p := startIdx; p < len(periodIndex); p++ {
			key := fmt.Sprintf("period_%d", p-startIdx)
			if _, ok := row[key]; !ok {
				row[key] = 0
			}
		}
	}

	// transform keys
	if request.Period == "weekly" {
		dates := weekToDateRange(request.DateStart, request.DateEnd, int64(0))
		for _, row := range result {
			row["date"] = dates[cast.ToString(row["date"])]
		}
	}

	//sorting
	sort.SliceStable(result, func(i, j int) bool {
		return cast.ToInt64(result[i]["date_millis"]) < cast.ToInt64(result[j]["date_millis"])
	})

	// log.Info("\n---- result: %v \n", utils.SimplyToJson(result))
	return result, nil
}

func generatePeriods(startDate, endDate, timeOffset int64, period string) []string {
	fmt.Println("period: ", period, ", until: ", endDate)
	result := make([]string, 0)
	if period == "monthly" {
		timeStart := time.Unix(startDate/1000+timeOffset, 0)
		for i := timeStart.Unix() * 1000; i < endDate; i++ {
			result = append(result, timeStart.Format("01/2006"))
			fmt.Println(">>> ", timeStart.Format("01/02/2006"), "::", timeStart.Unix()*1000)
			timeStart = timeStart.AddDate(0, 1, 0)
			if timeStart.Unix()*1000 >= endDate {
				fmt.Println("stop...")
				break
			}
		}
		return result
	}

	if period == "daily" {
		timeStart := time.Unix(startDate/1000+timeOffset, 0)
		for i := timeStart.Unix() * 1000; i < endDate; i++ {
			result = append(result, timeStart.Format("02/01/2006"))
			fmt.Println(">>> ", timeStart.Format("02/01/2006"), "::", timeStart.Unix()*1000)
			timeStart = timeStart.AddDate(0, 0, 1)
			// fmt.Println("is after: ", timeStart.After(time.Unix(endDate/1000+timeOffset, 0)), ">>", timeStart)
			if timeStart.Unix()*1000 > endDate {
				fmt.Println("stop...")
				break
			}
		}
		return result
	}

	if period == "weekly" {
		timeStart := time.Unix(startDate/1000+timeOffset, 0)
		for i := timeStart.Unix() * 1000; i < endDate; i++ {
			y, w := timeStart.ISOWeek()
			result = append(result, fmt.Sprintf("%02d/%d", w, y))
			fmt.Println(">>> ", result[len(result)-1], "::", timeStart.Unix()*1000)
			timeStart = timeStart.AddDate(0, 0, 7)
			// fmt.Println("is after: ", timeStart.After(time.Unix(endDate/1000+timeOffset, 0)), ">>", timeStart)
			if timeStart.Unix()*1000 > endDate {
				fmt.Println("stop...")
				break
			}
		}
	}

	return result
}

func weekToDateRange(startDate, endDate, timeOffset int64) map[string]string {
	result := make(map[string]string)
	timeStart := time.Unix(startDate/1000+timeOffset, 0)
	lastDate := time.Unix(endDate/1000+timeOffset, 0).Format("02/01/2006")
	lastWeek := 0
	dateRangeStart := ""
	for i := timeStart.Unix() * 1000; i < endDate; i++ {
		_, w := timeStart.ISOWeek()
		if w != lastWeek || timeStart.Format("02/01/2006") == lastDate {
			if lastWeek > 0 {
				prefDay := timeStart.AddDate(0, 0, -1)
				result[fmt.Sprintf("%02d/%s", lastWeek, prefDay.Format("2006"))] = fmt.Sprintf("%s - %s", dateRangeStart, prefDay.Format("02/01/2006"))
			}

			//reset
			dateRangeStart = timeStart.Format("02/01/2006")
			lastWeek = w
		}

		timeStart = timeStart.AddDate(0, 0, 1)
		if timeStart.Unix()*1000 > endDate {
			fmt.Println("stop...")
			break
		}
	}
	return result
}
