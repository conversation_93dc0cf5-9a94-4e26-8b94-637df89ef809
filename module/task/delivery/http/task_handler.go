package http

import (
	"encoding/json"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/exception"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type taskHandler struct {
	uc domain.TaskUseCase
}

func NewHttpTaskHandler(app *fasthttprouter.Router, useCase domain.TaskUseCase) {
	handler := &taskHandler{useCase}

	app.GET("/v1/task/performance", auth.ValidateToken(handler.FetchReportPerformance))
	app.GET("/v1/task/performance-by-workflow", auth.ValidateToken(handler.FetchReportPerformanceByWorkflow))

	app.GET("/v1/task/time-analysis", auth.ValidateToken(handler.FetchReportTimeAnalysis))
}

func (h *taskHandler) FetchReportPerformance(ctx *fasthttp.RequestCtx) {
	param := &domain.TaskReportRequest{
		User:       domain.GetUserSessionOfFastHttp(ctx),
		CategoryId: cast.ToInt(ctx.QueryArgs().Peek("category_id")),
		StartDate:  cast.ToInt64(ctx.QueryArgs().Peek("start_date")),
		EndDate:    cast.ToInt64(ctx.QueryArgs().Peek("end_date")),
	}
	result, err := h.uc.FetchReportPerformance(param)
	if err != nil {
		handleError(err, ctx)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *taskHandler) FetchReportPerformanceByWorkflow(ctx *fasthttp.RequestCtx) {
	param := &domain.TaskReportRequest{
		User:       domain.GetUserSessionOfFastHttp(ctx),
		CategoryId: cast.ToInt(ctx.QueryArgs().Peek("category_id")),
		StartDate:  cast.ToInt64(ctx.QueryArgs().Peek("start_date")),
		EndDate:    cast.ToInt64(ctx.QueryArgs().Peek("end_date")),
		WorkflowId: cast.ToInt(ctx.QueryArgs().Peek("workflow_id")),
	}
	result, err := h.uc.FetchReportPerformanceByWorkflow(param)
	if err != nil {
		handleError(err, ctx)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func (h *taskHandler) FetchReportTimeAnalysis(ctx *fasthttp.RequestCtx) {
	param := &domain.TaskReportRequest{
		User:       domain.GetUserSessionOfFastHttp(ctx),
		CategoryId: cast.ToInt(ctx.QueryArgs().Peek("category_id")),
		StartDate:  cast.ToInt64(ctx.QueryArgs().Peek("start_date")),
		EndDate:    cast.ToInt64(ctx.QueryArgs().Peek("end_date")),
	}

	result, err := h.uc.FetchReportTimeAnalysis(param)
	if err != nil {
		handleError(err, ctx)
		return
	}
	_ = json.NewEncoder(ctx).Encode(result)
}

func handleError(err error, ctx *fasthttp.RequestCtx) {
	ctx.SetStatusCode(fasthttp.StatusInternalServerError)
	var data interface{}
	if validationErr, ok := err.(*exception.ValidationError); ok {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		data = validationErr.Data
	}
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"message": err.Error(),
		"data":    data,
	})
}
