package repository

import (
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type salesRepository struct {
	repoPrimary domain.SalesRepositoryPrimary
	repoReplica domain.SalesRepositoryReplica
}

func NewSalesRepositoryMiddleware(repoPrimary domain.SalesRepositoryPrimary, repoReplica domain.SalesRepositoryReplica) domain.SalesRepositoryMiddleware {
	return salesRepository{repoPrimary: repoPrimary, repoReplica: repoReplica}
}

func (s salesRepository) FetchSalesAnalysisOutletByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisOutletByNominal(request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisOutletByNominal(request, adminId)
	}
}

func (s salesRepository) FetchSalesAnalysisOutletByQty(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisOutletByQty(request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisOutletByQty(request, adminId)
	}
}

func (s salesRepository) FetchSalesAnalysisDayByNominal(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisDayByNominal(request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisDayByNominal(request, adminId)
	}
}

func (s salesRepository) FetchSalesAnalysisDayByQty(request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisDayByQty(request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisDayByQty(request, adminId)
	}
}

func (s salesRepository) FetchSalesByMedia(user domain.User, request domain.SalesReportMediaRequest) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesByMedia(user, request)
	} else {
		return s.repoReplica.FetchSalesByMedia(user, request)
	}
}

func (s salesRepository) FetchSalesAnalysisByNominal(groupBy string, request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisByNominal(groupBy, request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisByNominal(groupBy, request, adminId)
	}
}

func (s salesRepository) FetchSalesAnalysisByQty(groupBy string, request domain.DataTableRequest, adminId int) ([]map[string]interface{}, error) {
	if request.EndDate > repository.GetMaxTimeSync() {
		return s.repoPrimary.FetchSalesAnalysisByQty(groupBy, request, adminId)
	} else {
		return s.repoReplica.FetchSalesAnalysisByQty(groupBy, request, adminId)
	}
}
