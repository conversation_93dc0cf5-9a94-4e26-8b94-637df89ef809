package hpp

import "gitlab.com/uniqdev/backend/api-report/domain"

type UseCase interface {
	HandleNewStockInOut(subscription domain.HppSubscription)
	FetchPurchaseConfirmId(purchaseProductId int) (int, error)
	CheckAnomalyHpp()
	// HandlePurchaseRetur(purchaseProductId, qty int)
	Test()
	// HandleStockOpname(opnameIds []int)

	//simulation
	SimulateFifoAdjusment(hpps []domain.Hpp) (domain.HppSimulationResponse, error)
	FetchStockPrice(param *domain.StockPriceRequest) ([]domain.StockPriceResponse, error)
	UpdateStockPrice(param *domain.StockPriceUpdateRequest) error

	UpdateStockPriceQtyIn(id, source string, qty float32) error
	AdjustStockPrice(productId int, dateStart int64) error
	FixWrongFifo(productIds ...int)
}
