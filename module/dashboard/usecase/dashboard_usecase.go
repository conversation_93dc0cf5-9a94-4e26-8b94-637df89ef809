package usecase

import (
	"fmt"
	"sort"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/parser"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type dashboardUseCase struct {
	repo      domain.DashboardRepository
	repoSales domain.SalesRepositoryPrimary
}

func NewDashboardUseCase(repository domain.DashboardRepository, sales domain.SalesRepositoryPrimary) domain.DashboardUseCase {
	return &dashboardUseCase{repository, sales}
}

func (d *dashboardUseCase) FetchCashFlow(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error) {
	outletIdMap := map[string]interface{}{}

	// net sales
	chanSales := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {
			query, _ = d.repo.FetchNetSalesByShift(adminId, timeStart, timeEnd, outletIds)
		} else {
			query, _ = d.repo.FetchNetSalesBySales(adminId, timeStart, timeEnd, outletIds)
		}
		fmt.Println("net sales: ", len(query))
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
			dataRender[id] = data["sales"]
		}
		chanSales <- dataRender
	}()

	// purchase
	chanPurchaseLunas := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, _ = d.repo.FetchPaidPurchaseBySales(adminId, timeStart, timeEnd, outletIds)
		}

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_grandtotal"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseLunas <- dataRender
	}()

	// purchase dengan hutang (pakai DP / tanpa DP)
	chanPurchaseHutang := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, _ = d.repo.FetchUnPaidPurchase(adminId, timeStart, timeEnd, outletIds)
		}
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_uangmuka"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseHutang <- dataRender
	}()

	// pembayaran hutang
	chanDebtPayment := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, _ = d.repo.FetchDebtPayment(adminId, timeStart, timeEnd, outletIds)
		}

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = cast.ToInt64(dataRender[id]) + cast.ToInt64(data["debt"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanDebtPayment <- dataRender
	}()

	// hutang
	chanDebt := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, _ = d.repo.FetchDebt(adminId, 0, timeEnd, outletIds)
		}

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			debt := cast.ToInt64(0)
			if cast.ToInt64(data["hutang"]) > cast.ToInt64(data["hutang_dibayar"]) {
				debt = cast.ToInt64(data["hutang"]) - cast.ToInt64(data["hutang_dibayar"])
			}

			if dataRender[id] != nil {
				q1 := cast.ToStringMap(dataRender[id])
				debt += cast.ToInt64(q1["debt"])
			}

			//render map
			dataRender[id] = debt

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanDebt <- dataRender
	}()

	//hasil merge id
	sales := <-chanSales
	purchaseLunas := <-chanPurchaseLunas
	purchaseHutang := <-chanPurchaseHutang
	debtPayment := <-chanDebtPayment
	debt := <-chanDebt

	//render data
	//render outletlist
	dataRender := make([]map[string]interface{}, 0)
	for index, data := range outletIdMap {
		dataSales := sales[index]
		if dataSales == nil {
			dataSales = 0
		}
		dataPurchaseLunas := purchaseLunas[index]
		if dataPurchaseLunas == nil {
			dataPurchaseLunas = 0
		}
		dataPurchaseHutang := purchaseHutang[index]
		if dataPurchaseHutang == nil {
			dataPurchaseHutang = 0
		}
		dataDebtPayment := debtPayment[index]
		if dataDebtPayment == nil {
			dataDebtPayment = 0
		}
		dataDebt := debt[index]
		if dataDebt == nil {
			dataDebt = 0
		}

		// countable
		purchase := cast.ToInt64(dataPurchaseLunas) + cast.ToInt64(dataPurchaseHutang) + cast.ToInt64(dataDebtPayment)
		cashflow := cast.ToInt64(dataSales) - purchase

		//render
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail": map[string]interface{}{
				"sales":    dataSales,
				"purchase": purchase,
				"cashflow": cashflow,
				"debt":     dataDebt,
			},
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender, nil
}

func (d *dashboardUseCase) FetchEntertainIncome(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error) {
	outletIdMap := make(map[string]interface{})
	var err error

	//get data
	chanCompliment := make(chan map[string]interface{})
	//go func() {
	//	var query []map[string]interface{}
	//	if dateOption == "by_shift" {
	//
	//	} else {
	//		query, err = d.repo.FetchComplimentBySales(adminId, timeStart, timeEnd, outletIds)
	//	}
	//	dataRender := map[string]interface{}{}
	//	for _, data := range query {
	//		id := utils.ToString(data["outlet_id"])
	//
	//		//append outlet
	//		outletIdMap[id] = data["outlet_name"]
	//
	//		//set map
	//		dataRender[id] = map[string]interface{}{
	//			"outlet_id":   data["outlet_id"],
	//			"outlet_name": data["outlet_name"],
	//			"compliment":  data["sum_grandtotal"],
	//		}
	//	}
	//	chanCompliment <- dataRender
	//}()

	chanDutyMeals := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchTotalSalesPerPaymentBySales(adminId, timeStart, timeEnd, outletIds, "DUTY MEALS", "COMPLIMENT")
		}

		dataRenderDutyMeals := make(map[string]interface{})
		dataRenderCompliment := make(map[string]interface{})

		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			if cast.ToString(data["payment"]) == "DUTY MEALS" {
				dataRenderDutyMeals[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
					"dutymeals":   data["sum_grandtotal"],
				}
			} else if cast.ToString(data["payment"]) == "COMPLIMENT" {
				dataRenderCompliment[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
					"compliment":  data["sum_grandtotal"],
				}
			}
		}

		fmt.Println("\nduty: ", len(dataRenderDutyMeals), "comp:", len(dataRenderCompliment))
		chanCompliment <- dataRenderCompliment
		chanDutyMeals <- dataRenderDutyMeals
	}()

	chanFree := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchFreeBySales(adminId, timeStart, timeEnd, outletIds)
		}
		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"free":        data["free"],
			}
		}
		chanFree <- dataRender
	}()

	chanDiscountReceipt := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchDiscSalesBySales(adminId, timeStart, timeEnd, outletIds)
		}
		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":        data["outlet_id"],
				"outlet_name":      data["outlet_name"],
				"discount_receipt": data["sales_discount"],
			}
		}
		chanDiscountReceipt <- dataRender
	}()

	chanDiscountItem := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchDiscItemBySales(adminId, timeStart, timeEnd, outletIds)
		}

		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":     data["outlet_id"],
				"outlet_name":   data["outlet_name"],
				"discount_item": data["discount_item"],
			}
		}
		chanDiscountItem <- dataRender
	}()

	chanDiscountGratuity := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchDiscGratuityBySales(adminId, timeStart, timeEnd, outletIds)
		}

		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":         data["outlet_id"],
				"outlet_name":       data["outlet_name"],
				"discount_gratuity": data["sales_tax_discount"],
			}
		}
		chanDiscountGratuity <- dataRender
	}()

	chanVoucher := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchVoucherBySales(adminId, timeStart, timeEnd, outletIds)
		}

		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"voucher":     data["sales_tax_voucher"],
			}
		}
		chanVoucher <- dataRender
	}()

	chanVoucherReceipt := make(chan map[string]interface{})
	go func() {
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchVoucherSalesBySales(adminId, timeStart, timeEnd, outletIds)
		}
		dataRender := make(map[string]interface{})
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":       data["outlet_id"],
				"outlet_name":     data["outlet_name"],
				"voucher_receipt": data["sales_voucher"],
			}
		}
		chanVoucherReceipt <- dataRender
	}()

	compliment := <-chanCompliment
	dutymeals := <-chanDutyMeals
	free := <-chanFree
	discountReceipt := <-chanDiscountReceipt
	discountItem := <-chanDiscountItem
	discountGratuity := <-chanDiscountGratuity
	voucher := <-chanVoucher
	voucherReceipt := <-chanVoucherReceipt

	fmt.Println(compliment)

	dataRender := make([]map[string]interface{}, 0)
	for k, v := range outletIdMap {
		dataCompliment := compliment[k]
		if dataCompliment == nil {
			dataCompliment = 0
		} else {
			dataCompliment = dataCompliment.(map[string]interface{})["compliment"]
		}
		dataDutyMeals := dutymeals[k]
		if dataDutyMeals == nil {
			dataDutyMeals = 0
		} else {
			dataDutyMeals = dataDutyMeals.(map[string]interface{})["dutymeals"]
		}
		dataFree := free[k]
		if dataFree == nil {
			dataFree = 0
		} else {
			dataFree = dataFree.(map[string]interface{})["free"]
		}
		dataVoucher := voucher[k]
		if dataVoucher == nil {
			dataVoucher = 0
		} else {
			dataVoucher = dataVoucher.(map[string]interface{})["voucher"]
		}
		dataVoucherReceipt := voucherReceipt[k]
		if dataVoucherReceipt == nil {
			dataVoucherReceipt = 0
		} else {
			dataVoucherReceipt = dataVoucherReceipt.(map[string]interface{})["voucher_receipt"]
		}
		dataDiscountReceipt := discountReceipt[k]
		if dataDiscountReceipt == nil {
			dataDiscountReceipt = 0
		} else {
			dataDiscountReceipt = dataDiscountReceipt.(map[string]interface{})["discount_receipt"]
		}
		dataDiscountItem := discountItem[k]
		if dataDiscountItem == nil {
			dataDiscountItem = 0
		} else {
			dataDiscountItem = dataDiscountItem.(map[string]interface{})["discount_item"]
		}
		dataDiscountGratuity := discountGratuity[k]
		if dataDiscountGratuity == nil {
			dataDiscountGratuity = 0
		} else {
			dataDiscountGratuity = dataDiscountGratuity.(map[string]interface{})["discount_gratuity"]
		}

		dataDiscountAll := cast.ToInt64(dataDiscountReceipt) + cast.ToInt64(dataDiscountItem) + cast.ToInt64(dataDiscountGratuity)
		dataVoucherAll := cast.ToInt64(dataVoucher) + cast.ToInt64(dataVoucherReceipt)

		dataRender = append(dataRender, map[string]interface{}{
			"outlet_id":   k,
			"outlet_name": v,
			"detail": map[string]interface{}{
				"compliment": dataCompliment,
				"dutymeals":  dataDutyMeals,
				"free":       dataFree,
				"voucher":    dataVoucherAll,
				"discount":   dataDiscountAll,
			},
		})
	}

	return dataRender, err

}

func (d *dashboardUseCase) FetchSalesTransactionV2(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error) {
	outletIdMap := make(map[string]interface{})
	var err error

	chanSales := make(chan map[string]interface{})
	go func() {
		startTime := time.Now()
		dataRender := map[string]interface{}{}
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchTotalSalesBySales(adminId, timeStart, timeEnd, outletIds)
		}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}

			//render map
			dataRender[id] = map[string]interface{}{
				"total_customer":    data["total_customer"],
				"total_income":      data["total_income"],
				"total_transaction": data["total_transaction"],
			}
		}
		log.Info("#FetchSalesTransaction, FetchTotalSalesBySales took %v", time.Since(startTime))
		chanSales <- dataRender
	}()

	chanPendingIncome := make(chan map[string]interface{})
	go func() {
		startTime := time.Now()
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchPendingIncomeBySales(adminId, timeStart, timeEnd, outletIds)
		}

		//query := getPendingIncome(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = data["pending_income"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		log.Info("#FetchSalesTransaction, FetchPendingIncomeBySales took %v", time.Since(startTime))
		chanPendingIncome <- dataRender
	}()

	// Set a timeout duration
	timeout := time.After(50 * time.Second) // Adjust the duration as needed

	// sales := <-chanSales
	// pendingIncome := <-chanPendingIncome
	sales := make(map[string]interface{}, 0)
	pendingIncome := make(map[string]interface{}, 0)

	select {
	case pendingIncome = <-chanPendingIncome:
		sales = <-chanSales
		// Handle sales and pendingIncome
	case <-timeout:
		// Handle timeout
		log.Info("#FetchSalesTransaction, timeout... size sales: %v, size pending: %v", len(sales), len(pendingIncome))
		// You can return an error or set pendingIncome to nil, etc.
	}

	//render outletlist
	dataRender := make([]map[string]interface{}, 0)
	for index, data := range outletIdMap {
		dataPendingIncome := pendingIncome[index]
		if dataPendingIncome == nil {
			dataPendingIncome = 0
		}

		salesOutlet := cast.ToStringMap(sales[index])

		//perhitungan akhir
		allTotalIncome := cast.ToInt64(salesOutlet["total_income"])
		allTotalTransaction := cast.ToInt64(salesOutlet["total_transaction"])
		allTotalCustomer := cast.ToInt64(salesOutlet["total_customer"])
		avgIncome := cast.ToInt64(0)
		if allTotalTransaction != 0 {
			avgIncome = allTotalIncome / allTotalTransaction
		}
		avgIncomeCustomer := cast.ToInt64(0)
		if allTotalCustomer != 0 {
			avgIncomeCustomer = allTotalIncome / allTotalCustomer
		}

		//render format data
		mergeMap := make(map[string]interface{})
		mergeMap["average_income"] = avgIncome
		mergeMap["average_income_customer"] = avgIncomeCustomer
		mergeMap["total_customer"] = allTotalCustomer
		mergeMap["total_income"] = allTotalIncome
		mergeMap["total_transaction"] = allTotalTransaction
		mergeMap["pendingIncome"] = dataPendingIncome

		//render data per-outlet
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail":      mergeMap,
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender, err
}

func (d *dashboardUseCase) FetchSalesTransaction(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error) {
	outletIdMap := make(map[string]interface{})
	var err error

	//if timeStart greater than now, reset to now
	//this to make sure the data is uptodate (in case caching is implementing)
	if timeEnd > time.Now().UnixMilli() {
		timeEnd = time.Now().UnixMilli()
		log.Info("FetchSalesTransaction, timeEnd > time.Now().UnixMilli(), reset to now")
	}

	chanSales := make(chan map[string]interface{})
	go func() {
		startTime := time.Now()
		dataRender := map[string]interface{}{}
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchTotalSalesBySales(adminId, timeStart, timeEnd, outletIds)
		}
		for _, data := range query {
			id := utils.ToString(cast.ToInt(data["outlet_id"]))

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}

			//render map
			dataRender[id] = map[string]interface{}{
				"total_customer":    data["total_customer"],
				"total_income":      data["total_income"],
				"total_transaction": data["total_transaction"],
			}
		}
		log.Info("#FetchSalesTransaction, FetchTotalSalesBySales took %v", time.Since(startTime))
		chanSales <- dataRender
	}()

	chanPendingIncome := make(chan map[string]interface{})
	go func() {
		startTime := time.Now()
		var query []map[string]interface{}
		if dateOption == "by_shift" {

		} else {
			query, err = d.repo.FetchPendingIncomeBySales(adminId, timeStart, timeEnd, outletIds)
		}

		//query := getPendingIncome(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(cast.ToInt(data["outlet_id"]))

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = data["pending_income"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		log.Info("#FetchSalesTransaction, FetchPendingIncomeBySales took %v", time.Since(startTime))
		chanPendingIncome <- dataRender
	}()

	// Set a timeout duration
	timeout := time.After(50 * time.Second) // Adjust the duration as needed

	// sales := <-chanSales
	// pendingIncome := <-chanPendingIncome
	sales := make(map[string]interface{}, 0)
	pendingIncome := make(map[string]interface{}, 0)

	select {
	case pendingIncome = <-chanPendingIncome:
		sales = <-chanSales
		// Handle sales and pendingIncome
	case <-timeout:
		// Handle timeout
		log.Info("#FetchSalesTransaction, timeout... size sales: %v, size pending: %v", len(sales), len(pendingIncome))
		// You can return an error or set pendingIncome to nil, etc.
	}

	//render outletlist
	dataRender := make([]map[string]interface{}, 0)
	for index, data := range outletIdMap {
		dataPendingIncome := pendingIncome[index]
		if dataPendingIncome == nil {
			dataPendingIncome = 0
		}

		salesOutlet := cast.ToStringMap(sales[index])

		//perhitungan akhir
		allTotalIncome := cast.ToInt64(salesOutlet["total_income"])
		allTotalTransaction := cast.ToInt64(salesOutlet["total_transaction"])
		allTotalCustomer := cast.ToInt64(salesOutlet["total_customer"])
		avgIncome := cast.ToInt64(0)
		if allTotalTransaction != 0 {
			avgIncome = allTotalIncome / allTotalTransaction
		}
		avgIncomeCustomer := cast.ToInt64(0)
		if allTotalCustomer != 0 {
			avgIncomeCustomer = allTotalIncome / allTotalCustomer
		}

		//render format data
		mergeMap := make(map[string]interface{})
		mergeMap["average_income"] = avgIncome
		mergeMap["average_income_customer"] = avgIncomeCustomer
		mergeMap["total_customer"] = allTotalCustomer
		mergeMap["total_income"] = allTotalIncome
		mergeMap["total_transaction"] = allTotalTransaction
		mergeMap["pendingIncome"] = dataPendingIncome

		//render data per-outlet
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail":      mergeMap,
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender, err
}

func (d *dashboardUseCase) FetchItemSalesGroup(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) (map[string]interface{}, error) {
	chanSalesByType := make(chan []map[string]interface{})
	go func() {
		query, err := d.repo.FetchItemSalesGroupType(adminId, timeStart, timeEnd, outletIds)
		utils.CheckErr(err)
		chanSalesByType <- query
	}()

	chanSalesByCategory := make(chan []map[string]interface{})
	go func() {
		query, err := d.repo.FetchItemSalesGroupCategory(adminId, timeStart, timeEnd, outletIds)
		utils.CheckErr(err)
		chanSalesByCategory <- query
	}()

	chanSalesBySubCategory := make(chan []map[string]interface{})
	go func() {
		query, err := d.repo.FetchItemSalesGroupSubCategory(adminId, timeStart, timeEnd, outletIds)
		utils.CheckErr(err)
		chanSalesBySubCategory <- query
	}()

	dataSalesType := <-chanSalesByType
	dataSalesCategory := <-chanSalesByCategory
	dataSalesSubCategory := <-chanSalesBySubCategory

	response := map[string]interface{}{
		"type":        dataSalesType,
		"category":    dataSalesCategory,
		"subcategory": dataSalesSubCategory,
	}
	return response, nil
}

func (d *dashboardUseCase) FetchSalesByMedia(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	data, err := d.repo.FetchSalesByMedia(param, user)
	if err != nil {
		return nil, err
	}

	bankIds := array.GetKeys(array.FlatMapArray(data, "bank_fkid"))
	banks, err := d.repo.FetchBankById(bankIds)
	log.IfError(err)

	fmt.Println("banks: ", len(banks), bankIds)
	bankMap := array.FlatMapArray(banks, "bank_id")
	for i, raw := range data {
		if raw["bank_fkid"] != nil && cast.ToString(raw["bank_fkid"]) != "" {
			data[i]["method"] = bankMap[cast.ToString(raw["bank_fkid"])]["name"]
		}
	}

	return data, err
}

func (d *dashboardUseCase) FetchSalesAnalysis(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	return []map[string]interface{}{}, nil
}

// FetchTopSales implements domain.DashboardUseCase.
func (d *dashboardUseCase) FetchTopSales(param domain.DashboardRequest, limit int, user domain.User) (map[string]interface{}, error) {
	log.Info("FetchTopSales param %v | user: %v", utils.SimplyToJson(param), utils.SimplyToJson(user))

	//adjust time start based on user role
	param.TimeStart = utils.Privilege{Role: user.Role, TimeDiff: cast.ToInt64(param.TimeDiff)}.DashboardDateMin(param.TimeStart)
	param.OutletIds = utils.FilterAccessibleOutlets(param.OutletIds, user.OutletAccess)
	if len(param.OutletIds) == 0 && user.UserType != domain.UserTypeAdmin {
		param.OutletIds = cast.ToStringSlice(user.OutletAccess)
	}

	period := parser.CalculatePeriods((param.TimeDiff), (param.TimeStart), (param.TimeEnd))
	if millisNow := time.Now().Unix() * 1000; param.TimeEnd > millisNow { //this will affect the cache key
		param.TimeEnd = millisNow
	}
	log.Info("[modified] FetchTopSales param %v", utils.SimplyToJson(param))

	topItemCurrentPeriodChan := make(chan []map[string]interface{})
	topItemPrevPeriodChan := make(chan []map[string]interface{})

	//fetch for current period
	go func(dataChan chan []map[string]interface{}, param domain.DashboardRequest, user domain.User) {
		result, err := d.repo.FetchTopSales(param, user)
		log.IfError(err)
		dataChan <- result
	}(topItemCurrentPeriodChan, param, user)

	//fetch for previous period
	prevParam := domain.DashboardRequest{
		TimeStart: period.PreviousPeriodStartMillis,
		TimeEnd:   period.PreviousPeriodEndMillis,
		OutletIds: param.OutletIds,
	}

	go func(dataChan chan []map[string]interface{}, param domain.DashboardRequest, user domain.User) {
		result, err := d.repo.FetchTopSales(prevParam, user)
		log.IfError(err)
		dataChan <- result
	}(topItemPrevPeriodChan, prevParam, user)

	topItemCurrentPeriod := <-topItemCurrentPeriodChan
	topItemPrevPeriod := <-topItemPrevPeriodChan

	fmt.Println(len(topItemCurrentPeriod), len(topItemPrevPeriod))
	if len(topItemCurrentPeriod) == 0 {
		return map[string]interface{}{}, nil
	}

	//map prev period
	topItemPrevPeriodMap := array.FlatMapArray(topItemPrevPeriod, "product_fkid")
	productIdMap := make(map[interface{}]bool)

	//sort desc (by qty)
	sort.Slice(topItemCurrentPeriod, func(i, j int) bool {
		return cast.ToInt(topItemCurrentPeriod[i]["total_qty"]) > cast.ToInt(topItemCurrentPeriod[j]["total_qty"])
	})

	topByQty := make([]map[string]interface{}, limit)
	for i := 0; i < limit && i < len(topItemCurrentPeriod); i++ {
		item := topItemCurrentPeriod[i]
		productIdMap[item["product_fkid"]] = true
		topByQty[i] = map[string]interface{}{
			"item_id": item["product_fkid"],
			"now":     item["total_qty"],
			"prev":    cast.ToInt(topItemPrevPeriodMap[cast.ToString(item["product_fkid"])]["total_qty"]),
		}
	}

	//get top by nominal
	sort.Slice(topItemCurrentPeriod, func(i, j int) bool {
		return cast.ToInt(topItemCurrentPeriod[i]["total_subtotal"]) > cast.ToInt(topItemCurrentPeriod[j]["total_subtotal"])
	})

	topByNominal := make([]map[string]interface{}, limit)
	for i := 0; i < limit && i < len(topItemCurrentPeriod); i++ {
		item := topItemCurrentPeriod[i]
		productIdMap[item["product_fkid"]] = true
		topByNominal[i] = map[string]interface{}{
			"item_id": item["product_fkid"],
			"now":     item["total_subtotal"],
			"prev":    cast.ToInt(topItemPrevPeriodMap[cast.ToString(item["product_fkid"])]["total_subtotal"]),
		}
	}

	//fetch product
	products, err := d.repoSales.FetchProductByIds(array.GetKeys(productIdMap)...)
	log.IfError(err)
	log.Info("productIds: %v | got: %v | %v", len(productIdMap), len(products), productIdMap)

	productMap := array.FlatMapArray(products, "product_id")
	for i, item := range topByQty {
		topByQty[i]["item_name"] = utils.GetSafe(productMap[cast.ToString(item["item_id"])], "name")
	}
	for i, item := range topByNominal {
		topByNominal[i]["item_name"] = utils.GetSafe(productMap[cast.ToString(item["item_id"])], "name")
	}

	return map[string]interface{}{
		"legend": map[string]string{
			"now":  period.CurrentPeriod,
			"prev": period.PreviousPeriod,
		},
		"result": map[string]interface{}{
			"qty":     topByQty,
			"nominal": topByNominal,
		},
	}, nil
}
