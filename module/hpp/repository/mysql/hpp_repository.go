package mysql

import (
	"database/sql"
	"fmt"
	"runtime"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
)

type hppRepository struct {
	db     mysql.Repository
	filter *domain.StockPriceFilter
}

func (h hppRepository) Filter(filter domain.StockPriceFilter) hpp.Repository {
	return &hppRepository{db: h.db, filter: &filter}
}

func NewMysqlHppRepository(db *sql.DB) hpp.Repository {
	return &hppRepository{db: mysql.Repository{Conn: db}}
}

func (h hppRepository) InsertStockPrice(hpp domain.Hpp) (int64, error) {
	// if hpp.StockOut == 0 && hpp.StockIn == 0 && (hpp.StockInSource != "" && hpp.StockInSource != domain.StockOpname.String()) {
	// 	return 0, fmt.Errorf("can not insert stockOut and stockIn 0, %v", utils.SimplyToJson(hpp))
	// }

	if hpp.StockInSource != "" && hpp.StockInId == "" {
		log.IfError(fmt.Errorf("invalid adding stock_price: %v", utils.SimplyToJson(hpp)))
	}

	//@experimental: if not stock in from stock opname, check whether it has been recorded before
	//this is incase hpp should run when user has been using UNIQ for a while
	if hpp.StockInSource != domain.StockOpname.String() {
		record, err := db.Query("SELECT stock_price_id from stock_price where product_detail_fkid=? limit 1", hpp.ProductDetailFkid)
		log.IfError(err)
		if err == nil && len(record) == 0 {
			log.Info("skip insert to stock_price: %v", hpp)
			return 0, fmt.Errorf("product %v not yet recorded", hpp.ProductDetailFkid)
		}
	}

	var id int64
	err := db.WithTransaction(func(tx db.Transaction) error {
		resp := tx.Insert("stock_price", hpp.ToMap())
		id, _ = resp.LastInsertId()

		if hpp.StockOut > 0 && hpp.StockOutId != "" {
			createdAt := hpp.CreatedAt
			if createdAt == 0 {
				createdAt = utils.CurrentMillis()
			}
			tx.Insert("stock_price_detail", map[string]interface{}{
				"stock_price_fkid ": id,
				"stock_out":         hpp.StockOut,
				"stock_out_id":      hpp.StockOutId,
				"stock_out_source":  hpp.StockOutSource,
				"created_at":        createdAt,
				"updated_at":        utils.CurrentMillis(),
			})
		}
		return nil
	})
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v error adding stockPrice %v \n", fn, line, err)
		log.Info("failed add this: %v", hpp)
	}
	return id, err
}

func (h hppRepository) InsertStockOutDetail(out domain.StockPriceDetail) error {
	if out.Qty <= 0 {
		fmt.Println(">> skip InsertStockOutDetail, qty: ", out.Qty, ", stockPriceId ", out.StockPriceId)
		return nil
	}

	stockPriceData := out.ToMap()
	if cast.ToInt64(stockPriceData["updated_at"]) == 0 {
		stockPriceData["updated_at"] = utils.CurrentMillis()
	}

	var id int64
	err := db.WithTransaction(func(tx db.Transaction) error {
		res := tx.Insert("stock_price_detail", stockPriceData)
		tx.Exec("update stock_price set stock_out = stock_out + ?, updated_at = ? where stock_price_id = ?", out.Qty, utils.CurrentMillis(), out.StockPriceId)
		log.Info("update stockOut stockPrice id: %v, added qty: %v", out.StockPriceId, out.Qty)
		id, _ = res.LastInsertId()
		return nil
	})

	if err == nil {
		_, fn, line, _ := runtime.Caller(1)
		currStockPrice, _ := db.Query("select stock_out, stock_in from stock_price where stock_price_id = ?", out.StockPriceId)
		log.Info("[%v:%v] insertStockPriceDetail, id %v, lastDetailId %v, qty %v, stockPriceNow: %v", fn, line, out.StockPriceId, id, out.Qty, currStockPrice)
		if cast.ToFloat32(currStockPrice["stock_out"]) > cast.ToFloat32(currStockPrice["stock_in"]) && cast.ToFloat32(currStockPrice["stock_in"]) > 0 {
			log.IfError(fmt.Errorf("stock_out greater than stockIn, id %v, stockPrice: %v", out.StockPriceId, currStockPrice))
		}
	}
	return err
}

func (h hppRepository) UpdateStockPrice(stockPriceId int, data map[string]interface{}) error {
	_, fn, line, _ := runtime.Caller(1)
	if cast.ToFloat32(data["stock_out"]) < 0 {
		log.IfError(fmt.Errorf("%v:%v update stockOut in stockPrice to minus: %v", fn, line, data["stock_out"]))
	}
	res, err := db.Update("stock_price", data, "stock_price_id = ?", stockPriceId)
	if err == nil {
		rows, _ := res.RowsAffected()
		log.Info("%v:%v update stock price, id: %v, data: %v, updated rows: %v", fn, line, stockPriceId, data, rows)
	}
	return err
}

func (h hppRepository) UpdateStockPriceQtyIn(stockPriceId int, qtyIn float32) error {
	_, err := db.Update("stock_price", map[string]interface{}{
		"stock_in": qtyIn,
	}, "stock_price_id = ?", stockPriceId)
	return err
}

func (h hppRepository) FetchHppByProductId(productDetailId int) ([]domain.Hpp, error) {
	//sql := "select * from "
	return nil, nil
}

// FetchStockOutHpp fetch available HPP to use
func (h hppRepository) FetchStockOutHpp(productDetailId ...int) ([]domain.Hpp, error) {
	if len(productDetailId) == 0 {
		fmt.Println("FetchStockOutHpp return empty, no productDetailId passed")
		return []domain.Hpp{}, nil
	}
	// 	sql := `SELECT * FROM stock_price
	// WHERE
	// 	product_detail_fkid in @productIds
	// 	and (stock_in_id is null  or stock_in_id = ''
	// 		or stock_out < stock_in )
	//     and deleted_at is null
	// ORDER BY
	// 	created_at `

	//to handle anomaly: using wrong stock_price
	//e.g row 0: stock_in_id 0 (pending), row 1: has stock_in
	//this query will prioritize non pending stock price to be using
	sql := `SELECT
	if(stock_in_id IS NULL, created_at + (UNIX_TIMESTAMP() * 1000), created_at) _created,
		sp.*
	FROM
		stock_price sp
	WHERE
		product_detail_fkid in @productIds and (stock_in_id IS NULL OR stock_in_id = '' OR stock_out < stock_in)
	ORDER BY
		_created, stock_price_id`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"productIds": productDetailId,
	})

	var result []domain.Hpp
	err := h.db.Query(sql, params...).Model(&result)
	return result, err
}

func (h hppRepository) FetchLastHppByProductId(productDetailId int) (domain.Hpp, error) {
	sql := "select * from stock_price where product_detail_fkid = ? and deleted_at is null order by created_at desc, stock_price_id desc limit 1"

	var hpp domain.Hpp
	err := h.db.Query(sql, productDetailId).Model(&hpp)
	return hpp, err
}

func (h hppRepository) FetchHppByStockInId(stockInId string, source string) ([]domain.Hpp, error) {
	sql := "select * from stock_price where stock_in_id = ? and stock_in_source = ? and deleted_at is null "
	data, err := db.QueryArray(sql, stockInId, source)
	if err != nil {
		return nil, err
	}

	var result []domain.Hpp
	err = cast.ToModel(data, &result)
	log.IfError(err)
	return result, err
}

func (h hppRepository) FetchCurrentPriceOfProduct(productDetailId int) (float32, error) {
	sql := `select price from stock_price 
	where product_detail_fkid = ? and (stock_out < stock_in) and deleted_at is null 
	order by created_at asc limit 1`

	data, err := db.Query(sql, productDetailId)
	if err != nil {
		return 0, err
	}

	if len(data) > 0 {
		return cast.ToFloat32(data["price"]), nil
	}

	sql = "select price from stock_price  where product_detail_fkid = ? order by created_at desc limit 1 "
	data, err = db.Query(sql, productDetailId)
	if err != nil {
		return 0, err
	}
	if len(data) == 0 {
		return 0, fmt.Errorf("no data found")
	}
	return cast.ToFloat32(data["price"]), nil
}

func (h hppRepository) FetchStockPriceDetail(stockPriceId ...interface{}) (*[]models.StockPriceDetailEntity, error) {
	if len(stockPriceId) == 0 {
		return nil, nil
	}
	whereIn := strings.TrimRight(strings.Repeat("?,", len(stockPriceId)), ",")
	sql := "select * from stock_price_detail where stock_price_fkid in ( " + whereIn + " ) order by created_at "

	var result []models.StockPriceDetailEntity
	err := h.db.Query(sql, stockPriceId...).Model(&result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

func (h hppRepository) FetchStockPriceDetailByOutSourceId(id string, idSource string) ([]models.StockPriceDetailEntity, error) {
	sql := "select * from stock_price_detail where stock_out_id = ? and stock_out_source = ?"
	var result []models.StockPriceDetailEntity
	err := h.db.Query(sql, id, idSource).Model(&result)
	return result, err
}

// FetchStockPriceDetailByStockIn implements hpp.Repository.
func (*hppRepository) FetchStockPriceDetailByStockIn(inId string, inSource string, stockOutSources ...string) ([]models.StockPriceDetailEntity, error) {
	panic("unimplemented")
}

func (h hppRepository) FetchStockPriceByOutSourceId(id string, idSource string) ([]models.StockPriceWithDetailQty, error) {
	sql := `SELECT
	sp.*, spd.stock_out as stock_out_detail, spd.stock_price_detail_id
FROM
	stock_price sp
	JOIN stock_price_detail spd ON spd.stock_price_fkid = sp.stock_price_id
WHERE
	spd.stock_out_id = ? 
	AND spd.stock_out_source = ? `

	var result []models.StockPriceWithDetailQty
	err := h.db.Query(sql, id, idSource).Model(&result)
	return result, err
}

func (h hppRepository) UpdatePriceBuyProduct(productDetailId int, priceBuy int) error {
	_, err := db.Update("products_detail", map[string]interface{}{
		"price_buy":     priceBuy,
		"data_modified": utils.CurrentMillis(),
	}, "product_detail_id = ?", productDetailId)
	return err
}

func (h hppRepository) UpdatePriceBuySalesDetail(updates []models.PriceBuyUpdate) error {
	for _, price := range updates {
		_, err := db.Update("sales_detail", map[string]interface{}{
			"price_buy_total": price.PriceBuyTotal,
			"price_buy":       price.PriceBuy,
		}, "sales_detail_id = ?", price.Id)

		if err != nil {
			return err
		}
	}
	return nil
}

func (h hppRepository) DeleteHppById(stockPriceId int) error {
	_, err := db.Delete("stock_price", "stock_price_id = ?", stockPriceId)
	return err
	// return h.DeleteHppByIdSoftly(stockPriceId)
}

func (h hppRepository) DeleteHppByIdSoftly(stockPriceId int) error {
	_, err := db.Update("stock_price", map[string]interface{}{
		"deleted_at": utils.CurrentMillis(),
	}, "stock_price_id = ?", stockPriceId)
	return err
}

func (h hppRepository) DeleteStockPriceDetailByOutsourceId(id string, source string) error {
	resp, err := db.Delete("stock_price_detail", "stock_out_id = ? and stock_out_source = ?", id, source)
	if err == nil {
		rows, _ := resp.RowsAffected()
		log.Info("delete of %s (%s) effected: %d rows", id, source, rows)
	}
	return err
}

func (h hppRepository) AdjustStockPriceDetail(adjust []models.StockPriceDetailEntity, remove []int64, stockNew []map[string]interface{}) (int64, error) {
	log.Info("%v adjust, %v remove, %v new", len(adjust), len(remove), len(stockNew))
	newStockPriceId := int64(0)
	err := db.WithTransaction(func(tx db.Transaction) error {
		if len(adjust) > 0 {
			batchInsertData := make([]map[string]interface{}, 0)
			for _, update := range adjust {
				if update.UpdatedAt == 0 {
					update.UpdatedAt = utils.CurrentMillis()
				}
				if update.StockPriceDetailID == 0 {
					// tx.Insert("stock_price_detail", update.ToMap())
					batchInsertData = append(batchInsertData, update.ToMap())
				} else {
					tx.Update("stock_price_detail", update.ToMap(), "stock_price_detail_id = ?", update.StockPriceDetailID)
				}
			}
			if len(batchInsertData) > 0 {
				res := tx.InsertBatch("stock_price_detail", batchInsertData)
				rows, _ := res.RowsAffected()
				log.Info("adjust, insertbatch, %v rows, %v data", rows, len(batchInsertData))
			}
		}

		if len(remove) > 0 {
			res := tx.Delete("stock_price_detail", "stock_price_detail_id in "+db.WhereIn(len(remove)), array.TransformToInterface(remove)...)
			rows, _ := res.RowsAffected()
			log.Info("stock_price_detail removed, size %v, removed %v", len(remove), rows)
		}

		totalStockOut := float32(0)
		for _, row := range stockNew {
			totalStockOut += cast.ToFloat32(row["stock_out"])
		}

		if totalStockOut > 0 {
			stockPrice, err := db.Query("select product_detail_fkid, price from stock_price where stock_price_id = ?", stockNew[0]["stock_price_fkid"])
			if err != nil {
				return err
			}

			stockPrice["stock_out"] = totalStockOut
			stockPrice["stock_in"] = 0
			stockPrice["updated_at"] = utils.CurrentMillis()
			stockPrice["created_at"] = utils.CurrentMillis() + 1 //in case time same as last stock_in

			resp := tx.Insert("stock_price", stockPrice)
			newStockPriceId, _ = resp.LastInsertId()
			log.Info("stock price added: %v, out: %v", newStockPriceId, totalStockOut)

			batchData := make([]map[string]interface{}, 0)
			for _, row := range stockNew {
				var createdAt interface{}
				if date, ok := row["created_at"]; ok {
					createdAt = date
				} else {
					createdAt = utils.CurrentMillis()
				}

				data := map[string]interface{}{
					"stock_price_fkid": newStockPriceId,
					"stock_out":        row["stock_out"],
					"stock_out_id":     row["stock_out_id"],
					"stock_out_source": row["stock_out_source"],
					"meta_data":        row["meta_data"],
					"created_at":       createdAt,
					"updated_at":       utils.CurrentMillis(),
				}
				batchData = append(batchData, data)
				// tx.Insert("stock_price_detail", data)
			}
			res := tx.InsertBatch("stock_price_detail", batchData)
			lastId, _ := res.LastInsertId()
			rows, _ := res.RowsAffected()
			for i := int64(0); i < rows; i++ {
				log.Info("insertStockPriceDetail, id %v, lastDetailId %v", newStockPriceId, lastId-i)
			}
		}

		return nil
	})

	return newStockPriceId, err
}

func (h hppRepository) FetchStockOpname(stockOpnameIds ...interface{}) ([]models.StockOpnameEntity, error) {
	sql := "select * from stock_opname"
	params := make([]interface{}, 0)
	if len(stockOpnameIds) > 0 {
		sql += " where opname_id in " + db.WhereIn(len(stockOpnameIds))
		params = append(params, stockOpnameIds...)
	}
	var result []models.StockOpnameEntity
	err := h.db.Query(sql, params...).Model(&result)
	return result, err
}

func (h hppRepository) FetchStockPrice() (*[]domain.Hpp, error) {
	sql := "select * from stock_price $WHERE order by created_at, stock_price_id" //order by created_at desc, stock_price_id desc
	whereQuery := make([]string, 0)
	params := make([]interface{}, 0)

	if h.filter != nil {
		if h.filter.ProductDetailId > 0 {
			whereQuery = append(whereQuery, "  product_detail_fkid = ? ")
			params = append(params, h.filter.ProductDetailId)
		}
		if len(h.filter.ProductDetailIds) > 0 {
			whereQuery = append(whereQuery, " product_detail_fkid in "+db.WhereIn(len(h.filter.ProductDetailIds)))
			params = append(params, cast.ToInterfaceArray(h.filter.ProductDetailIds)...)
		}

		if h.filter.StockOut.Operator != "" {
			whereQuery = append(whereQuery, fmt.Sprintf(" stock_out %v ? ", h.filter.StockOut.Operator))
			params = append(params, h.filter.StockOut.Value)
		}

		if h.filter.StockIn.Operator != "" {
			whereQuery = append(whereQuery, fmt.Sprintf(" stock_in %v ? ", h.filter.StockIn.Operator))
			params = append(params, h.filter.StockIn.Value)
		}

		if h.filter.IsAvailable {
			whereQuery = append(whereQuery, " (stock_out < stock_in or stock_in_id is null) ")
		}

		if h.filter.IsPending {
			whereQuery = append(whereQuery, " (stock_in_id is null or stock_in_id = '') ")
		}

		if h.filter.DateStart > 0 {
			whereQuery = append(whereQuery, " created_at >= ? ")
			params = append(params, h.filter.DateStart)
		}
	}

	whereSql := ""
	if len(whereQuery) > 0 {
		whereSql = " WHERE " + strings.Join(whereQuery, " AND ")
	}

	sql = strings.Replace(sql, "$WHERE", whereSql, 1)
	var result []domain.Hpp
	err := h.db.Query(sql, params...).Model(&result)
	return &result, err
}

// transfer
func (h hppRepository) FetchTransferConfirm(transferId int) (*[]models.TransferConfirm, error) {
	sql := `SELECT tp.product_detail_fkid, tp.product_detail_des_fkid, tp.qty, tc.qty_confirm,
	tp.markup, tp.markup_type, tp.transfer_product_id, tc.transfer_confirm_id,
	tp.price, tp.discount
	from transfer_products tp 
	join transfer_confirm tc on tc.transfer_product_fkid=tp.transfer_product_id
	where tp.transfer_fkid=?`

	var transfers []models.TransferConfirm
	err := h.db.Query(sql, transferId).Model(&transfers)
	return &transfers, err
}

// FetchStockPriceByIdAndSource implements domain.HppRepository.
func (h *hppRepository) FetchStockPriceByIdAndSource(id string, source string) (*[]domain.Hpp, error) {
	sql := `SELECT * FROM stock_price WHERE stock_in_id = ? AND stock_in_source = ?`
	var hpp []domain.Hpp
	err := h.db.Query(sql, id, source).Model(&hpp)
	if err != nil {
		return nil, err
	}
	return &hpp, nil
}

// FetchNearestStockPrice implements hpp.Repository.
func (h *hppRepository) FetchNearestStockPrice(productDetailId int, createdAt int64, stockInSource string) ([]domain.Hpp, error) {
	//first, fetch the nearest stock_in id
	//this becuase, the same stock_in id can be found  in 2 rows or more
	sql := `SELECT stock_in_id from stock_price
	where product_detail_fkid= @productDetailId 
	and created_at > @created 	
	$WHERE
	order by created_at desc, stock_price_id 
	desc limit 1 `

	whereSql := ""
	if stockInSource != "" {
		whereSql = " and stock_in_source = @stockInSource "
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"productDetailId": productDetailId,
		"created":         createdAt,
		"stockInSource":   stockInSource,
		"WHERE":           whereSql,
	})

	var stockPrice domain.Hpp
	err := h.db.Query(sql, params...).Model(&stockPrice)
	if err != nil {
		return nil, err
	}
	//if not found, just return
	if stockPrice.StockInId == "" {
		return []domain.Hpp{}, nil
	}

	sql = `SELECT * from stock_price 
	where stock_in_id=?
	and product_detail_fkid=?
	and stock_in_source=?	
	order by created_at `

	var result []domain.Hpp
	err = h.db.Query(sql, stockPrice.StockInId, productDetailId, stockInSource).Model(&result)
	return result, err
}

// FetchStockPriceWithDetail implements hpp.Repository.
func (h *hppRepository) FetchStockPriceWithDetail(param *domain.StockPriceRequest) ([]domain.StockPriceResponse, error) {
	sql := `SELECT CONCAT(p.name, COALESCE(concat(' ', '(', pdv.variant_name, ')'),'')) as product_name,
	o.name as outlet_name, sp.created_at as created_at_in, sp.stock_out as stock_out_total,sp.*, spd.*
	from stock_price sp 
	left join stock_price_detail spd on sp.stock_price_id=spd.stock_price_fkid
	join products_detail pd on pd.product_detail_id=sp.product_detail_fkid
	join products p on p.product_id=pd.product_fkid
	join outlets o on o.outlet_id=pd.outlet_fkid
	left join products_detail_variant pdv on pdv.variant_id=pd.variant_fkid
	where p.name = @productName and o.name = @outletName and o.admin_fkid = @adminId
	and sp.created_at > @startDate
	order by sp.created_at, sp.stock_price_id, spd.created_at`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"productName": param.ProductName,
		"outletName":  param.OutletName,
		"adminId":     param.AdminId,
		"startDate":   param.StartDate,
	})

	var result []domain.StockPriceResponse
	err := h.db.Query(sql, params...).Model(&result)
	return result, err
}

// UpdateStockPriceDetailQty implements hpp.Repository.
func (h *hppRepository) UpdateStockPriceDetailQty(stockPriceDetailId int64, qtyOut float32) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		data, err := db.Query("select stock_out, stock_price_fkid from stock_price_detail where stock_price_detail_id = ?", stockPriceDetailId)
		if err != nil {
			return err
		}
		if qtyOut == 0 {
			// tx.Delete("stock_price_detail", "stock_price_detail_id = ? and stock_out_source != ?", stockPriceDetailId, domain.StockOpname.String())
			tx.Delete("stock_price_detail", "stock_price_detail_id = ? ", stockPriceDetailId)
		} else {
			tx.Update("stock_price_detail", map[string]interface{}{
				"stock_out":  qtyOut,
				"updated_at": utils.CurrentMillis(),
			}, "stock_price_detail_id = ?", stockPriceDetailId)
		}
		diff := cast.ToFloat32(data["stock_out"]) - qtyOut
		tx.Exec("update stock_price set stock_out = stock_out - ? where stock_price_id = ?", diff, data["stock_price_fkid"])
		log.Info("update stockOut stockPrice id: %v, updated qty: %v, qtyOut: %v, stockPriceDetailId: %v", data["stock_price_fkid"], diff, qtyOut, stockPriceDetailId)
		return nil
	})
	return err
}

// AdjustStockPriceQtyOut implements hpp.Repository.
func (h *hppRepository) AdjustStockPriceQtyOut(stockPrice map[int64]float32) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		for id, qtyAdjust := range stockPrice {
			// fmt.Println("update --- ", id, qtyAdjust)
			if id == 0 || qtyAdjust == 0 {
				continue
			}
			tx.Exec("update stock_price set stock_out = stock_out + ?, updated_at =? where stock_price_id = ?", qtyAdjust, utils.CurrentMillis(), id)
			log.Info("update stockOut stockPrice id: %v, updated qty: %v", id, qtyAdjust)
		}
		return nil
	})
	return err
}

// UpdateStockPriceOutBasedDetail implements hpp.Repository.
func (h *hppRepository) UpdateStockPriceOutBasedDetail(stockPriceIds ...interface{}) (int64, error) {
	if len(stockPriceIds) == 0 {
		return 0, nil
	}
	sql := `update stock_price sp 
	inner join (select sum(stock_out) as stock_out, stock_price_fkid from stock_price_detail where stock_price_fkid in @stockPriceIds group by stock_price_fkid) sd 
	on sd.stock_price_fkid=sp.stock_price_id
	set sp.stock_out=sd.stock_out
	where sp.stock_price_id in @ids `

	sql, params := db.MapParam(sql, map[string]interface{}{
		"stockPriceIds": stockPriceIds,
		"ids":           stockPriceIds,
	})

	res, err := h.db.Conn.Exec(sql, params...)
	if err != nil {
		return 0, err
	}

	return res.RowsAffected()
}

// DeleteUnUsedStockPrice implements hpp.Repository.
func (h *hppRepository) DeleteUnUsedStockPrice() error {
	_, err := h.db.Conn.Exec(`delete from stock_price where stock_in=0 and stock_out<=0 and (stock_in_id='' or stock_in_id is null)`)
	return err
}

// DeleteStockPriceDetail implements hpp.Repository.
func (h *hppRepository) DeleteStockPriceDetail(id int64) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		stockDetail, err := db.Query("select stock_out, stock_price_fkid from stock_price_detail where stock_price_detail_id = ?", id)
		if err != nil {
			return err
		}
		delRows, _ := tx.Delete("stock_price_detail", "stock_price_detail_id = ?", id).RowsAffected()
		updateRows, _ := tx.Exec("update stock_price set stock_out = stock_out - ? where stock_price_id = ?", stockDetail["stock_out"], stockDetail["stock_price_fkid"]).RowsAffected()
		log.Info("delete stock_price_detail, stockPriceId %v, detailId: %v, out: %v, delRows: %v, updateRows: %v", stockDetail["stock_price_fkid"], id, stockDetail["stock_out"], delRows, updateRows)
		return nil
	})
	return err
}
