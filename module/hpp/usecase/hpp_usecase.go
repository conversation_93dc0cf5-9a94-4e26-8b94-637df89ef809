package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"strings"
	"sync"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/google"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
	"gitlab.com/uniqdev/backend/api-report/module/purchase"
)

var mu sync.Mutex
var env = os.Getenv("ENV")
var enableFifo = strings.TrimSpace(strings.ToLower(os.Getenv("ENABLE_FIFO")))

type hppUseCase struct {
	repo         hpp.Repository
	repoPurchase purchase.Repository
	repoProduct  domain.ProductRepository
}

// FixWrongFifo implements hpp.UseCase.
func (h *hppUseCase) FixWrongFifo(productIds ...int) {
	for i := 0; i < 20; i++ {
		data, err := h.repo.FetchAnomalyOverStockOutDetail(productIds...)
		if log.IfError(err) || data == nil {
			break
		}

		// h.UpdateStockPriceQtyIn(data.StockInId, data.StockInSource, data.StockIn)
		h.AdjustStockPrice(data.ProductDetailFkid, data.CreatedAt)
		fmt.Println(i, "........")
	}
}

// UpdateStockPrice implements hpp.UseCase.
func (h *hppUseCase) UpdateStockPrice(param *domain.StockPriceUpdateRequest) error {
	if param.StockInSrouce != domain.StockOpname.String() {
		err := h.AdjustStockOpname(param.StockInId, param.StockInSrouce, param.Qty)
		if log.IfError(err) {
			return err
		}
	}

	err := h.UpdateStockPriceQtyIn(param.StockInId, param.StockInSrouce, param.Qty)
	return err
}

func NewHppUseCase(repository hpp.Repository,
	repoPurhcase purchase.Repository,
	repoProduct domain.ProductRepository) hpp.UseCase {
	return &hppUseCase{repository, repoPurhcase, repoProduct}
}

// FetchStockPrice implements hpp.UseCase.
func (h *hppUseCase) FetchStockPrice(param *domain.StockPriceRequest) ([]domain.StockPriceResponse, error) {
	return h.repo.FetchStockPriceWithDetail(param)
}

// SimulateFifoAdjusment implements hpp.UseCase.
func (h *hppUseCase) SimulateFifoAdjusment(hpps []domain.Hpp) (domain.HppSimulationResponse, error) {
	result, changes := recalculateHpp(hpps)
	return domain.HppSimulationResponse{
		UpdateHpp: result,
		Changes:   changes,
	}, nil
}

func IsFifoEnabled() bool {
	return env == "development" || enableFifo == "true"
}

func (h *hppUseCase) HandleNewStockInOut(subs domain.HppSubscription) {
	if !IsFifoEnabled() {
		// fmt.Printf("fifo not runnning... ENV: '%s', ENABLE: '%v'\n", env, enableFifo)
		return
	}

	mu.Lock()
	defer mu.Unlock()

	isRecorded := h.isRecordedInStockPrice(subs.Id, subs.Source.String())
	log.Info("HandleNewStockInOut %s,%v, qty: %v | is stock  in: %v, isRecorded: %v", subs.Source, subs.Id, subs.Qty, isInStock(subs.Source), isRecorded)

	if isRecorded && os.Getenv("ENV") == "development" {
		fmt.Println(">>> SKIP, this already recorded")
		log.IfError(fmt.Errorf(">>> SKIP, this already recorded: %v", utils.SimplyToJson(subs)))
		return
	}
	stockPriceIds := make([]interface{}, 0)

	switch subs.Source {
	case domain.Sale:
		h.recordSales(subs.Id)
		return
	case domain.SaleRefund:
		// if env != "development" {
		// 	fmt.Println("do nothing on refund... env: ", env)
		// 	return
		// }

		ids := h.recordSalesRefund(subs.Id)
		if len(ids) > 0 {
			stockPriceIds = append(stockPriceIds, ids...)
		}
	case domain.PurchaseConfirm:
		ids := h.recordPurchaseConfirm(cast.ToInt(subs.Id), cast.ToFloat32(subs.Qty))
		if len(ids) > 0 {
			stockPriceIds = append(stockPriceIds, ids...)
		}
	case domain.PurchaseRetur:
		ids := h.recordPurchaseRetur(cast.ToInt(subs.Id), subs.Qty)
		if len(ids) > 0 {
			stockPriceIds = append(stockPriceIds, ids...)
		}
	case domain.Production:
		h.recordPriceOfProduction(cast.ToInt(subs.Id))
	case domain.TransferConfirm:
		h.recordTransferConfirm(cast.ToInt(subs.Id))
	case domain.StockOpname:
		var stockOpnameIds []int
		err := json.Unmarshal([]byte(subs.Id), &stockOpnameIds)
		log.IfError(err)
		h.HandleStockOpname(stockOpnameIds)
	case domain.PurchaseConfirmUpdate:
		var ids []int
		log.IfError(json.Unmarshal([]byte(subs.Id), &ids))
		h.HandlePurchaseConfirmUpdate(ids)
	case domain.Spoil:
		var ids []int
		log.IfError(json.Unmarshal([]byte(subs.Id), &ids))
		h.handleSpoil(ids)
	default:
		log.IfError(fmt.Errorf("can not handle hpp stockInOut type: %v", subs.Source))
	}

	h.updatePriceBuyOutOriginSource(stockPriceIds...)
	log.Info("[FINISH] HandleNewStockInOut --> '%s' (%v), qty: %v ", subs.Source, subs.Id, subs.Qty)
}

func (h *hppUseCase) isRecordedInStockPrice(id string, source string) bool {
	record, err := h.repo.FetchStockPriceByIdAndSource(id, source)
	if log.IfError(err) {
		return false
	}
	return record != nil && len(*record) > 0
}

// This function logs the stock out process for different data types including sales and sales breakdown.
// The dataDetail parameter should contain the product_detail_fkid and the quantity (qty).
// The dataType parameter could be either 'sales' or 'sales_breakdown'.
// The function returns a pointer to a slice of StockOutProductPrice and an error.
// The function returns the product given in param, with the price used
func (h *hppUseCase) logStockOut(dataDetail *[]models.StockOutProduct, dataType, dataId string) (*[]models.StockOutProductPrice, error) {
	stockOutPrices := make([]models.StockOutProductPrice, 0)
	if dataDetail == nil || len(*dataDetail) == 0 {
		log.Info("skippp %v (%v) has no data", dataType, dataId)
		return &stockOutPrices, nil
	}

	if dataId == "" || strings.TrimSpace(dataId) == "" {
		log.IfError(fmt.Errorf("logStockOut empty dataId: '%v'", dataId))
	}

	// Collect all productDetailIds from dataDetail
	productDetailIds := make([]int, 0, len(*dataDetail))
	for _, detail := range *dataDetail {
		productDetailIds = append(productDetailIds, detail.ProductDetailId)
	}

	// Fetch all Hpp records at once
	hpps, err := h.repo.FetchStockOutHpp(productDetailIds...)
	if log.IfError(err) {
		return &stockOutPrices, err
	}

	// Convert hpps to a map for easy access
	hppMap := make(map[int][]domain.Hpp)
	for _, hpp := range hpps {
		if _, exist := hppMap[hpp.ProductDetailFkid]; !exist {
			hppMap[hpp.ProductDetailFkid] = make([]domain.Hpp, 0)
		}
		hppMap[hpp.ProductDetailFkid] = append(hppMap[hpp.ProductDetailFkid], hpp)
	}

	logLine := log.Line()
	defer logLine.PrintDebug()

	stockPriceIdMap := make(map[int]bool)
	productDetailIdUpdates := make(map[int]bool)

	for _, detail := range *dataDetail {
		//fetch all available hpp to use
		hpps := hppMap[detail.ProductDetailId]
		log.Info("#%v (%v), prodId: %v, hpps avail size: %v", dataType, dataId, detail.ProductDetailId, len(hpps))
		logLine.AddLog("prodId: %v | hpps %v", detail.ProductDetailId, utils.SimplyToJson(hpps))
		// if len(hpps) == 1 {
		// 	log.Info("hpps: %v", utils.SimplyToJson(hpps))
		// }
		if len(hpps) == 0 { //if not found, use the latest (that already used), or from product_detail
			lastPrice := float32(0)
			lastHpp, err := h.repo.FetchLastHppByProductId(detail.ProductDetailId)
			if err == nil {
				lastPrice = lastHpp.Price
			}

			//use the price_buy from products_detail
			if lastPrice == 0 && lastHpp.StockPriceId == 0 {
				productDetail, err := h.repo.FetchProductByProductDetailId(detail.ProductDetailId)
				log.IfError(err)
				lastPrice = cast.ToFloat32(productDetail["price_buy"])
				log.Debug("price buy from product_detail: %v", productDetail["price_buy"])
			}

			newHpp := domain.Hpp{
				ProductDetailFkid: detail.ProductDetailId,
				StockOut:          detail.Qty,
				StockOutId:        dataId,
				Price:             lastPrice,
				StockOutSource:    dataType,
				UpdatedAt:         detail.CreatedAt,
				CreatedAt:         detail.CreatedAt,
			}
			id, err := h.repo.InsertStockPrice(newHpp)
			if err == nil {
				log.Info("stock price added: %v, in: %v, inId: %v", id, detail.Qty, "[empty]")
				newHpp.StockPriceId = int(id)
				hppMap[detail.ProductDetailId] = append(hppMap[detail.ProductDetailId], newHpp)
			}
			stockOutPrices = append(stockOutPrices, models.StockOutProductPrice{
				StockOutProduct: detail,
				Price:           lastPrice,
			})
			stockPriceIdMap[int(id)] = true
		} else if len(hpps) >= 1 && hpps[0].StockIn == 0 && hpps[0].StockInSource == "" {
			log.Info("add to stock out detail, stock_price_id: %v, in: %v, source: '%v'", hpps[0].StockPriceId, hpps[0].StockIn, hpps[0].StockInSource)
			_ = h.repo.InsertStockOutDetail(domain.StockPriceDetail{
				StockPriceId:   hpps[0].StockPriceId,
				Qty:            detail.Qty,
				StockOutId:     dataId,
				StockOutSource: dataType,
				CreatedAt:      detail.CreatedAt,
				MetaData:       detail.MetaData,
			})
			stockOutPrices = append(stockOutPrices, models.StockOutProductPrice{
				StockOutProduct: detail,
				Price:           hpps[0].Price,
			})
			stockPriceIdMap[hpps[0].StockPriceId] = true
		} else {
			lastPrice := float32(0)
			qty := detail.Qty
			logLine.AddLog("source: %v, id: %v, ProductDetailId: %v, qtyOutOri: %v, ", dataType, dataId, detail.ProductDetailId, qty)
			for j, hpp := range hpps {
				if hpp.StockInId != "" && hpp.StockOut >= hpp.StockIn {
					log.Info(">> skip.. no available stockIn, in: %v, out: %v, id: %v", hpp.StockIn, hpp.StockOut, hpp.StockPriceId)
					continue
				}
				qtyOut := float32(qty)
				//qtyLeft: qty left in db that can be used
				//if qty out, greater than what is currently left or available, then only use available qty
				if qtyLeft := hpp.StockIn - hpp.StockOut; (hpp.StockIn > 0 && hpp.StockInId != "") && qtyOut > qtyLeft {
					logLine.AddLog("qtyLeft: %v, set qtyOut to whats left", qtyLeft)
					qtyOut = qtyLeft
				}

				logLine.AddLog("qtyOut: %v, qty: %v", qtyOut, qty)
				hpps[j].StockOut += qtyOut
				err = h.repo.InsertStockOutDetail(domain.StockPriceDetail{
					StockPriceId:   hpp.StockPriceId,
					Qty:            qtyOut,
					StockOutId:     dataId,
					StockOutSource: dataType,
					CreatedAt:      detail.CreatedAt,
					MetaData:       detail.MetaData,
				})
				log.IfError(err)

				stockOutPrices = append(stockOutPrices, models.StockOutProductPrice{
					StockOutProduct: models.StockOutProduct{
						ProductDetailId: detail.ProductDetailId,
						Qty:             qtyOut,
					},
					Price: hpp.Price,
				})
				stockPriceIdMap[hpp.StockPriceId] = true

				lastPrice = hpp.Price
				logLine.AddLog("qtyOut: %v, qtyNow: %v, hpp (%v) in %v, out %v | ", qtyOut, qty-(qtyOut), hpp.StockPriceId, hpp.StockIn, hpp.StockOut)
				if !utils.IsValidFloat32((qty - (qtyOut))) {
					log.Info("loop stops... value becomes infinity. -> %v", logLine.String())
					log.IfError(fmt.Errorf("value becomes infinity: %v", logLine.String()))
					break
				}
				qty -= qtyOut
				if qty <= 0 {
					break
				}
			}

			// log.Debug("total hpp: %v + (%v x %v) = %v", totalPrice, qty, lastPrice, (totalPrice)+(qty*(lastPrice)))
			//if qty still remain, add all into new row
			if qty > 0 {
				newHpp := domain.Hpp{
					ProductDetailFkid: detail.ProductDetailId,
					StockOut:          float32(qty),
					Price:             lastPrice,
					StockOutId:        dataId,
					StockOutSource:    dataType,
					UpdatedAt:         detail.CreatedAt,
					CreatedAt:         detail.CreatedAt,
					StockOutMetaData:  detail.MetaData,
				}
				id, err := h.repo.InsertStockPrice(newHpp)
				if err == nil {
					log.Info("stock price added: %v, out: %v, outId: %v", id, qty, dataId)
					newHpp.StockPriceId = int(id)
					hppMap[detail.ProductDetailId] = append(hppMap[detail.ProductDetailId], newHpp)
				} else {
					log.Info(logLine.String())
				}

				if len(hpps) == 1 {
					log.Info("hpp: %v", utils.SimplyToJson(hpps))
				}

				stockOutPrices = append(stockOutPrices, models.StockOutProductPrice{
					StockOutProduct: detail,
					Price:           lastPrice,
				})
				stockPriceIdMap[int(id)] = true
			}
		}

		productDetailIdUpdates[detail.ProductDetailId] = true
		// h.updateProductPriceBuy(detail.ProductDetailId)
		// if dataType == domain.Sale.String() || dataType == "sales_beakdown" {
		// 	h.updateSalesPriceBuy(dataType, dataId)
		// 	h.publishSalesHppUpdate(dataId)
		// }
	}

	//for temporary:
	//try to match stock_out in stock_price and stock_price_detail
	//for now, just for sales_breakdown, its seems to have the problem
	if dataType == "sales_beakdown" {
		ids := array.GetKeys(stockPriceIdMap)
		details, err := h.repo.FetchStockPriceDetail(ids...)
		log.IfError(err)
		rows, err := h.repo.UpdateStockPriceOutBasedDetail(ids...)
		if !log.IfError(err) && rows > 0 {
			detailsAfter, err := h.repo.FetchStockPriceDetail(ids...)
			log.IfError(err)
			// log.IfError(fmt.Errorf("stock_out not match, after out '%v', rows: %v, ids: %v", dataType, rows, ids))
			log.Info("stock_out not match, after out '%v', rows: %v, ids: %v", dataType, rows, ids)
			log.Info("hppMap %s", utils.SimplyToJson(hppMap))
			log.Info("LOG DETAIL >> %v", logLine.String())
			log.Info("details original: %v", utils.SimplyToJson(simplifyStockPriceDetails(*details)))
			log.Info("details after: %v", utils.SimplyToJson(simplifyStockPriceDetails(*detailsAfter)))
		}
	}

	h.updateProductPriceBuy(array.MapKey(productDetailIdUpdates)...)
	if dataType == domain.Sale.String() || dataType == "sales_beakdown" {
		h.updateSalesPriceBuy(dataType, dataId)
		h.publishSalesHppUpdate(dataId)
	}

	return &stockOutPrices, nil
}

func simplifyStockPriceDetails(details []models.StockPriceDetailEntity) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)
	for _, row := range details {
		result = append(result, map[string]interface{}{
			"id":  row.StockPriceDetailID,
			"out": row.StockOut,
		})
	}
	return result
}

// logStockIn logs the details of the stock in operation. It checks if there is any pending stock price,
// if there is, it updates the pending stock with the new stock in. If the stock out is greater than the stock in,
// it creates a new stock price with 0 stock in and adjusts the stock price detail with the new stock price id.
// If there is no pending stock price, it simply inserts a new stock price.
// >>> TODO WARNING: if there is same productId in dataDetail, bug might occur, better to make them unique
func (h *hppUseCase) logStockIn(dataDetail *[]models.StockInProduct, dataType, dataId string) {
	if dataDetail == nil || len(*dataDetail) == 0 {
		_, fn, line, _ := runtime.Caller(1)
		log.Info("[%v: %v] skip logStcokIn, dataDetail arg is nil", fn, line)
		return
	}
	logLine := log.Line()
	defer logLine.PrintDebug()
	log.Info("logStockIn for %v #%v: %v products", dataType, dataId, len(*dataDetail))

	//if there is stock_price pending (has stockout but no stock in) use that
	//then if in < out, then should create new stock_price, with the rest of stock_out
	//the stock_price_detail then also should be adjusted
	//if above not the case, then just simply insert new stock_price
	productDetailIdMap := make(map[int]bool)
	minTimeCreated := utils.CurrentMillis()
	for _, detail := range *dataDetail {
		productDetailIdMap[detail.ProductDetailId] = true
		if detail.CreatedAt > 0 && detail.CreatedAt < minTimeCreated {
			minTimeCreated = detail.CreatedAt
		}
	}

	productDetailIds := array.MapKey[int](productDetailIdMap)

	//TODO: check if there is stockIn after new stockIn
	//if so, just insert, and do adjustment
	stockIns, err := h.repo.Filter(domain.StockPriceFilter{
		StockIn: domain.FilterInt{
			Operator: ">",
			Value:    0,
		},
		DateStart:        minTimeCreated,
		ProductDetailIds: productDetailIds,
	}).FetchStockPrice()
	log.IfError(err)
	if stockIns == nil {
		stockIns = &[]domain.Hpp{}
	}

	log.Info("stockIn after: %v, size %v", minTimeCreated, len(*stockIns))

	removedIdMap := make(map[int]bool) //contains productIds, which has stockIn after the date of its creation
	if stockIns != nil && len(*stockIns) > 0 {
		detailMap := make(map[int]models.StockInProduct)
		for _, detail := range *dataDetail {
			detailMap[detail.ProductDetailId] = detail
		}

		log.Debug("stockIns: %v", utils.SimplyToJson(stockIns))
		log.Debug("detailMap: %v", utils.SimplyToJson(detailMap))
		var logs strings.Builder

		//check if it has stockIn after its creation
		for _, stockIn := range *stockIns {
			stockInProduct := detailMap[stockIn.ProductDetailFkid]
			if stockIn.StockIn > 0 && stockIn.CreatedAt > stockInProduct.CreatedAt {
				logs.WriteString(fmt.Sprintf("stockIn prod %v, after %v (%v #%v): id %v at %v | ", stockIn.ProductDetailFkid, stockInProduct.CreatedAt, dataType, dataId, stockIn.StockPriceId, stockIn.CreatedAt))
				if env == "development" || env == "staging" {
					removedIdMap[stockIn.ProductDetailFkid] = true
				}
			}
		}
		log.Info(logs.String())
	}

	log.Info("removedIds: %v", utils.SimplyToJson(removedIdMap))
	//insert new stockPrices with adjustments
	h.adjustNewStockPrices(array.MapKey(removedIdMap), dataDetail, dataId, dataType)

	productDetailIds = array.RemoveElements(productDetailIds, array.MapKey(removedIdMap))
	if len(productDetailIds) == 0 {
		log.Info("auto-finish logStockIn, all removed, ids: %v", removedIdMap)
		return
	}

	stockPrices, err := h.repo.Filter(domain.StockPriceFilter{
		IsPending:        true,
		ProductDetailIds: productDetailIds,
	}).FetchStockPrice()
	log.IfError(err)

	if stockPrices == nil {
		stockPrices = &[]domain.Hpp{}
	}

	stockPriceMap := make(map[int]domain.Hpp)
	for _, stockPrice := range *stockPrices {
		stockPriceMap[stockPrice.ProductDetailFkid] = stockPrice
	}
	log.Info("%v has pendingStockPrice: %v | %v", utils.SimplyToJson(productDetailIds), len(*stockPrices), utils.SimplyToJson(stockPrices))

	for _, stockIn := range *dataDetail {
		if stockIn.CreatedAt == 0 {
			stockIn.CreatedAt = utils.CurrentMillis()
		}

		//removedIds already been handled, skip in this process
		if removedIdMap[stockIn.ProductDetailId] {
			log.Info(">> skip... prodId %v removed", stockIn.ProductDetailId)
			continue
		}

		//if no pending stock_price, simply insert new stock_price
		if _, ok := stockPriceMap[stockIn.ProductDetailId]; !ok {
			newHpp := domain.Hpp{
				StockInId:         dataId,
				StockInSource:     dataType,
				StockIn:           stockIn.Qty,
				Price:             stockIn.Price,
				ProductDetailFkid: stockIn.ProductDetailId,
				StockInMetaData:   stockIn.MetaData,
				CreatedAt:         stockIn.CreatedAt,
			}
			id, err := h.repo.InsertStockPrice(newHpp)
			if err == nil {
				log.Info("stock price added: %v, in: %v, inId: %v, prodId: %v", id, stockIn.Qty, dataId, stockIn.ProductDetailId)
				newHpp.StockPriceId = int(id)
				stockPriceMap[stockIn.ProductDetailId] = newHpp
			}
			continue
		}

		pendingStockPrice := stockPriceMap[stockIn.ProductDetailId]

		//TODO: we can do adjustment just by calling AdjustStockPrice
		if env == "development" || env == "staging" {
			//we need to update (at least) stock_in_id and stock_in_source, so that AdjustStockPrice func will work
			h.repo.UpdateStockPrice(pendingStockPrice.StockPriceId, map[string]interface{}{
				"price":           stockIn.Price,
				"stock_in":        stockIn.Qty,
				"stock_in_id":     dataId,
				"stock_in_source": dataType,
				"created_at":      stockIn.CreatedAt,
				"meta_data":       utils.SimplyToJson(stockIn.MetaData),
			})

			//once above qty set to 0, now we adjust by real qty in
			// h.UpdateStockPriceQtyIn(dataId, dataType, stockIn.Qty)

			log.Info("--testing by calling AdjustStockPrice, id: %v, qty: %v", pendingStockPrice.StockPriceId, stockIn.Qty)
			h.AdjustStockPrice(stockIn.ProductDetailId, stockIn.CreatedAt)
			continue
		}

		//first, update the pending stock with new stock_in
		//make sure stock_out not greater than stock in
		updatedStockOut := pendingStockPrice.StockOut //updatedStockOut: the possible stock_out, which can not be greater than stock_in
		if updatedStockOut > stockIn.Qty {
			updatedStockOut = stockIn.Qty
		}

		h.repo.UpdateStockPrice(pendingStockPrice.StockPriceId, map[string]interface{}{
			"stock_in":        stockIn.Qty,
			"stock_out":       updatedStockOut,
			"price":           stockIn.Price,
			"stock_in_id":     dataId,
			"stock_in_source": dataType,
			"created_at":      stockIn.CreatedAt,
			"meta_data":       utils.SimplyToJson(stockIn.MetaData),
		})
		log.Info("update stockPrice %v, qtyIn: %v, qtyOut: %v", pendingStockPrice.StockPriceId, stockIn.Qty, updatedStockOut)

		//if new stock_in less than current stock_out, make adjustmnet
		// do adjustment, by creating new stock_price with 0 stock_in
		//the stock_price_detail should be adjusted with this new stock_price_id

		if pendingStockPrice.StockOut > stockIn.Qty {
			stockPriceDetails, err := h.repo.FetchStockPriceDetail(pendingStockPrice.StockPriceId)
			if log.IfError(err) {
				continue
			}

			leftQty := pendingStockPrice.StockOut - stockIn.Qty
			logLine.AddLog("left: %v", leftQty)

			newStockPriceDetail := make([]map[string]interface{}, 0)
			removeStockPriceDetail := make([]int64, 0)
			updateStockPriceDetail := make([]models.StockPriceDetailEntity, 0)

			// Loop through stockPriceDetails from the last item
			//loop until left is empty
			for i := len(*stockPriceDetails) - 1; i >= 0; i-- {
				detail := (*stockPriceDetails)[i]
				fmt.Println(detail.StockOut)

				if detail.StockOut > leftQty {
					newStockPriceDetail = append(newStockPriceDetail, detail.ToMap())
					newStockPriceDetail[len(newStockPriceDetail)-1]["stock_out"] = leftQty

					updateStockPriceDetail = append(updateStockPriceDetail, detail)
					updateStockPriceDetail[len(updateStockPriceDetail)-1].StockOut = detail.StockOut - leftQty
					updateStockPriceDetail[len(updateStockPriceDetail)-1].UpdatedAt = utils.CurrentMillis()
					leftQty = float32(0)
				} else {
					newStockPriceDetail = append(newStockPriceDetail, detail.ToMap())
					removeStockPriceDetail = append(removeStockPriceDetail, detail.StockPriceDetailID)
					leftQty -= detail.StockOut
				}

				if leftQty <= 0 {
					break
				}
			}

			logLine.AddLog(">>>>> adjustPriceDetail --> \nupdate: %v \nremove: %v \nnew: %v", utils.SimplyToJson(updateStockPriceDetail), utils.SimplyToJson(removeStockPriceDetail), utils.SimplyToJson(newStockPriceDetail))
			log.Info("AdjustStockPriceDetail, updated size: %v, remove size: %v, newDetail size: %v", len(updateStockPriceDetail), len(removeStockPriceDetail), len(newStockPriceDetail))
			_, err = h.repo.AdjustStockPriceDetail(updateStockPriceDetail, removeStockPriceDetail, newStockPriceDetail)
			log.IfError(err)
		}
	}
}

// check if stock price should be recorded or not, terms:
// - stock management should be true
func (h *hppUseCase) shouldRecordStockPrice(productDetailId int) bool {
	product, err := h.repo.FetchProductByProductDetailId(productDetailId)
	if err != nil {
		return false
	}
	log.Info("stock_management of product detail id %d is %v", productDetailId, product["stock_management"])
	return cast.ToInt(product["stock_management"]) == 1
}

// update price_buy of products_detail, based on the latest data in stock_price
func (h *hppUseCase) updateProductPriceBuy(productDetailIds ...int) {
	productDetailMap := make(map[int]bool)
	for _, productDetailId := range productDetailIds {
		productDetailMap[productDetailId] = true
	}
	ids := array.MapKey[int](productDetailMap)
	productsPrice, err := h.repo.FetchCurrentPrice(ids...)
	if log.IfError(err) && len(productDetailIds) == 0 {
		return
	}

	log.Info("priceUpdates: %v", utils.SimplyToJson(productsPrice))
	err = h.repo.UpdatePriceBuyProducts(productsPrice...)
	log.IfError(err)

	// log.Info("update price_buy %v to: %v", productDetailId, price)
	// _ = h.repo.UpdatePriceBuyProduct(productDetailId, int(price))
}

// func (h *hppUseCase) updateProductPriceBuy(productDetailIds ...int) {
// 	//TODO make the process more efficient
// 	for _, productDetailId := range productDetailIds {
// 		price, err := h.repo.FetchCurrentPriceOfProduct(productDetailId)
// 		if err != nil {
// 			continue
// 		}

// 		// log.Info("update price_buy %v to: %v", productDetailId, price)
// 		_ = h.repo.UpdatePriceBuyProduct(productDetailId, int(price))
// 	}
// }

// func (h *hppUseCase) getPriceByDataSource(id string, source domain.HppSource) float32 {
// 	switch source {
// 	case domain.PurchaseConfirm:
// 		purchase, _ := h.repo.FetchPurchaseProductByConfirmId(cast.ToInt(id))
// 		return purchase.PriceStok
// 	}
// 	return 0
// }

// updating price buy, based on stock_out_source
func (h *hppUseCase) updatePriceBuyOutOriginSource(stockPriceIds ...interface{}) {
	if len(stockPriceIds) == 0 {
		log.Info("updatePriceBuyOutOriginSource skip... no stockPriceIds were given")
		return
	}

	log.Info("update hpp all stock price ids: %v", stockPriceIds)
	stockPrices, err := h.repo.FetchStockPriceDetail(stockPriceIds...)
	if err != nil {
		return
	}

	for _, stockPrice := range *stockPrices {
		stockOutSource := stockPrice.StockOutSource
		if stockOutSource == "sales" {
			h.updateSalesDetailHpp(stockPrice.StockOutID)
			h.publishSalesHppUpdate(stockPrice.StockOutID)
		} else if stockOutSource == "sales_breakdown" {
			h.updateSalesPriceBuy("sales_breakdown", stockPrice.StockOutID)
			h.publishSalesHppUpdate(stockPrice.StockOutID)
		}
	}
}

func (h *hppUseCase) updateSalesDetailHpp(salesId ...interface{}) {
	if len(salesId) == 0 {
		log.Info("updateSalesDetailHpp stop... no salesId were given")
		return
	}

	logLine := log.Line()
	defer logLine.PrintDebug()

	logLine.AddLog("update hpp sales: %v", salesId)
	for _, id := range salesId {
		prices, err := h.repo.FetchStockPriceByOutSourceId(cast.ToString(id), "sales")
		if err != nil {
			continue
		}

		salesDetail, err := h.repo.FetchSalesDetailBySalesId(cast.ToString(id))
		if err != nil {
			continue
		}

		salesQtyAssign := make(map[int]float32)      //key: sales_detail_id, value: total assign
		salesDetailPriceBuy := make(map[int]float32) //key: sales_detail_id, value: total price buy

		logLine.AddLog("sales '%v' uses %v stockPrices", id, len(prices))
		for _, price := range prices {
			stockOut := price.StockOutDetail //cast.ToFloat32(price["detail_stock_out"])
			for _, sales := range *salesDetail {
				if sales.ProductDetailFkid == price.ProductDetailFkid {
					if salesQtyAssign[sales.SalesDetailID] >= sales.Qty {
						logLine.AddLog("assigned all, %v | %v", salesQtyAssign[sales.SalesDetailID], sales.Qty)
						continue
					}

					taken := stockOut
					qtyRemain := sales.Qty - salesQtyAssign[sales.SalesDetailID]
					if taken > qtyRemain {
						taken = qtyRemain
					}

					salesQtyAssign[sales.SalesDetailID] = salesQtyAssign[sales.SalesDetailID] + taken
					salesDetailPriceBuy[sales.SalesDetailID] = salesDetailPriceBuy[sales.SalesDetailID] + (float32(taken) * price.Price)

					stockOut -= taken
					logLine.AddLog("from %v, taken %v, price: %v, stockOut: %v", price.StockPriceId, taken, price.Price, stockOut)
				}

				if stockOut <= 0 {
					continue
				}
			}
		}

		log.Info("update these sales details -> %s", cast.ToStringJson(salesDetailPriceBuy))
		priceUpdates := make([]models.PriceBuyUpdate, 0)
		for _, sales := range *salesDetail {
			priceUpdates = append(priceUpdates, models.PriceBuyUpdate{
				Id:            sales.SalesDetailID,
				PriceBuy:      salesDetailPriceBuy[sales.SalesDetailID] / sales.Qty,
				PriceBuyTotal: salesDetailPriceBuy[sales.SalesDetailID],
			})
		}

		_ = h.repo.UpdatePriceBuySalesDetail(priceUpdates)
	}
}

// the clone of function "updateSalesDetailHpp"
func (h *hppUseCase) updateSalesPriceBuy(dataType string, salesId ...interface{}) {
	if len(salesId) == 0 {
		log.Info("updateSalesBreakdownHpp stop... no salesId were given")
		return
	}

	if dataType != "sales" && dataType != "sales_breakdown" {
		log.Info("invalid dataType... '%v'", dataType)
		return
	}

	type salesDetails struct {
		SalesDetailId   int
		ProductDetailId int
		Qty             float32
	}

	log.Info("update hpp %s: %v", dataType, salesId)
	for _, id := range salesId {
		prices, err := h.repo.FetchStockPriceByOutSourceId(cast.ToString(id), dataType)
		if err != nil {
			continue
		}

		salesDetail := make([]salesDetails, 0)
		if dataType == "sales" {
			sales, err := h.repo.FetchSalesDetailBySalesId(cast.ToString(id))
			log.IfError(err)
			for _, detail := range *sales {
				salesDetail = append(salesDetail, salesDetails{
					SalesDetailId:   detail.SalesDetailID,
					ProductDetailId: detail.ProductDetailFkid,
					Qty:             detail.Qty,
				})
			}
		} else {
			breakdown, err := h.repo.FetchSalesBreakdown(cast.ToString(id))
			log.IfError(err)
			for _, detail := range *breakdown {
				salesDetail = append(salesDetail, salesDetails{
					SalesDetailId:   detail.SalesDetailID,
					ProductDetailId: detail.ProductDetailFkid,
					Qty:             detail.Qty,
				})
			}
		}

		if err != nil {
			continue
		}

		salesQtyAssign := make(map[int]float32)      //key: sales_detail_id, value: total assign
		salesDetailPriceBuy := make(map[int]float32) //key: sales_detail_id, value: total price buy

		for _, price := range prices {
			stockOut := price.StockOutDetail //cast.ToFloat32(price["detail_stock_out"])
			for _, sales := range salesDetail {
				if sales.ProductDetailId == price.ProductDetailFkid {
					if salesQtyAssign[sales.SalesDetailId] >= sales.Qty {
						continue
					}

					assign := stockOut
					qtyRemain := sales.Qty - salesQtyAssign[sales.SalesDetailId]
					if assign > qtyRemain {
						assign = qtyRemain
					}

					salesQtyAssign[sales.SalesDetailId] = salesQtyAssign[sales.SalesDetailId] + assign
					salesDetailPriceBuy[sales.SalesDetailId] = salesDetailPriceBuy[sales.SalesDetailId] + ((assign) * price.Price)
					// if dataType == "sales" {
					// 	salesDetailPriceBuy[salesDetailId] = salesDetailPriceBuy[salesDetailId] + (float32(assign) * cast.ToFloat32(price["price"]))
					// } else {
					// 	salesDetailPriceBuy[salesDetailId] = cast.ToFloat32(price["price"])
					// }

					stockOut -= assign
				}

				if stockOut <= 0 {
					// fmt.Println("stockOut completed...")
					continue
				}
			}
		}

		priceUpdates := make([]models.PriceBuyUpdate, 0)
		for _, sales := range salesDetail {
			priceUpdates = append(priceUpdates, models.PriceBuyUpdate{
				Id:            sales.SalesDetailId,
				PriceBuy:      salesDetailPriceBuy[sales.SalesDetailId] / sales.Qty,
				PriceBuyTotal: salesDetailPriceBuy[sales.SalesDetailId],
			})
		}

		// log.Info("update these %s details -> %s", dataType, cast.ToStringJson(salesDetailPriceBuy))
		if dataType == "sales" {
			_ = h.repo.UpdatePriceBuySalesDetail(priceUpdates)
		} else {
			//because what in sales breakdown is price buy for each qty,
			//then we have to devide it with the qty
			// for _, sales := range salesDetail {
			// 	salesDetailPriceBuy[sales.SalesDetailId] = salesDetailPriceBuy[sales.SalesDetailId] / sales.Qty
			// }
			_ = h.repo.UpdatePriceBuySalesBreakdown(priceUpdates)
		}

	}
}

func (h *hppUseCase) Test() {
	if os.Getenv("ENV") != "localhost" {
		log.IfError(fmt.Errorf("do not run testing on env: '%s' ", os.Getenv("ENV")))
		return
	}

	h.updateSalesDetailHpp("1I0383I0I34N8")
	// h.updateSalesPriceBuy("sales_breakdown", "16581KS7K9N62")
	// h.HandleStockOpname([]int{287})
	// detail := make([]models.StockOutProduct, 0)
	// detail = append(detail, models.StockOutProduct{
	// 	ProductDetailId: 37064,
	// 	Qty:             5,
	// })

	// stockPrices, _ := h.logStockOut(&detail, "production", "50")
	// log.Info("stock prices: %v", utils.SimplyToJson(stockPrices))
	panic(fmt.Errorf("test finish..."))
}

func (h *hppUseCase) publishSalesHppUpdate(salesId string) {
	topic := fmt.Sprintf("hpp-update-%s", os.Getenv("ENV"))
	err := google.PublishMessage(topic, map[string]interface{}{
		"source": "sales",
		"id":     salesId,
	})
	log.IfError(err)
}

func isInStock(source domain.HppSource) bool {
	inStocks := []domain.HppSource{domain.PurchaseConfirm}
	for _, in := range inStocks {
		if in == source {
			return true
		}
	}
	return false
}

func (h *hppUseCase) adjustNewStockPrices(productDetailIds []int, dataDetail *[]models.StockInProduct, dataId, dataType string) {
	if len(productDetailIds) == 0 {
		return
	}

	log.Info("adjustNewStockPrices, ids: %v", productDetailIds)
	detailMap := make(map[int]models.StockInProduct)
	for _, detail := range *dataDetail {
		detailMap[detail.ProductDetailId] = detail
	}

	for _, productId := range productDetailIds {
		stockInProduct := detailMap[productId]
		id, err := h.repo.InsertStockPrice(domain.Hpp{
			// StockIn:           stockInProduct.Qty,
			StockInId:         dataId,
			StockInSource:     dataType,
			Price:             stockInProduct.Price,
			StockInMetaData:   stockInProduct.MetaData,
			ProductDetailFkid: productId,
			CreatedAt:         stockInProduct.CreatedAt,
		})
		if err == nil {
			log.Info("stock price added: %v, in: %v, inId: %v #%v, createdAt: %v", id, stockInProduct.Qty, dataId, dataType, stockInProduct.CreatedAt)
			h.AdjustStockOpname(dataId, dataType, stockInProduct.Qty)
			h.UpdateStockPriceQtyIn(dataId, dataType, stockInProduct.Qty)
			// h.AdjustStockPrice(productId, stockInProduct.CreatedAt)
		}
	}

}

func (h *hppUseCase) UpdateStockPriceQtyIn(stockInId, stockInSource string, stockInNew float32) error {
	logLine := log.Line()
	defer logLine.PrintDebug()

	stockPrice, err := h.repo.FetchStockPriceByIdAndSource(stockInId, stockInSource)
	if log.IfError(err) {
		return err
	}
	if stockPrice == nil || len(*stockPrice) == 0 {
		logLine.AddLog("no stockPrice found: %v (%v)", stockInId, stockInSource)
		return nil
	}

	//compare different
	stockInOld := float32(0)
	for _, stock := range *stockPrice {
		stockInOld += stock.StockIn
	}

	diff := stockInNew - stockInOld

	//update stock_price data
	logLine.AddLog("update stockPrice of %v, old: %v, becomes: %v", (*stockPrice)[0].StockPriceId, (*stockPrice)[0].StockIn, (*stockPrice)[0].StockIn+diff)
	log.IfError(h.repo.UpdateStockPriceQtyIn((*stockPrice)[0].StockPriceId, (*stockPrice)[0].StockIn+diff))

	h.AdjustStockPrice((*stockPrice)[0].ProductDetailFkid, (*stockPrice)[0].CreatedAt)

	return nil
}

// to recalculate fifo table for particular productId, started from dateStart
func (h *hppUseCase) AdjustStockPrice(productId int, dateStart int64) error {
	logLine := log.Line()
	defer logLine.PrintDebug()

	filter := domain.StockPriceFilter{
		ProductDetailId: productId,
		DateStart:       dateStart,
	}
	allStockPrices, err := h.repo.Filter(filter).FetchStockPrice()
	if log.IfError(err) {
		return err
	}

	if allStockPrices == nil || len(*allStockPrices) == 0 {
		log.Info("no stockPrice found after %v, prodId: %v", dateStart, productId)
		return nil
	}

	logLine.AddLog("total stockPrice %v, filter: %v", len(*allStockPrices), utils.SimplyToJson(filter))
	logLine.AddLog("allStockPrices: %v", utils.SimplyToJson(allStockPrices))
	_, changes := recalculateHpp(*allStockPrices)
	if len(changes) == 0 {
		logLine.AddLog("no changes..., skip AdjustStockPrice")
		return nil
	}

	logLine.AddLog("changes: %v", utils.SimplyToJson(changes))
	stockPriceIdsMap := make(map[int]bool)
	productDetailIdsMap := make(map[int]bool)
	productDetailIds := make([]int, 0)
	for _, change := range changes {
		for _, update := range change {
			stockPriceIdsMap[update.StockPriceId] = true
			if !productDetailIdsMap[update.ProductDetailId] {
				productDetailIds = append(productDetailIds, update.ProductDetailId)
			}
			productDetailIdsMap[update.ProductDetailId] = true
		}
	}

	// productDetailIds := array.GetKeys(productDetailIdsMap)
	logLine.AddLog("productDetailIds: %v", len(productDetailIds))

	var stockPriceIds []interface{}
	for id := range stockPriceIdsMap {
		stockPriceIds = append(stockPriceIds, id)
	}

	logLine.AddLog("fetching %v stockPriceIds", len(stockPriceIds))
	stockPriceDetails, err := h.repo.FetchStockPriceDetail(stockPriceIds...)
	if log.IfError(err) {
		return err
	}

	if stockPriceDetails == nil || len(*stockPriceDetails) == 0 {
		logLine.AddLog("no stockPriceDetail of ids: %v", stockPriceIds)
		return nil
	}

	logLine.AddLog("total details data: %v", len(*stockPriceDetails))
	stockPriceDetailUnique := make([]models.StockPriceDetailEntity, 0)
	stockPriceDetailsMap := make(map[int64][]models.StockPriceDetailEntity)
	uniqueCheck := make(map[string]bool)
	for _, detail := range *stockPriceDetails {
		stockPriceDetailsMap[detail.StockPriceFKID] = append(stockPriceDetailsMap[detail.StockPriceFKID], detail)
		uniqueKey := fmt.Sprintf("%v-%v", detail.StockOutID, detail.StockOutSource)
		if _, exists := uniqueCheck[uniqueKey]; !exists {
			stockPriceDetailUnique = append(stockPriceDetailUnique, detail)
			uniqueCheck[uniqueKey] = true
		}
	}

	logLine.AddLog("total details data unique: %v", len(stockPriceDetailUnique))
	var logStockDetail strings.Builder
	for key, detail := range stockPriceDetailsMap {
		logLine.AddLog("%v has %v stockPriceDetail", key, len(detail))
		for _, row := range detail {
			logStockDetail.WriteString(fmt.Sprintf("(%v)[%v]: %v, ", row.StockPriceFKID, row.StockPriceDetailID, row.StockOut))
		}
	}

	logLine.AddLog("logStockPriceDetail %v", logStockDetail.String())
	logLine.AddLog("detailMap: %v", utils.SimplyToJson(stockPriceDetailsMap))
	stockPriceDetailNew, stockPriceDetailAdjust, stockPriceDetailRemove, stockOutUpdates := buildChanges(changes, stockPriceDetailsMap)

	//update stockPriceDetail based on changes
	logLine.AddLog("stockPriceDetailAdjust: %v", utils.SimplyToJson(stockPriceDetailAdjust))
	logLine.AddLog("stockPriceDetailNew: %v", utils.SimplyToJson(stockPriceDetailNew))
	logLine.AddLog("stockPriceDetailRemove: %v", utils.SimplyToJson(stockPriceDetailRemove))
	_, err = h.repo.AdjustStockPriceDetail(stockPriceDetailAdjust, stockPriceDetailRemove, stockPriceDetailNew)
	if logLine.PrintIfError(err) {
		return err
	}

	logLine.AddLog("stockOutUpdates: %v", utils.SimplyToJson(stockOutUpdates))
	err = h.repo.AdjustStockPriceQtyOut(stockOutUpdates)
	if logLine.PrintIfError(err) {
		return err
	}

	//update stockPriceIds stockOut, based on stockPriceDetails
	totalUpdates, err := h.repo.UpdateStockPriceOutBasedDetail(stockPriceIds...)
	log.IfError(err)
	if totalUpdates > 0 {
		log.IfError(fmt.Errorf("stockOut not match with detail, size stockPriceIds: %v, updated rows: %v, ids: %v", len(stockPriceIds), totalUpdates, stockPriceIds))
		log.Info(logLine.String())
		// log.Info("changes: %v", utils.SimplyToJson(changes))
		// log.Info("allStockPrices original: %v", utils.SimplyToJson(allStockPrices))

		allStockPricesNew, err := h.repo.Filter(filter).FetchStockPrice()
		log.IfError(err)
		log.Info("allStockPrices now: %v", utils.SimplyToJson(allStockPricesNew))

		log.Info("----- end log totalUpdates -----")
	}

	if os.Getenv("ENV") != "localhost" {
		// update products_detail (should fetch again the latest price from fifo)
		h.updateProductPriceBuy(productDetailIds...)

		// update who use the fifo (stock_out)
		// notify the updates, through pubsub
		h.updatePriceBuyOutOriginSource(stockPriceIds...)
	}

	//remove unused stock, if in=0 and out=0
	h.repo.DeleteUnUsedStockPrice()

	return nil
}

// returns: stockPriceDetailNew,
// stockPriceDetailAdjust: adjustment and new stock_price_detail (new if no stock_price_detail_id given),
// stockPriceDetailRemove,
// stockOutUpdates : map, key: stock_price_id, value: qty adjust
func buildChanges(changes map[int][]models.HppUpdate, stockPriceDetailsMap map[int64][]models.StockPriceDetailEntity) ([]map[string]interface{}, []models.StockPriceDetailEntity, []int64, map[int64]float32) {
	logLine := log.Line()
	logLine.AddLog("--- start buildChanges")
	defer logLine.PrintDebug()

	stockPriceDetailNew := make([]map[string]interface{}, 0)
	stockPriceDetailAdjust := make([]models.StockPriceDetailEntity, 0)
	stockPriceDetailRemove := make([]int64, 0)
	stockOutUpdates := make(map[int64]float32) //key: stockPriceId, value: qty adjusment

	hasErr := false
	// logLine.AddLog("-- buildChanges, changes: %v", utils.SimplyToJson(changes))
	// logLine.AddLog("-- buildChanges, stockPriceDetailsMap: %v", utils.SimplyToJson(stockPriceDetailsMap))

	outs := make(map[int]float32)

	for updatedStockPriceId, change := range changes {
		for _, update := range change {
			stockOutUpdates[int64(updatedStockPriceId)] += update.Qty //add to new stockPriceId
			stockOutUpdates[int64(update.StockPriceId)] -= update.Qty //reduce from origin

			// update update.StockPriceId to updatedStockPriceId
			details := stockPriceDetailsMap[int64(update.StockPriceId)]
			//TODO: details should be ordered based on created_at

			logLine.AddLog(">> updatedStockPriceId %v, StockPriceId %v, change %v", updatedStockPriceId, update.StockPriceId, utils.SimplyToJson(change))
			logLine.AddLog(">> details %v", utils.SimplyToJson(details))

			// logLine.AddLog("--- update %v to %v, qty: %v, size detail: %v", update.StockPriceId, updatedStockPriceId, update.Qty, len(details))
			qtyUpdate := update.Qty

			//loop through all stock_price_details, to update the stock_price_fkid
			//if go up (updatedStockPriceId < stockPriceId): loop from index 0
			//if go down: loop from last index
			start := 0
			inc := 1 //increment
			end := len(details)

			//go down loop setup
			if updatedStockPriceId > update.StockPriceId {
				// logLine.AddLog("[[loop]] go down.... %v VS %v", updatedStockPriceId, update.StockPriceId)
				start = len(details) - 1
				end = -1
				inc = -1
			}

			for i := start; i != end; i += inc { //i := len(details) - 1; i >= 0; i--
				detail := details[i]
				if detail.StockOut == 0 {
					// logLine.AddLog(">>> empty stockout, %v (%v)", update.StockPriceId, detail.StockPriceDetailID)
					continue
				}

				//split the data if stock_out in a row, greater than what needed
				if detail.StockOut > qtyUpdate {
					//we need to do 2 things: update the current qty of stock_price_detail,
					//and create new stock_price_detail with this new stockPriceFkid
					//update qty
					stockPriceDetailAdjust = append(stockPriceDetailAdjust, models.StockPriceDetailEntity{
						StockOut:           detail.StockOut - qtyUpdate,
						StockPriceDetailID: detail.StockPriceDetailID,
					})

					// logLine.AddLog("stockOutUpdates of %v : %v | current out: %v (id: %v)", detail.StockPriceFKID, (detail.StockOut - qtyUpdate), detail.StockOut, detail.StockPriceDetailID)

					if detail.StockOut-qtyUpdate <= 0 {
						stockPriceDetailRemove = append(stockPriceDetailRemove, detail.StockPriceDetailID)
						// logLine.AddLog("remove from db: %v, qty: %v", detail.StockPriceDetailID, detail.StockOut)
					}

					//if its a pending stock, use all
					if updatedStockPriceId == 0 {
						stockPriceDetailNew = append(stockPriceDetailNew, detail.ToMap())
						stockPriceDetailNew[len(stockPriceDetailNew)-1]["stock_out"] = qtyUpdate
					} else {
						stockPriceDetailAdjust = append(stockPriceDetailAdjust, detail)
						stockPriceDetailAdjust[len(stockPriceDetailAdjust)-1].StockOut = qtyUpdate
						stockPriceDetailAdjust[len(stockPriceDetailAdjust)-1].StockPriceDetailID = 0 //set 0, to insert new
						stockPriceDetailAdjust[len(stockPriceDetailAdjust)-1].StockPriceFKID = int64(updatedStockPriceId)
						// logLine.AddLog("stockOutUpdates of %v : %v | insertNew", updatedStockPriceId, qtyUpdate)
						outs[int(updatedStockPriceId)] += qtyUpdate
					}

					detail.StockOut -= qtyUpdate
					qtyUpdate = 0
					// logLine.AddLog("%v (%v) qtyNow: %v, qtyUpdate left: %v", detail.StockPriceFKID, detail.StockPriceDetailID, detail.StockOut, qtyUpdate)
				} else {
					if updatedStockPriceId == 0 {
						stockPriceDetailNew = append(stockPriceDetailNew, detail.ToMap())
						stockPriceDetailRemove = append(stockPriceDetailRemove, detail.StockPriceDetailID)
					} else {
						stockPriceDetailAdjust = append(stockPriceDetailAdjust, models.StockPriceDetailEntity{
							StockPriceFKID:     int64(updatedStockPriceId),
							StockPriceDetailID: detail.StockPriceDetailID,
							StockOut:           detail.StockOut,
						})
						outs[updatedStockPriceId] += (detail.StockOut)
					}

					// logLine.AddLog("stockOutUpdates of %v : %v", detail.StockPriceFKID, -(detail.StockOut))
					// logLine.AddLog("stockOutUpdates of %v : %v", updatedStockPriceId, detail.StockOut)

					qtyUpdate -= detail.StockOut
					detail.StockOut = 0
					// logLine.AddLog("%v (%v) qtyNow: %v, qtyUpdate left: %v", detail.StockPriceFKID, detail.StockPriceDetailID, detail.StockOut, qtyUpdate)
				}

				stockPriceDetailsMap[int64(update.StockPriceId)][i].StockOut = detail.StockOut
				if qtyUpdate <= 0 {
					logLine.AddLog("------------------ finish... at %v, stockOut: %v", i, detail.StockOut)
					break
				}
			}
			if qtyUpdate > 0 {
				// logLine.AddLog("[[[ERROR]]] (buildChanges) finish loop, but still need: %v", qtyUpdate)
				log.IfError(fmt.Errorf("(buildChanges) finish loop, but still need: %v", qtyUpdate))
				logLine.Print()
				log.Info(" --- (buildChanges) finish log ---")
				logLine.Reset()
				// hasErr = true
			}
			//TODO: remove if not testing
			// break
		}
	}

	fmt.Println("\n\n---------- outs", utils.SimplyToJson(outs))

	// logLine.AddLog("final stockPriceDetail: %v", utils.SimplyToJson(stockPriceDetailsMap))
	if hasErr {
		logLine.Print()
	}

	return stockPriceDetailNew, stockPriceDetailAdjust, stockPriceDetailRemove, stockOutUpdates
}

func uniqueStocks(stocks []models.StockInProduct) []models.StockInProduct {
	uniq := make(map[int]models.StockInProduct)
	for _, s := range stocks {
		if val, ok := uniq[s.ProductDetailId]; ok {
			val.Qty += s.Qty
			uniq[s.ProductDetailId] = val
		} else {
			uniq[s.ProductDetailId] = s
		}
	}

	res := make([]models.StockInProduct, 0, len(uniq))
	for _, val := range uniq {
		res = append(res, val)
	}
	return res
}
