package cache

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"gitlab.com/uniqdev/backend/api-report/domain"
)

var ctx = context.Background()

type cacheDb struct {
	client *redis.Client
}

func NewCacheDb(client *redis.Client) domain.CacheInterface {
	return &cacheDb{client: client}
}

// Delete implements domain.CacheInterface.
func (c *cacheDb) Delete(key string) error {
	return c.client.Del(ctx, key).Err()
}

// Get implements domain.CacheInterface.
func (c *cacheDb) Get(key string) (string, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	result, err := c.client.Get(ctx, key).Result()
	if err != nil {
		fmt.Printf("GetCacheErr '%v' Err: %v\n", key, err)
	}
	return result, err
}

// GetMap implements domain.CacheInterface.
func (c *cacheDb) GetMap(key string) (map[string]string, error) {
	return c.client.HGetAll(ctx, key).Result()
}

// GetMapArray implements domain.CacheInterface.
func (c *cacheDb) GetMapArray(keyPrefix string, ids ...interface{}) ([]map[string]string, error) {
	result := make([]map[string]string, 0)
	var errMsg strings.Builder

	for _, id := range ids {
		key := fmt.Sprintf("%s:%v", keyPrefix, id)
		data, err := c.GetMap(key)
		if err != nil {
			errMsg.WriteString(fmt.Sprintf("error get %v: %v,", key, err))
		} else {
			if len(data) > 0 {
				result = append(result, data)
			}
		}
	}

	var err error
	if errMsg.Len() > 0 {
		err = fmt.Errorf(errMsg.String())
	}
	fmt.Println(errMsg)
	return result, err
}

// Set implements domain.CacheInterface.
func (c *cacheDb) Set(key string, value interface{}, expiration time.Duration) error {
	return c.client.Set(ctx, key, value, expiration).Err()
}

// SetMap implements domain.CacheInterface.
func (c *cacheDb) SetMap(key string, value map[string]interface{}, expiration time.Duration) error {
	err := c.client.HSet(ctx, key, value).Err()
	if err != nil {
		return err
	}
	return c.client.Expire(ctx, key, expiration).Err()
}

// SetMapBatch implements domain.CacheInterface.
func (c *cacheDb) SetMapBatch(keyPrefix string, mapKey string, values []map[string]interface{}, expiration time.Duration) error {
	var errMsg strings.Builder
	for _, value := range values {
		key := fmt.Sprintf("%v:%v", keyPrefix, value[mapKey])
		err := c.SetMap(key, value, expiration)
		if err != nil {
			errMsg.WriteString(fmt.Sprintf("error write %v, %v", key, err))
		}
	}

	if errMsg.Len() > 0 {
		fmt.Printf("SetMapBatch '%s' error: %v ", keyPrefix, errMsg.String())
		return fmt.Errorf(errMsg.String())
	}
	return nil
}
