package http

import (
	v2 "github.com/gofiber/fiber/v2"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type syncHandler struct {
	domain.SyncUseCase
}

func NewHttpSyncHandler(app *v2.App, useCase domain.SyncUseCase) {
	handler := &syncHandler{useCase}
	app.Get("/module/sync", handler.Sample)
}
func (h syncHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of sync feature route")
}
