package domain

import "gitlab.com/uniqdev/backend/api-report/models"

type Product struct{}

type ProductFilter struct {
	CategoryId    []interface{}
	SubCategoryId []interface{}
	AdminId       int
}

type ProductUseCase interface{}
type ProductRepository interface {
	FetchProductByFilter(filter ProductFilter) ([]map[string]interface{}, error)
	FetchProductDetail(ids []int) ([]models.ProductDetailEntity, error)
}
