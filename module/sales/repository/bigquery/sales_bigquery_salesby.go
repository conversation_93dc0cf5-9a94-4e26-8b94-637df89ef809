package bigquery

import (
	"fmt"
	"strings"

	"github.com/mitchellh/mapstructure"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// sales by
func (s salesRepository) FetchSalesTagByOutlet(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesTagByOutlet, error) {
	sql := `SELECT sum(s.grand_total) as total, count(s.sales_id) as total_transaction, s.outlet_fkid, s.sales_tag_fkid, any_value(o.name) as outlet_name 
	from $DB.sales s 
	join $DB.outlets o on o.outlet_id=s.outlet_fkid
	where s.status='Success' and s.sales_tag_fkid is not null 
	and o.admin_fkid=@adminId
	and s.time_created BETWEEN @startDate AND @endDate
	$WHERE
	group by s.outlet_fkid, s.sales_tag_fkid `

	var sqlWhere strings.Builder
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" AND s.outlet_fkid IN UNNEST(@outletIds) ")
	}

	sqlReplacer := strings.NewReplacer(
		"$DB.", s.DbName(),
		"$WHERE", sqlWhere.String())
	sql = sqlReplacer.Replace(sql)

	params := bq.MapParam(map[string]interface{}{
		"adminId":   cast.ToInt64(user.BusinessId),
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"outletIds": request.Outlet,
	})

	var result []models.SalesTagByOutlet
	mapArray, err := s.Query(sql, params...).MapArray()
	if err != nil {
		return nil, err
	}

	err = mapstructure.Decode(mapArray, &result)
	if len(mapArray) > 0 {
		fmt.Printf("mapArr: %v, result: %v", mapArray[0], result[0])
	}

	return &result, err
}

func salesTagMapToStruct(mapArray []map[string]interface{}, result *[]models.SalesTagByOutlet) {
	log.IfError(mapstructure.Decode(mapArray, &result))
	if len(mapArray) > 0 {
		fmt.Printf("mapArr: %v, result: %v", mapArray[0], (*result)[0])
	}
}

func (s salesRepository) FetchSalesByOrderTypeGroup(user *domain.User, request *domain.SalesReportOrderTypeRequst) (*[]models.SalesTagByDate, error) {
	sql := `SELECT 
	SUM(s.grand_total) AS total, 
	s.outlet_fkid, 
	s.sales_tag_fkid, 
	ANY_VALUE(o.name) AS outlet_name, 
	FORMAT_DATE("$dateFormat", date(TIMESTAMP_MILLIS(s.time_created))) AS date
  FROM 
  $DB.sales s 
	JOIN $DB.outlets o ON o.outlet_id = s.outlet_fkid 
  WHERE 
	o.admin_fkid = @adminId
	and s.time_created BETWEEN @startDate AND @endDate
	and s.status = 'Success' 
	and s.data_status = 'on' 
	AND s.sales_tag_fkid IS NOT NULL 
	$WHERE
  GROUP BY 
	s.outlet_fkid, 
	s.sales_tag_fkid, 
	date
  `

	dateFormatMap := map[string]string{
		"monthly": "%m/%Y",
		"daily":   "%d/%m/%Y",
	}

	var sqlWhere strings.Builder
	if len(request.SalesTagId) > 0 {
		//UNNEST(@outletIds)
		sqlWhere.WriteString(" AND s.sales_tag_fkid in UNNEST(@salesTagIds) ")
	}
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" AND s.outlet_fkid IN UNNEST(@outletIds) ")
	}

	sql = strings.ReplaceAll(sql, "$dateFormat", dateFormatMap[request.GroupBy])
	sql = strings.Replace(sql, "$DB.", s.DbName(), -1)
	sql = strings.Replace(sql, "$WHERE", sqlWhere.String(), -1)

	params := bq.MapParam(map[string]interface{}{
		"adminId":     user.BusinessId,
		"startDate":   request.StartDate,
		"endDate":     request.EndDate,
		"outletIds":   request.Outlet,
		"salesTagIds": request.SalesTagId,
	})

	var result []models.SalesTagByDate
	mapArray, err := s.Query(sql, params...).MapArray()
	if err != nil {
		return nil, err
	}

	err = mapstructure.Decode(mapArray, &result)
	return &result, err
}
