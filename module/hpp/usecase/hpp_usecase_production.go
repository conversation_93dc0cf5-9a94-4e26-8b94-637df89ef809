package usecase

import (
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type ProductionData struct {
	Production       models.ProductionEntity
	ProductionDetail []models.ProductionDetailEntity
	ProductionCost   []models.ProductionCostEntity
	ProductDetail    []models.ProductDetailEntity
}

type ProductionPriceBuy struct {
	ProductDetailId int
	PriceBuy        float32
	Qty             float32
}

func (h hppUseCase) recordPriceOfProduction(productionId int) {
	var err error
	var data ProductionData

	//fetch production
	data.Production, err = h.repoPurchase.FetchProduction(productionId)
	log.IfError(err)

	//fetch production detail
	data.ProductionDetail, err = h.repoPurchase.FetchProductionDetail(productionId)
	log.IfError(err)

	//fetch production cost
	data.ProductionCost, err = h.repoPurchase.FetchProductionCost(productionId)
	log.IfError(err)

	productIds := make([]int, 0)
	for _, detail := range data.ProductionDetail {
		productIds = append(productIds, int(detail.ProductDetailFKID))
	}
	productIds = append(productIds, int(data.Production.ProductDetailFKID))

	//fetch product detail (to get price buy)
	data.ProductDetail, err = h.repoProduct.FetchProductDetail(productIds)
	log.IfError(err)

	productPriceBuy := calculatePriceBuy(data)
	log.Info("productPriceBuy: %v", utils.SimplyToJson(productPriceBuy))
	for _, product := range productPriceBuy {
		// id, err := h.repo.InsertStockPrice(domain.Hpp{
		// 	ProductDetailFkid: product.ProductDetailId,
		// 	StockIn:           product.Qty,
		// 	Price:             (product.PriceBuy),
		// 	StockInId:         cast.ToString(productionId),
		// 	StockInSource:     domain.Production.String(),
		// 	UpdatedAt:         utils.CurrentMillis(),
		// 	CreatedAt:         data.Production.DataCreated,
		// })

		// if err == nil {
		// 	log.Info("stock price added: %v, in: %v, inId: %v", id, product.Qty, productionId)
		// }
		// log.Info("[production] stock price Id: %v, prodId: %v, price: %v", id, product.ProductDetailId, product.PriceBuy)

		stockIn := models.StockInProduct{
			ProductDetailId: product.ProductDetailId,
			Qty:             product.Qty,
			Price:           product.PriceBuy,
			CreatedAt:       data.Production.DataCreated,
		}
		h.logStockIn(&[]models.StockInProduct{stockIn}, domain.Production.String(), cast.ToString(productionId))

		// stockPriceIds = append(stockPriceIds, id)
		//update price_buy in product_detail
		// h.updateProductPriceBuy(product.ProductDetailId) //remove to after loop

		//update production detail
		h.repoPurchase.UpdateProductionDetail(models.ProductionDetailEntity{
			ProductionFKID:    int64(productionId),
			ProductDetailFKID: int64(product.ProductDetailId),
			PriceBuy:          float32(product.PriceBuy),
		})
	}

	h.updateProductPriceBuy(productIds...)

	//update price_buy ingredient (get from fifo)
	for _, detail := range data.ProductionDetail {
		if detail.DetailType == models.ProductionTypeIngredient {
			prices, _ := h.logStockOut(&[]models.StockOutProduct{
				{ProductDetailId: int(detail.ProductDetailFKID), Qty: detail.Qty},
			}, "production", cast.ToString(detail.ProductionDetailID))

			totalQty := float32(0)
			total := float32(0)
			for _, price := range *prices {
				total += price.Qty * price.Price
				totalQty += price.Qty
			}

			finalPrice := total / totalQty
			log.Info("prices: %v | id: %v -> %v", len(*prices), detail.ProductionDetailID, finalPrice)
			h.repoPurchase.UpdateProductionDetail(models.ProductionDetailEntity{
				ProductionFKID:    detail.ProductFKID,
				ProductDetailFKID: detail.ProductDetailFKID,
				PriceBuy:          finalPrice,
				DetailType:        detail.DetailType,
			})
		}
	}
}

func calculatePriceBuy(data ProductionData) []ProductionPriceBuy {
	productDetailMap := make(map[int]models.ProductDetailEntity)
	for _, detail := range data.ProductDetail {
		productDetailMap[detail.ProductDetailID] = detail
	}

	//calculate total ingredient used + primary product
	//calculate total residual
	totalIngredient := float64(0)
	totalResidu := float64(0)
	totalEndProduct := float64(0)
	for _, detail := range data.ProductionDetail {
		product := productDetailMap[int(detail.ProductDetailFKID)]
		if detail.DetailType == models.ProductionTypeIngredient {
			totalIngredient += float64(detail.Qty) * safe(float64(product.PriceBuy))
		} else if detail.DetailType == models.ProductionTypeResidual {
			totalResidu += float64(detail.Qty) * safe(float64(product.PriceBuy))
		} else if detail.DetailType == models.ProductionTypeEndProduct {
			totalEndProduct += float64(detail.Qty) * safe(float64(product.PriceSell))
		}
	}
	product := productDetailMap[int(data.Production.ProductDetailFKID)]
	totalIngredient += float64(data.Production.QtyPrimary) * safe(float64(product.PriceBuy))

	//calculate cost
	totalCost := float64(0)
	for _, cost := range data.ProductionCost {
		totalCost += float64(cost.Nominal)
	}
	log.Info("(%v) total cost: %v -  size data: %v | ", data.Production.ProductionID, totalCost, len(data.ProductionCost))
	log.Info("totalEndProduct: %v, totalCost: %v, totalIngredient: %v, totalResidu: %v", totalEndProduct, totalCost, totalIngredient, totalResidu)

	//minus total ingredient with residual

	//calculate new hpp for end product
	//formula: current hpp / total end product * (total ingredient - residual)
	result := make([]ProductionPriceBuy, 0)
	for _, item := range data.ProductionDetail {
		product := productDetailMap[int(item.ProductDetailFKID)]
		if item.DetailType == models.ProductionTypeEndProduct {
			priceBuy := (safe(float64(product.PriceSell)) / totalEndProduct) * (totalCost + totalIngredient - totalResidu)
			result = append(result, ProductionPriceBuy{
				PriceBuy:        float32(priceBuy),
				ProductDetailId: int(item.ProductDetailFKID),
				Qty:             item.Qty,
			})
		} else if item.DetailType == models.ProductionTypeResidual { //also add for residu
			result = append(result, ProductionPriceBuy{
				PriceBuy:        (product.PriceBuy), //using its own price buy
				ProductDetailId: int(item.ProductDetailFKID),
				Qty:             item.Qty,
			})
		}
	}

	return result
}

func safe(num float64) float64 {
	if num == 0 {
		return 1
	}
	return num
}
