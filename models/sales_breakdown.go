package models

type SalesBreakdown struct {
	SalesDetailFkid   int     `json:"sales_detail_fkid,omitempty"`
	SalesDetailID     int     `json:"sales_detail_id,omitempty"`
	ProductDetailFkid int     `json:"product_detail_fkid,omitempty"`
	Qty               float32 `json:"qty,omitempty"`
	PriceBuy          int     `json:"price_buy,omitempty"`
}

func (s *SalesBreakdown) ToStockOutProduct() StockOutProduct {
	return StockOutProduct{
		ProductDetailId: s.ProductDetailFkid,
		Qty:             s.Qty,
		MetaData: map[string]interface{}{
			"sales_detail_id": s.SalesDetailFkid,
		},
	}
}
