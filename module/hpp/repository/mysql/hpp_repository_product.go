package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h hppRepository) FetchProductByProductDetailId(productDetailId int) (map[string]any, error) {
	sql := "select * from products p join products_detail pd on p.product_id=pd.product_fkid where pd.product_detail_id = ?"
	return db.Query(sql, productDetailId)
}

// FetchCurrentPrice implements hpp.Repository.
func (h *hppRepository) FetchCurrentPrice(productDetailIds ...int) ([]models.CurrentProductPrice, error) {
	if len(productDetailIds) == 0 {
		log.Info("FetchCurrentPrice, no productDetailIds given...")
		return []models.CurrentProductPrice{}, nil
	}

	sql := `SELECT sp.product_detail_fkid as product_detail_id, sp.price, sp.stock_price_id 
	from (
	select min(created_at) cr, product_detail_fkid  from stock_price 
		where ((stock_out < stock_in) or (stock_out > stock_in and stock_in_id is null)) 
		and deleted_at is null
		and product_detail_fkid in @ids
		group by product_detail_fkid
	) spj 
	join stock_price sp on sp.product_detail_fkid=spj.product_detail_fkid and sp.created_at=spj.cr 	`

	sql, params := db.MapParam(sql, map[string]any{
		"ids": productDetailIds,
	})

	var result []models.CurrentProductPrice
	err := h.db.Query(sql, params...).Model(&result)
	return result, err
}

func (h *hppRepository) UpdatePriceBuyProducts(productPrice ...models.CurrentProductPrice) error {
	var err error
	for _, update := range productPrice {
		_, err = h.db.Update("products_detail", map[string]any{
			"price_buy": int(update.Price),
		}, "product_detail_id = ?", update.ProductDetailId)
		if err != nil {
			log.Info("error update price_buy, productDetailId: %v, to: %v, err : %v", update.ProductDetailId, update.Price, err)
		}
	}
	return err
}
