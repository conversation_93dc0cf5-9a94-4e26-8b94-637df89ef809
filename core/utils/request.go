package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	net "net/url"
	"os"
	"path/filepath"
	"strings"
)

type HttpRequest struct {
	Method           string
	Url              string
	Header           map[string]interface{}
	PostRequest      PostRequest
	MultipartRequest MultipartRequest
	BinaryFilePath   string
	DisableLog       bool
}

type PostRequest struct {
	Body interface{}
	Form map[string]string
}

type MultipartRequest struct {
	FilePath  string
	FileParam string
	Form      map[string]string
}

func (req HttpRequest) Execute() ([]byte, error) {
	return request(req)
}

func (req HttpRequest) Log(msg string, param ...interface{}) {
	if !req.DisableLog {
		fmt.Printf(msg, param...)
	}
}

func request(request HttpRequest) ([]byte, error) {
	//validate method
	isValidMethod := false
	availableMethods := []string{"POST", "GET", "DELETE", "PUT"}
	for _, method := range availableMethods {
		if method == request.Method {
			isValidMethod = true
			break
		}
	}

	if !isValidMethod {
		return nil, fmt.Errorf("invalid request method: %s", request.Method)
	}

	var body string
	if request.Header == nil {
		request.Header = make(map[string]interface{})
	}

	if len(request.PostRequest.Form) > 0 {
		data := net.Values{}
		for key, value := range request.PostRequest.Form {
			data.Set(key, value)
		}
		body = data.Encode()
		request.Header["Content-Type"] = "application/x-www-form-urlencoded"
	} else if request.BinaryFilePath != "" {
		fileStr, err := os.ReadFile(request.BinaryFilePath)
		if err != nil {
			fmt.Println("reading file error", err)
			return nil, err
		}
		body = string(fileStr)
	} else if request.PostRequest.Body != nil {
		dataJson, err := json.Marshal(request.PostRequest.Body)
		if err != nil {
			fmt.Println("Parse map to json error : ", err)
		}
		request.Header["Content-Type"] = "application/json"

		request.Log("[SEND] body: [%s]\n", string(dataJson))
		body = string(dataJson)
	}

	fmt.Println("[SEND] header: ", request.Header)

	var req *http.Request
	var err error

	if request.MultipartRequest.FilePath != "" {
		req, err = createMultipartRequest(request)
	} else {
		if body == "" {
			req, err = http.NewRequest(request.Method, request.Url, nil)
		} else {
			req, err = http.NewRequest(request.Method, request.Url, strings.NewReader(body))
		}
	}

	if err != nil {
		request.Log("Creating http request error : %v\n", err)
		return nil, err
	}

	for key, value := range request.Header {
		req.Header.Add(key, ToString(value))
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		request.Log("Request to '%s' error %v \n", request.Url, err)
		return nil, err
	}
	defer resp.Body.Close()

	bodyResp, err := io.ReadAll(resp.Body)
	request.Log("[SEND] (%s) %d - %s\n", req.Method, resp.StatusCode, req.URL)
	return bodyResp, err
}

func createMultipartRequest(request HttpRequest) (*http.Request, error) {
	file, err := os.Open(request.MultipartRequest.FilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	part, err := writer.CreateFormFile(request.MultipartRequest.FileParam, filepath.Base(request.MultipartRequest.FilePath))
	if err != nil {
		return nil, err
	}
	_, err = io.Copy(part, file)

	for key, val := range request.MultipartRequest.Form {
		_ = writer.WriteField(key, val)
	}
	err = writer.Close()
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", request.Url, body)
	req.Header.Set("Content-Type", writer.FormDataContentType())
	return req, err
}
