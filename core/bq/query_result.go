package bq

import (
	"errors"
	"fmt"
	"reflect"

	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func (s *SqlResult) Map() (map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr[0], nil
		} else {
			return map[string]interface{}{}, nil
		}
	}
	return nil, fmt.Errorf("model is not single map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) MapArray() ([]map[string]interface{}, error) {
	if s.Error != nil {
		return nil, s.Error
	}

	if m, ok := s.Data.([]map[string]interface{}); ok {
		return m, nil
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			return arr, nil
		} else {
			return []map[string]interface{}{}, nil
		}
	}
	return nil, fmt.Errorf("model is not multi map, %v", reflect.TypeOf(s.Data))
}

func (s *SqlResult) Model(model interface{}) error {
	if s.Error != nil {
		return s.Error
	}

	//return mapstructure.Decode(s.Result[0], model)
	//e, _ := json.Marshal(s.Data)
	//fmt.Println(string(e))

	if reflect.TypeOf(model).Kind() != reflect.Ptr {
		return errors.New("model should be pointer")
	}

	if m, ok := s.Data.(map[string]interface{}); ok {
		return cast.MapToStruct(m, model)
	} else if arr, ok := s.Data.([]map[string]interface{}); ok {
		if len(arr) > 0 {
			if reflect.ValueOf(model).Elem().Kind() == reflect.Slice {
				//jsonStr, err := json.Marshal(arr)
				//if err != nil {
				//	return err
				//}
				//return json.Unmarshal(jsonStr, model)
				return cast.MapArrayToStruct(arr, model)
			} else {
				return cast.MapToStruct(arr[0], model)
			}
		} else {
			return nil
		}
	}
	return errors.New("model is not single map")
}
