package usecase

import (
	"fmt"
	"sort"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

func (s *salesUseCase) FetchSalesAnalysisByDate(request domain.DataTableRequest, dataType string, user domain.User) (map[string]any, error) {
	return nil, nil
}

func (s *salesUseCase) FetchSalesAnalysisByHour(request domain.DataTableRequest, dataType string, user domain.User) (map[string]any, error) {
	if request.TimeZone == 0 {
		request.TimeZone = 25200
	}

	var result []map[string]any
	if dataType == "nominal" {
		result, _ = s.repoMiddleware.FetchSalesAnalysisByNominal("hour", request, cast.ToInt(user.BusinessId))
	} else {
		result, _ = s.repoMiddleware.FetchSalesAnalysisByQty("hour", request, cast.ToInt(user.BusinessId))
	}

	if len(result) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	productIdMap := make(map[string]any)
	outletIdMap := make(map[string]any)
	for _, row := range result {
		productIdMap[cast.ToString(row["product_fkid"])] = 0
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
	}

	timeStart := time.Now()
	products, err := s.repoPrimary.FetchProductByIds(array.FromMapKey(productIdMap)...)
	outlets, err := s.repoPrimary.FetchOutletByIds(array.FromMapKey(outletIdMap)...)
	if err != nil || len(products) == 0 || len(outlets) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	productsMap := make(map[int]map[string]any)
	for _, row := range products {
		productsMap[cast.ToInt(row["product_id"])] = row
	}

	outletsMap := make(map[string]map[string]any)
	for _, row := range outlets {
		outletsMap[cast.ToString(row["outlet_id"])] = row
	}

	fmt.Printf("get products and outlet data took: %v\n", time.Since(timeStart))

	defaultRowValues := make(map[string]any)
	for i := 0; i <= 23; i++ {
		defaultRowValues[fmt.Sprintf("qty%d", i+1)] = 0
	}

	data := make([]map[string]any, 0)

	total := 0
	dataPerRow := array.Copy(defaultRowValues)
	timeStart = time.Now()
	for i, row := range result {
		day := cast.ToInt(row["day"])

		dataPerRow[fmt.Sprintf("qty%d", day)] = cast.ToInt(row["total"])
		total += cast.ToInt(row["total"])

		if i == len(result)-1 || row["product_detail_fkid"] != result[i+1]["product_detail_fkid"] {
			product := productsMap[cast.ToInt(row["product_fkid"])]
			dataPerRow["product_name"] = product["name"]
			dataPerRow["category"] = product["category_name"]
			dataPerRow["subcategory"] = product["subcategory_name"]
			dataPerRow["outlet_name"] = outletsMap[cast.ToString(row["outlet_fkid"])]["name"]
			dataPerRow["product_detail_fkid"] = row["product_detail_fkid"]
			dataPerRow["total"] = total
			data = append(data, array.Copy(dataPerRow))

			dataPerRow = array.Copy(defaultRowValues)
			total = 0
		}
	}

	fmt.Printf("modify data took: %v\n", time.Since(timeStart))
	return map[string]any{"data": data}, nil
}

func (s *salesUseCase) FetchSalesAnalysisByDay(request domain.DataTableRequest, dataType string, user domain.User) (map[string]any, error) {
	if request.TimeZone == 0 {
		request.TimeZone = 25200
	}

	var result []map[string]any
	if dataType == "nominal" {
		result, _ = s.repoMiddleware.FetchSalesAnalysisDayByNominal(request, cast.ToInt(user.BusinessId))
	} else {
		result, _ = s.repoMiddleware.FetchSalesAnalysisDayByQty(request, cast.ToInt(user.BusinessId))
	}

	if len(result) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	productIdMap := make(map[string]any)
	outletIdMap := make(map[string]any)
	for _, row := range result {
		productIdMap[cast.ToString(row["product_fkid"])] = 0
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
	}

	timeStart := time.Now()
	products, err := s.repoPrimary.FetchProductByIds(array.FromMapKey(productIdMap)...)
	outlets, err := s.repoPrimary.FetchOutletByIds(array.FromMapKey(outletIdMap)...)
	if err != nil || len(products) == 0 || len(outlets) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	productsMap := make(map[int]map[string]any)
	for _, row := range products {
		productsMap[cast.ToInt(row["product_id"])] = row
	}

	outletsMap := make(map[string]map[string]any)
	for _, row := range outlets {
		outletsMap[cast.ToString(row["outlet_id"])] = row
	}

	fmt.Printf("get products and outlet data took: %v\n", time.Since(timeStart))

	defaultRowValues := make(map[string]any)
	for i := 0; i < 7; i++ {
		defaultRowValues[fmt.Sprintf("qty%d", i+1)] = 0
	}

	data := make([]map[string]any, 0)

	total := 0
	dataPerRow := array.Copy(defaultRowValues)
	timeStart = time.Now()
	for i, row := range result {
		day := cast.ToInt(row["day"])
		if day == 0 { //day 0 : monday, move it to day 7
			day = 7
		}

		dataPerRow[fmt.Sprintf("qty%d", day)] = cast.ToInt(row["total"])
		total += cast.ToInt(row["total"])

		if i == len(result)-1 || row["product_detail_fkid"] != result[i+1]["product_detail_fkid"] {
			product := productsMap[cast.ToInt(row["product_fkid"])]
			dataPerRow["product_name"] = product["name"]
			dataPerRow["category"] = product["category_name"]
			dataPerRow["subcategory"] = product["subcategory_name"]
			dataPerRow["outlet_name"] = outletsMap[cast.ToString(row["outlet_fkid"])]["name"]
			dataPerRow["product_detail_fkid"] = row["product_detail_fkid"]
			dataPerRow["total"] = total
			data = append(data, array.Copy(dataPerRow))

			dataPerRow = array.Copy(defaultRowValues)
			total = 0
		}
	}

	fmt.Printf("modify data took: %v\n", time.Since(timeStart))
	return map[string]any{"data": data}, nil
}

func (s *salesUseCase) FetchSalesAnalysisByOutlet(request domain.DataTableRequest, dataType string, user domain.User) (map[string]any, error) {
	var result []map[string]any
	if dataType == "nominal" {
		// result, _ = s.repoMiddleware.FetchSalesAnalysisOutletByNominal(request, cast.ToInt(user.BusinessId))
		result, _ = s.FetchSalesAnalysisOutletByNominalAsync(request, cast.ToInt(user.BusinessId))
	} else {
		result, _ = s.repoMiddleware.FetchSalesAnalysisOutletByQty(request, cast.ToInt(user.BusinessId))
	}

	if len(result) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	//productIdMap := make(map[string]any)
	productDetailIdMap := make(map[string]any)
	outletIdMap := make(map[string]any)

	//if user specify the outlet, make sure its returned
	//the value is set to 0 by default
	for _, id := range request.Outlet {
		outletIdMap[cast.ToString(id)] = 0
	}

	for i, row := range result {
		//productIdMap[cast.ToString(row["product_fkid"])] = 0
		productDetailIdMap[cast.ToString(row["product_detail_fkid"])] = 0
		outletIdMap[cast.ToString(row["outlet_fkid"])] = 0
		if dataType == "nominal" {
			result[i]["total"] = cast.ToInt(row["subtotal"]) - cast.ToInt(row["discount"]) + cast.ToInt(row["gratuity_tax"]) - cast.ToInt(row["gratuity_discount"]) - cast.ToInt(row["promotion"])
		}
	}

	log.Info("fetching product data....")
	//products, err := s.repoPrimary.FetchProductByIds(array.FromMapKey(productIdMap)...)
	products, err := s.repoPrimary.FetchProductByDetailIds(array.FromMapKey(productDetailIdMap)...)
	log.IfError(err)

	log.Info("fetching outlet data....")
	outlets, err := s.repoPrimary.FetchOutletByIds(array.FromMapKey(outletIdMap)...)
	if log.IfError(err) || len(products) == 0 || len(outlets) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	productsMap := make(map[int]map[string]any)
	for _, row := range products {
		productsMap[cast.ToInt(row["product_detail_id"])] = row
	}

	//transform data to map, to make easy for searching data
	outletsMap := make(map[string]map[string]any)
	for _, row := range outlets {
		outletsMap[cast.ToString(row["outlet_id"])] = row
	}

	data := make([]map[string]any, 0)
	log.Info("total products: %d, productMap: %d", len(products), len(productsMap))

	total := 0
	totalByOutlet := array.Copy(outletIdMap)

	for i, row := range result {
		result[i]["p_name"] = productsMap[cast.ToInt(row["product_detail_fkid"])]["name"]
	}

	// log.Info("result --> %s", utils.SimplyToJson(result))

	fmt.Println(">> BEFORE....")
	timeStartSort := time.Now()
	// for _, row := range result {
	// 	// log.Info("name: %s", productsMap[cast.ToInt(row["product_detail_fkid"])]["name"])
	// }

	sort.Slice(result, func(i, j int) bool {
		x1, x2 := cast.ToString(result[i]["p_name"]), cast.ToString(result[j]["p_name"])
		tmp := []string{x1, x2}
		sort.Strings(tmp)
		return tmp[0] == x1
	})

	log.Info("%d data sorting took: %v", len(result), time.Since(timeStartSort))
	fmt.Println(">> AFTER....")
	for i, row := range result {
		total += cast.ToInt(row["total"])
		totalByOutlet[cast.ToString(row["outlet_fkid"])] = cast.ToInt(row["total"]) + cast.ToInt(totalByOutlet[cast.ToString(row["outlet_fkid"])])
		//log.Info("name: %s", productsMap[cast.ToInt(row["product_detail_fkid"])]["name"])

		if i == len(result)-1 || row["p_name"] != result[i+1]["p_name"] {
			outletGroup := make([]map[string]any, 0)

			//transform outlet_id to name
			for k, v := range totalByOutlet {
				if outlet, ok := outletsMap[k]; ok {
					outletGroup = append(outletGroup, map[string]any{
						"name":   cast.ToString(outlet["name"]),
						dataType: v,
					})
					//delete(totalByOutlet, k)
				}
			}

			product := productsMap[cast.ToInt(row["product_detail_fkid"])]
			totalByOutlet["outlet"] = outletGroup
			totalByOutlet["total"] = total
			totalByOutlet["product_name"] = product["name"]
			totalByOutlet["category"] = product["category_name"]
			totalByOutlet["subcategory"] = product["subcategory_name"]
			data = append(data, totalByOutlet)

			//reset to default (containing all outlets, with zero value)
			totalByOutlet = array.Copy(outletIdMap)
			total = 0
		}
	}

	return map[string]any{"data": data}, nil
}

func (s *salesUseCase) FetchSalesAnalysisOutletByNominalAsync(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	//should return these keys: 'product_detail_fkid', 'product_fkid', 'outlet_fkid',
	// 'total_grandtotal', 'subtotal', 'discount', 'gratuity_tax', 'gratuity_discount'

	sales, err := s.repoPrimary.FetchSalesAnalysisOutletByNominalFast(request, adminId)
	if log.IfError(err) {
		return nil, err
	}

	//get product_detail_fkid from sales
	productDetailIds := make([]int64, 0)
	for _, sale := range sales {
		productDetailIds = append(productDetailIds, cast.ToInt64(sale["product_detail_fkid"]))
	}

	repoParam := domain.SalesReportRequest{
		ProductDetailIds: productDetailIds,
		StartDate:        request.StartDate,
		EndDate:          request.EndDate,
		Outlet:           request.Outlet,
	}

	type discountResult struct {
		discounts map[int64]int64
		err       error
	}

	type taxResult struct {
		taxMap      map[int64]int64
		discountMap map[int64]int64
		err         error
	}

	type promoResult struct {
		promoMap map[int64]int64
		err      error
	}

	discountChan := make(chan discountResult)
	taxChan := make(chan taxResult)
	promoChan := make(chan promoResult)

	// Go routine 1 - Fetch and map discount data
	go func() {
		productDetailDiscount, err := s.repoPrimary.FetchSalesDetailDiscount(domain.User{
			BusinessId: cast.ToString(adminId),
		}, repoParam)

		if err != nil {
			discountChan <- discountResult{err: err}
			return
		}

		discounts := make(map[int64]int64)
		for _, discount := range productDetailDiscount {
			discounts[cast.ToInt64(discount["product_detail_fkid"])] += cast.ToInt64(discount["total"])
		}
		discountChan <- discountResult{discounts: discounts}
	}()

	// Go routine 2 - Fetch and map tax data
	go func() {
		productDetailTax, err := s.repoPrimary.FetchSalesDetailTax(adminId, repoParam)

		if err != nil {
			taxChan <- taxResult{err: err}
			return
		}

		taxMap := make(map[int64]int64)
		discountMap := make(map[int64]int64)
		for _, tax := range productDetailTax {
			switch tax["category"] {
			case "tax", "service":
				taxMap[cast.ToInt64(tax["product_detail_fkid"])] += cast.ToInt64(tax["total"])
			case "voucher", "discount":
				discountMap[cast.ToInt64(tax["product_detail_fkid"])] += cast.ToInt64(tax["total"])
			}
		}
		taxChan <- taxResult{taxMap: taxMap, discountMap: discountMap}
	}()

	// Go routine 3 - Fetch and map promotion data
	go func() {
		promoRows, err := s.repoPrimary.FetchSalesDetailPromotions(repoParam)
		if err != nil {
			promoChan <- promoResult{err: err}
			return
		}

		promos := make(map[int64]int64)
		for _, row := range promoRows {
			promos[cast.ToInt64(row["product_detail_fkid"])] += cast.ToInt64(row["total"])
		}
		promoChan <- promoResult{promoMap: promos}
	}()

	// Wait for all goroutines to complete
	discountRes := <-discountChan
	if log.IfError(discountRes.err) {
		log.Info("error fetching discount: %v", discountRes.err)
		return nil, discountRes.err
	}
	discountMap := discountRes.discounts

	taxRes := <-taxChan
	if taxRes.err != nil {
		log.Info("error fetching tax: %v", taxRes.err)
		return nil, taxRes.err
	}
	gratuityTaxMap := taxRes.taxMap
	gratuityDiscountMap := taxRes.discountMap

	promoRes := <-promoChan
	if log.IfError(promoRes.err) {
		log.Info("error fetching promotions: %v", promoRes.err)
		return nil, promoRes.err
	}
	promoMap := promoRes.promoMap

	log.Info("discountMap: %v, gratuityTaxMap: %v, gratuityDiscountMap: %v", len(discountMap), len(gratuityTaxMap), len(gratuityDiscountMap))
	log.Info("discount: %v, gratuityTax: %v, gratuityDiscount: %v", cast.ToString(discountMap), cast.ToString(gratuityTaxMap), cast.ToString(gratuityDiscountMap))

	//adjust sales data based on the discount, tax and promotions
	for _, sale := range sales {
		productDetailId := cast.ToInt64(sale["product_detail_fkid"])
		sale["discount"] = cast.ToInt64(sale["discount"]) + discountMap[productDetailId]
		sale["gratuity_tax"] = gratuityTaxMap[productDetailId]
		sale["gratuity_discount"] = gratuityDiscountMap[productDetailId]
		sale["promotion"] = promoMap[productDetailId]
	}

	//sort by product_detail_fkid
	sort.Slice(sales, func(i, j int) bool {
		return cast.ToInt64(sales[i]["product_detail_fkid"]) < cast.ToInt64(sales[j]["product_detail_fkid"])
	})

	log.Info("finish fetching sales analysis outlet by nominal async....")
	return sales, nil
}

func (s *salesUseCase) FetchSalesAnalysisByPromotion(request domain.DataTableRequest, dataType string, user domain.User) (map[string]any, error) {
	if request.StartDate == 0 {
		request.StartDate = time.Now().AddDate(0, 0, -7).Unix() * 1000
	}
	if request.EndDate == 0 {
		request.EndDate = time.Now().Unix() * 1000
	}

	var repo domain.SalesRepository = s.pickRepo(request.EndDate)

	var salesPromo []map[string]any
	var err error
	if dataType == "qty" {
		salesPromo, err = repo.FetchSalesAnalysisPromotionByQty(request, cast.ToInt(user.BusinessId))
	} else {
		// salesPromo, err = repo.FetchSalesAnalysisPromotionByNominal(request, cast.ToInt(user.BusinessId))
		salesPromo, err = s.FetchSalesAnalysisPromotionByNominal(request, cast.ToInt(user.BusinessId))
	}
	if err != nil || len(salesPromo) == 0 {
		return map[string]any{"data": []any{}}, err
	}

	//hold the default value for all outlets
	outletIdTotalMap := make(map[string]any)

	//if user specify the outlet, make sure its returned
	//the value is set to 0 by default
	for _, id := range request.Outlet {
		outletIdTotalMap[cast.ToString(id)] = 0
	}

	promoIdMap := make(map[int]bool)
	for _, raw := range salesPromo {
		promoIdMap[cast.ToInt(raw["promotion_fkid"])] = true
		outletIdTotalMap[cast.ToString(raw["outlet_id"])] = 0
	}

	promotions, err := s.repoPrimary.FetchPromotion(array.GetKeys(promoIdMap)...)
	if err != nil {
		return map[string]any{"data": []any{}}, err
	}

	outlets, err := s.repoPrimary.FetchOutletByIds(array.FromMapKey(outletIdTotalMap)...)
	if err != nil {
		return map[string]any{"data": []any{}}, err
	}

	//transform data to map, to make easy for searching data
	outletsMap := make(map[string]map[string]any)
	for _, row := range outlets {
		outletsMap[cast.ToString(row["outlet_id"])] = row
	}

	fmt.Println(promotions)
	promotionMap := make(map[int]map[string]any)
	for _, raw := range promotions {
		promotionMap[cast.ToInt(raw["promotion_id"])] = raw
	}

	data := make([]map[string]any, 0)
	totalByOutlet := array.Copy(outletIdTotalMap)
	total := 0

	for i, row := range salesPromo {
		totalByOutlet[cast.ToString(row["outlet_id"])] = cast.ToInt(row["total"])
		total += cast.ToInt(row["total"])

		if i == len(salesPromo)-1 || row["promotion_fkid"] != salesPromo[i+1]["promotion_fkid"] {
			outletGroup := make([]map[string]any, 0)

			//transform outlet_id to name
			for k, v := range totalByOutlet {
				if outlet, ok := outletsMap[k]; ok {
					outletGroup = append(outletGroup, map[string]any{
						"name":   cast.ToString(outlet["name"]),
						dataType: v,
					})
				}
			}

			dataRaw := make(map[string]any)
			dataRaw["outlet"] = outletGroup
			dataRaw["total"] = total
			dataRaw["promo_name"] = promotionMap[cast.ToInt(row["promotion_fkid"])]["name"]
			data = append(data, dataRaw)

			//reset to default (containing all outlets, with zero value)
			totalByOutlet = array.Copy(outletIdTotalMap)
			total = 0
		}
	}

	result := map[string]any{
		"data": data,
	}

	return result, nil
}

func (s *salesUseCase) FetchSalesAnalysisPromotionByNominal(request domain.DataTableRequest, adminId int) ([]map[string]any, error) {
	repo := s.pickRepo(request.EndDate)
	salesPromo, err := repo.FetchSalesAnalysisPromotionByNominal(request, adminId)
	log.IfError(err)
	// log.Info("salesPromo : %v", cast.ToStringJson(salesPromo))

	salesDetailPromo, err := repo.FetchSalesAnalysisPromotionDetailByNominal(request, adminId)
	log.IfError(err)

	formatKey := func(row map[string]any) string {
		return fmt.Sprintf("%v#%v", row["promotion_fkid"], row["outlet_id"])
	}

	//mapping data
	salesPromoMap := make(map[string]map[string]any)
	for _, detail := range salesPromo {
		key := formatKey(detail)
		salesPromoMap[key] = detail
	}

	//mapping data, for easy search
	salesDetailPromoMap := make(map[string]map[string]any)
	for _, detail := range salesDetailPromo {
		key := formatKey(detail)
		salesDetailPromoMap[key] = detail
		//if data not exist if sales_promotion, add it
		if _, ok := salesPromoMap[key]; !ok {
			// log.Info("salesPromoMap not found: %v", key)
			salesPromoNew := array.Copy(detail)
			salesPromoNew["total"] = 0 //we have to set total to 0, as we will make addition later
			salesPromo = append(salesPromo, salesPromoNew)
		}
	}

	//adding sales_promotion with sales_promotion_detail (merge by the same promoId)
	for i, data := range salesPromo {
		key := formatKey(data)
		// log.Info("salesPromo : %v, total Detail: %v, key: %v", cast.ToStringJson(salesPromo[i]), cast.ToInt(salesDetailPromoMap[key]["total"]), key)
		salesPromo[i]["total"] = cast.ToInt(salesPromo[i]["total"]) + cast.ToInt(salesDetailPromoMap[key]["total"])
	}

	// //remove 0 total promo
	// for i := len(salesPromo) - 1; i >= 0; i-- {
	// 	if cast.ToInt(salesPromo[i]["total"]) == 0 {
	// 		salesPromo = slices.Delete(salesPromo, i, i+1)
	// 	}
	// }

	// log.Info("salesPromo : %v", cast.ToStringJson(salesPromo))

	return salesPromo, err
}
