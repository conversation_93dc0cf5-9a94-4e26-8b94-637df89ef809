package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h hppRepository) FetchPurchaseProductById(purchaseProductId int) (map[string]interface{}, error) {
	sql := "select * from purchase_products where purchase_products_id = ?"
	return db.Query(sql, purchaseProductId)
}

func (h hppRepository) FetchPurchaseProductByConfirmId(purchaseConfirmId ...int) (*[]models.PurchaseProductStockIn, error) {
	if len(purchaseConfirmId) == 0 {
		return nil, nil
	}

	sql := `
SELECT
    pp.*, pc.date_created as confirm_at, purchase_confrim_id, pc.qty_arive
FROM
    purchase_products pp
JOIN purchase_confrim pc ON
    pc.purchase_product_fkid = pp.purchase_products_id
WHERE
    purchase_confrim_id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": purchaseConfirmId,
	})

	var result []models.PurchaseProductStockIn
	err := h.db.Query(sql, params...).Model(&result)
	return &result, err
	// return db.Query(sql, purchaseConfirmId)
}

func (h hppRepository) FetchPurchaseConfirmByPurchaseProduct(purchaseProductId int) ([]map[string]interface{}, error) {
	sql := "SELECT * from purchase_confrim where purchase_product_fkid = ? "
	return db.QueryArray(sql, purchaseProductId)
}

func (h hppRepository) FetchPurchaseRetur(returId int) (map[string]interface{}, error) {
	sql := "select * from retur_products where retur_product_id = ?"
	return db.Query(sql, returId)
}
