package domain

import "gitlab.com/uniqdev/backend/api-report/module/stock_card/models"

type StockCard struct{}
type StockCardUseCase interface {
	FetchStockCard(adminId string, timeStart int64, timeEnd int64, timeDiff int64, outletId string, productId string, variantId string) ([]models.StockCardJsonResponse, error)
}
type StockCardRepositoryMiddleware interface {
	FetchProductDetail(adminId string, outletId string, productId string, variantId string) (map[string]interface{}, error)
	FetchLastOpname(productdetailId string, timemillisMax int64) (int64, error)
	FetchOpenAfterOpname(productdetailId string, timemillisLastOpname int64, timeStart int64) (float32, error)

	FetchProductionPrimary(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchProductionDetail(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchStockOpname(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchRetur(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchPurchaseConfirm(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSales(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchTransferOut(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchTransferIn(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSpoil(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)

	FetchTransaction(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
}
type StockCardRepositoryMysql interface {
	FetchProductDetail(adminId string, outletId string, productId string, variantId string) (map[string]interface{}, error)
	FetchLastOpname(productdetailId string, timemillisMax int64) (int64, error)
	FetchOpenAfterOpname(productdetailId string, timemillisLastOpname int64, timeStart int64) (float32, error)

	FetchProductionPrimary(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchProductionDetail(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchStockOpname(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchRetur(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchPurchaseConfirm(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSales(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchTransferOut(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchTransferIn(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSpoil(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)

	FetchTransaction(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
}
type StockCardRepositoryBigquery interface {
	FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
	FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error)
}
