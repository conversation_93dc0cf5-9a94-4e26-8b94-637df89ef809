package bigquery

import (
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/domain"
)

type dashboardRepository struct {
	bq.Repository
}

// FetchTopSales implements domain.DashboardRepository.
func (d *dashboardRepository) FetchTopSales(param domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	panic("unimplemented")
}

func NewBigQueryDashboardRepository(client *bigquery.Client) domain.DashboardRepository {
	return &dashboardRepository{bq.Repository{Client: client}}
}

func (d dashboardRepository) FetchNetSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			o.outlet_id,
			min(o.name) AS outlet_name,
			sum(CASE WHEN (s.status='Success' and s.data_status='on' and s.payment NOT IN ('COMPLIMENT','DUTY MEALS') ) THEN s.grand_total ELSE 0 END) AS sales
		FROM %ssales s
		LEFT JOIN %soutlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid=%s
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY outlet_id
	`, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)

	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchNetSalesByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	return d.Query("").MapArray()
}

func (d dashboardRepository) FetchPaidPurchaseBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchUnPaidPurchase(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchDebtPayment(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchDebt(adminId string, timeStart int, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchComplimentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			o.outlet_id,
			min(o.name) AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment ='COMPLIMENT', s.grand_total,0)) AS sum_grandtotal
		FROM %ssales s
		LEFT JOIN %soutlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=%s
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY o.outlet_id
	`, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchDutyMealsBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			o.outlet_id,
			min(o.name) AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment ='DUTY MEALS', s.grand_total,0)) AS sum_grandtotal
		FROM %ssales s
		LEFT JOIN %soutlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=%s
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY o.outlet_id
	`, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchTotalSalesPerPaymentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, paymentMethods ...string) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	payments := strings.Join(paymentMethods, "','")
	sql := fmt.Sprintf(`
		SELECT
			o.outlet_id,
			min(o.name) AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on', s.grand_total,0)) AS sum_grandtotal,
			trim(s.payment) as payment 
		FROM %ssales s
		LEFT JOIN %soutlets o ON o.outlet_id = s.outlet_fkid
		WHERE o.admin_fkid=%s
		AND s.time_created >= %d
		AND s.time_created <= %d		
		AND trim(s.payment) in ('%s')
		%s
		GROUP BY o.outlet_id, s.payment
	`, dbName, dbName, adminId, timeStart, timeEnd, payments, whereInOutlet)

	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchFreeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			s.outlet_fkid AS outlet_id,
			min(o.name) AS outlet_name,
			sum(case when (s.status='Success' and s.data_status='on' and sd.discount = sd.sub_total) THEN sd.discount ELSE 0 END) AS free
		FROM %ssales_detail sd
		LEFT JOIN %ssales s ON s.sales_id = sd.sales_fkid
		LEFT JOIN %soutlets o ON s.outlet_fkid = o.outlet_id
		WHERE sd.discount > 0
		AND o.admin_fkid = %s
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY s.outlet_fkid
	`, dbName, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchDiscSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			s.outlet_fkid AS outlet_id,
			min(o.name) AS outlet_name,
			sum(s.discount) AS sales_discount
		FROM %ssales s
		LEFT JOIN %soutlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid = %s
		AND s.time_created >= %d
		AND s.time_created <= %d
		AND s.data_status = 'on'
		AND s.status = 'Success'
		%s
		GROUP BY s.outlet_fkid
	`, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchDiscItemBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`SELECT s.outlet_fkid                             AS outlet_id,
       min(o.name)                                    AS outlet_name,
       sum(CASE WHEN (s.status='Success' and s.data_status='on' and sd.discount < sd.sub_total) THEN sd.discount - coalesce(sv.v_disc, 0) ELSE 0 END) AS discount_item
FROM %ssales_detail sd
         LEFT JOIN %ssales s ON s.sales_id = sd.sales_fkid
         LEFT JOIN %soutlets o ON s.outlet_fkid = o.outlet_id
         left join (select sum(v.discount) as v_disc, v.sales_detail_fkid
                    from %ssales_void v
                    group by sales_detail_fkid) sv on sv.sales_detail_fkid = sd.sales_detail_id
WHERE o.admin_fkid = %s
  AND s.time_created >= %d
  AND s.time_created <= %d
  %s
GROUP BY s.outlet_fkid`, dbName, dbName, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchDiscGratuityBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			s.outlet_fkid AS outlet_id,
			min(o.name) AS outlet_name,
			sum(st.total) AS sales_tax_discount
		FROM %ssales_tax st
		LEFT JOIN %ssales s ON s.sales_id = st.sales_fkid
		LEFT JOIN %soutlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN %sgratuity g ON g.gratuity_id = st.tax_fkid
		WHERE o.admin_fkid = %s
		AND s.time_created >= %d
		AND s.time_created <= %d
		AND g.tax_category = 'discount'
		AND s.data_status = 'on'
		AND s.status = 'Success'
		%s
		GROUP BY s.outlet_fkid
	`, dbName, dbName, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchVoucherBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			s.outlet_fkid AS outlet_id,
			min(o.name) AS outlet_name,
			sum(CASE WHEN(s.data_status='on' and s.status='Success' and g.tax_category='voucher') THEN st.total ELSE 0 END) AS sales_tax_voucher
		FROM %ssales_tax st
		LEFT JOIN %ssales s ON s.sales_id = st.sales_fkid
		LEFT JOIN %soutlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN %sgratuity g ON g.gratuity_id = st.tax_fkid
		WHERE o.admin_fkid = %s 
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY s.outlet_fkid
	`, dbName, dbName, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchVoucherSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT
			s.outlet_fkid AS outlet_id,
			min(o.name) AS outlet_name,
			sum(IF(s.data_status='on' AND s.status='Success', s.voucher, 0)) AS sales_voucher
		FROM %ssales s
		LEFT JOIN %soutlets o ON s.outlet_fkid = o.outlet_id
		WHERE o.admin_fkid = %s 
		AND s.time_created >= %d
		AND s.time_created <= %d
		%s
		GROUP BY s.outlet_fkid
	`, dbName, dbName, adminId, timeStart, timeEnd, whereInOutlet)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchPendingIncomeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND ts.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	sql := fmt.Sprintf(`
		SELECT 
			o.outlet_id,
			min(o.name) AS outlet_name,
			sum(ts.grand_total) AS pending_income
		FROM %stmp_sales ts
		LEFT JOIN %soutlets o ON ts.outlet_fkid = o.outlet_id
		WHERE ts.time_created >= %d
		AND ts.time_created <= %d
		AND o.admin_fkid = %s
		AND ts.status = 'pending'
		%s
		GROUP BY ts.outlet_fkid
		ORDER BY pending_income DESC
	`, dbName, dbName, timeStart, timeEnd, adminId, whereInOutlet)

	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchTotalSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInOutlet = "AND s.outlet_fkid IN (" + outletId + ") "
	}

	dbName := d.DbName()
	querySalesInformation := db.GetSQLRaw(`
		SELECT
			s.sales_id,
			min(s.outlet_fkid) as outlet_id,
			min(o.name) AS outlet_name,
			min(s.qty_customers) as qty_customers,
			min(s.grand_total) as grand_total,
			(sum(sd.qty) - IFNULL(sum(sv.qty),0)) AS qty_clean
		FROM  `+dbName+`sales s
		LEFT JOIN `+dbName+`outlets o ON s.outlet_fkid = o.outlet_id 
		LEFT JOIN `+dbName+`sales_detail sd ON sd.sales_fkid = s.sales_id 
		LEFT JOIN (select sum(qty) as qty, sales_detail_fkid from `+dbName+`sales_void group by sales_detail_fkid) sv
                            ON sd.sales_detail_id = sv.sales_detail_fkid
		WHERE o.admin_fkid= ?
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND s.status = 'Success'
		AND s.data_status='on'
		AND trim(s.payment) NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP BY sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	sql := fmt.Sprintf(`
		SELECT
			outlet_id,

			min(outlet_name) as outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (` + querySalesInformation + `) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)

	fmt.Println(sql)
	return d.Query(sql).MapArray()
}

func (d dashboardRepository) FetchItemSalesGroupSubCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchItemSalesGroupCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchItemSalesGroupType(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error) {
	panic("implement me")
}

func (d dashboardRepository) FetchBankById(id ...interface{}) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (d dashboardRepository) FetchSalesByMedia(request domain.DashboardRequest, user domain.User) ([]map[string]interface{}, error) {
	sql := `select sp.method, sum(sp.total) as total, spb.bank_fkid, 
	date(TIMESTAMP_MILLIS(any_value(s.time_created + @diff))) as created_at, any_value(o.name) as outlet_name,
	spb.bank_fkid
	from [db].sales_payment sp 
	join [db].sales s on s.sales_id=sp.sales_fkid
	join [db].outlets o on s.outlet_fkid=o.outlet_id
	left join [db].sales_payment_bank spb on spb.sales_payment_fkid=sp.payment_id
	left join payment_media_bank pmb on pmb.bank_id=spb.bank_fkid
	where s.status = 'Success'
	and o.admin_fkid=@adminId
	[where]
	group by sp.method, spb.bank_fkid, s.outlet_fkid, date(TIMESTAMP_MILLIS((s.time_created + @diff)))
	order by created_at `

	whereQuery := ""
	if len(request.OutletIds) > 0 {
		whereQuery = " AND s.outlet_fkid in UNNEST(@outletIds) "
	}

	sql = strings.Replace(sql, "[db].", d.DbName(), -1)
	sql = strings.Replace(sql, "[where]", whereQuery, 1)

	params := bq.MapParam(map[string]interface{}{
		"startDate": request.TimeStart,
		"endDate":   request.TimeEnd,
		"adminId":   cast.ToInt(user.BusinessId),
		"outletIds": request.OutletIds,
		"diff":      request.TimeDiff,
		// "shiftIds":  request.Shift,
	})

	return d.Query(sql, params...).MapArray()
}
