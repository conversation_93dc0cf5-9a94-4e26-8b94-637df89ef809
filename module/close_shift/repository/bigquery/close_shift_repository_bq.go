package bigquery

import (
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type closeShiftRepository struct {
	bq.Repository
}

func NewBigQueryDashboardRepository(client *bigquery.Client) domain.CloseShiftRepository {
	return &closeShiftRepository{bq.Repository{Client: client}}
}

// FetchPayShift implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchPayShift(request domain.SalesReportRequest, user domain.User) (models.PayShift, error) {
	return models.PayShift{}, fmt.Errorf("not implemented")
}

// FetchPromoRecapProduk implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchPromoRecapProduk(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	return models.PromoRecapTotal{}, fmt.Errorf("not implemented")
}

// FetchPromoRecapShift implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchPromoRecapShift(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	return models.PromoRecapTotal{}, fmt.Errorf("not implemented")
}

// FetchRefundShiftRecap implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchRefundShiftRecap(request domain.SalesReportRequest, user domain.User) (models.RefundShiftRecap, error) {
	return models.RefundShiftRecap{}, fmt.Errorf("not implemented")
}

// FetchSalesDetailShift implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchSalesDetailShift(request domain.SalesReportRequest, user domain.User) (models.SalesDetailShift, error) {
	return models.SalesDetailShift{}, fmt.Errorf("not implemented")
}

// FetchSalesRecapShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchSalesRecapShift(request domain.SalesReportRequest, user domain.User) (models.SalesRecapShift, error) {
	// Construct query similar to PHP model
	query := `SELECT 
                SUM(s.grand_total) AS grand_total,  
                SUM(s.discount) AS discount_sales,
                SUM(s.qty_customers) AS pax,
                SUM(s.voucher) AS voucher_sales, 
                COUNT(s.sales_id) AS bill
            FROM [db].sales s 
            JOIN [db].open_shift os ON os.open_shift_id = s.open_shift_fkid
            JOIN [db].outlets o ON o.outlet_id = s.outlet_fkid
            WHERE s.status = 'success' AND s.data_status = 'on' 
			AND o.admin_fkid = @adminId AND os.time_open BETWEEN @dateStart AND @dateEnd `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN UNNEST(@shiftIds) "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN UNNEST(@outletIds) "
	}

	query = strings.ReplaceAll(query, "[db].", c.DbName())
	params := bq.MapParam(map[string]interface{}{
		"adminId":   cast.ToInt(user.BusinessId),
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	var result models.SalesRecapShift
	err := c.Query(query, params...).Model(&result)
	log.IfError(err)
	return result, err
}

// FetchTaxShift implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchTaxShift(request domain.SalesReportRequest, user domain.User) (models.TaxShift, error) {
	return models.TaxShift{}, fmt.Errorf("not implemented")
}

// FetchVoidShiftRecap implements domain.CloseShiftRepository.
func (*closeShiftRepository) FetchVoidShiftRecap(request domain.SalesReportRequest, user domain.User) (models.VoidShiftRecap, error) {
	return models.VoidShiftRecap{}, fmt.Errorf("not implemented")
}
