package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/go-sql-driver/mysql"
	"gitlab.com/uniqdev/backend/api-report/core/log"
)

func QueryArrayContext(ctx context.Context, sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	db := GetConn()
	start := time.Now()

	rows, err := executeQuery(db, ctx, sqlQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	tableData, err := processRows(rows)
	if err != nil {
		return nil, err
	}

	logQueryPerformance(sqlQuery, args, start, len(tableData))
	return tableData, nil
}

func QueryArray(sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()
	return QueryArrayContext(ctx, sqlQuery, args...)
}

func Query(sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
	db := GetConn()
	start := time.Now()

	ctx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	defer cancel()

	rows, err := executeQuery(db, ctx, sqlQuery, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	entryData, err := processSingleRow(rows)
	if err != nil {
		return nil, err
	}

	logQueryPerformance(sqlQuery, args, start, 1)
	return entryData, nil
}

func executeQuery(db *sql.DB, ctx context.Context, sqlQuery string, args ...interface{}) (*sql.Rows, error) {
	ctxPrepare, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	stmt, err := db.PrepareContext(ctxPrepare, sqlQuery)
	if err != nil {
		fmt.Println("prepare statement err: ", err, ", sql: ", sqlQuery)
		return nil, err
	}
	defer stmt.Close()

	rows, err := stmt.QueryContext(ctx, args...)
	if err != nil {
		handleQueryError(err, sqlQuery, args...)
		return nil, err
	}

	LogQueryStart(sqlQuery, args...)
	return rows, nil
}

func processRows(rows *sql.Rows) ([]map[string]interface{}, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, errors.New("sql column error: " + err.Error())
	}

	tableData := make([]map[string]interface{}, 0)
	values := make([]sql.RawBytes, len(columns))
	scanArgs := make([]interface{}, len(columns))
	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		if err := rows.Scan(scanArgs...); err != nil {
			return nil, errors.New("sql scan error: " + err.Error())
		}

		entry := make(map[string]interface{})
		for i, col := range values {
			entry[columns[i]] = string(col)
		}
		tableData = append(tableData, entry)
	}

	if err := rows.Err(); err != nil {
		return nil, errors.New("sql rows error: " + err.Error())
	}

	return tableData, nil
}

func handleQueryError(err error, sqlQuery string, args ...interface{}) {
	log.IfError(fmt.Errorf("%v query err: %v \n>> %v", log.GetCaller("repository"), err, getSQLRaw(sqlQuery, args...)))
	fmt.Println("query err: ", strings.ReplaceAll(getSQLRaw(sqlQuery, args...), "\n", " "))
	if driverErr, ok := err.(*mysql.MySQLError); ok {
		if driverErr.Number == 1065 {
			fmt.Println("error 1065")
		}
	}
}

func LogQueryStart(sqlQuery string, args ...interface{}) {
	if os.Getenv("ENV") == "localhost" && os.Getenv("LOG_SQL") != "false" {
		fmt.Println("\n>> START QUERY : ", getSQLRaw(sqlQuery, args...))
	}
}

func logQueryPerformance(sqlQuery string, args []interface{}, start time.Time, rowCount int) {
	duration := time.Since(start)
	if os.Getenv("env") == "localhost" || duration > 60*time.Second {
		fmt.Printf("\n>> QUERY : %s\n>> took: %v | total rows: %d\n", getSQLRaw(sqlQuery, args...), duration, rowCount)
	}

	if duration > 15*time.Second {
		log.IfError(fmt.Errorf("%s > query took %v \n>> %v", log.GetCaller("_repository"), duration, strings.ReplaceAll(getSQLRaw(sqlQuery, args...), "\n", " ")))
	}
}

func processSingleRow(rows *sql.Rows) (map[string]interface{}, error) {
	columns, err := rows.Columns()
	if err != nil {
		return nil, errors.New("sql column error: " + err.Error())
	}

	values := make([]interface{}, len(columns))
	scanArgs := make([]interface{}, len(columns))
	for i := range values {
		scanArgs[i] = &values[i]
	}

	if !rows.Next() {
		return nil, nil // No rows found
	}

	if err := rows.Scan(scanArgs...); err != nil {
		return nil, nil // Error scanning row, return nil as before
	}

	entryData := make(map[string]interface{}, len(columns))
	for i, col := range columns {
		v := values[i]
		if b, ok := v.([]byte); ok {
			entryData[col] = string(b)
		} else {
			entryData[col] = v
		}
	}

	return entryData, nil
}
