package usecase

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"path/filepath"
	"testing"

	_ "github.com/go-sql-driver/mysql"
	"github.com/golang-migrate/migrate/v4" // install this package
	"github.com/golang-migrate/migrate/v4/database/mysql"
	_ "github.com/golang-migrate/migrate/v4/source/file"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite" // install this package
	dbRepo "gitlab.com/uniqdev/backend/api-report/core/mysql"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// Mock database name for the test
const databaseTestName = "pos-test"

type MySQLTestSuite struct {
	suite.Suite
	db     *sql.DB
	m      *migrate.Migrate
	dbRepo dbRepo.Repository
}

// Run before all tests start
func (s *MySQLTestSuite) SetupSuite() {
	fmt.Println("SetupSuite, connect to db...")
	// Connect mock database  // Setup migration  // Run migrate up
	var err error

	// Prepare SQL connection for run the test
	s.db, err = sql.Open("mysql",
		"root:example@tcp(172.18.0.2:3306)/"+databaseTestName+"?parseTime=true&loc=Asia%2FJakarta&charset=utf8mb4&collation=utf8mb4_unicode_ci")
	require.NoError(s.T(), err)

	// Migrate up (create all tables)
	migrationPath, err := filepath.Abs(filepath.Join(filepath.Dir(""), "../../../", "database/mysql/migrations"))
	fmt.Println("migration path: ", migrationPath)
	require.NoError(s.T(), err)

	// db driver
	driver, err := mysql.WithInstance(s.db, &mysql.Config{DatabaseName: databaseTestName})
	require.NoError(s.T(), err)

	// Init migrate
	// s.m, err = migrate.NewWithDatabaseInstance(migrationPath, databaseTestName, driver)
	s.m, err = migrate.NewWithDatabaseInstance("file:///workspace/api-report/database/mysql/migrations", "mysql", driver)
	require.NoError(s.T(), err)
	require.NotNil(s.T(), s.m)
	require.NoError(s.T(), s.m.Up())

	fmt.Println("database test initialized...")
	s.dbRepo = dbRepo.NewInstance(s.db)
}

// Run after each test finished
func (s *MySQLTestSuite) TearDownTest() {
	// Truncate all tables
	data, err := s.dbRepo.Query("select * from stock_price").MapArray()
	require.NoError(s.T(), err)
	fmt.Println("data: ", data)
}

// Run after all tests has finished
func (s *MySQLTestSuite) TearDownSuite() {
	// Run migrate down
	fmt.Println("dropping tables..")
	require.NoError(s.T(), s.m.Down())
}

func TestMySQLTestSuite(t *testing.T) {
	suite.Run(t, new(MySQLTestSuite))
}

func (s *MySQLTestSuite) TestAdjustFifo() {
	//insert to stock_price
	jsonStockPrices := `[{"stock_price_id":1,"product_detail_fkid":1,"stock_in":10,"stock_out":10,"price":1000,"stock_in_id":"756","stock_in_source":"purchase_confirm","updated_at":1702960317000,"created_at":1702960317000},{"stock_price_id":2,"product_detail_fkid":1,"stock_in":20,"stock_out":20,"price":2000,"stock_in_id":"758","stock_in_source":"purchase_confirm","updated_at":1702960317000,"created_at":1702960317000},{"stock_price_id":3,"product_detail_fkid":1,"stock_in":10,"stock_out":0,"price":2000,"stock_in_id":"760","stock_in_source":"stock_opname","updated_at":1702960317000,"created_at":1702960317000}]`
	var stockPrices []models.StockPriceEntity
	err := json.Unmarshal([]byte(jsonStockPrices), &stockPrices)
	require.NoError(s.T(), err)
	for _, stockPrice := range stockPrices {
		_, err = s.dbRepo.Insert("stock_price", stockPrice.ToMap())
		require.NoError(s.T(), err)
	}

}
