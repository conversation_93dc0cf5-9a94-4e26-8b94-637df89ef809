package controller

import (
	"encoding/json"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func Login(ctx *fasthttp.RequestCtx) {
	userId := ctx.FormValue("user_id")
	userPswd := ctx.FormValue("user_secret")

	data, err := db.Table("admin").Where("email = ?", string(userId)).First()
	if utils.CheckErr(err) {
		return
	}

	apiResp := models.ApiResponse{Status: false, Message: "Invalid Credential!"}

	if len(data) > 0 {
		if utils.CheckPasswordHash(string(userPswd), data["password"].(string)) {
			auth := auth.InitJWTAuth()
			token := auth.GenerateToken(map[string]interface{}{
				"userId":     data["admin_id"],
				"other_data": "sample_data",
			})
			apiResp.Status = true
			apiResp.Data = token
			apiResp.Message = "Success"
		}
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(apiResp)
}
