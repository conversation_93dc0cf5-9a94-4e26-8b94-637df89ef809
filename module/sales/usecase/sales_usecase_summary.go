package usecase

import (
	"strings"
	"sync"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

func (s salesUseCase) FetchSalesSummary(user domain.User, request domain.SalesReportRequest) (map[string]any, error) {
	var repo domain.SalesRepository
	if request.EndDate > repository.GetMaxTimeSync() {
		repo = s.repoPrimary
	} else {
		repo = s.repoReplica
	}

	result := make(map[string]any)

	log.Info("param : %s", cast.ToStringJson(request))

	summarySalesChan := make(chan []map[string]any)
	summaryItemChan := make(chan map[string]any)
	summaryPromoChan := make(chan map[string]any)
	summaryPromoItemChan := make(chan map[string]any)
	summaryGratuityChan := make(chan []map[string]any)
	summaryPaymentChan := make(chan []map[string]any)

	var wg sync.WaitGroup

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan []map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummarySales(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summarySalesChan)

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummaryItem(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summaryItemChan)

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummaryPromo(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summaryPromoChan)

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummaryPromoItem(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summaryPromoItemChan)

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan []map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummaryGratuity(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summaryGratuityChan)

	// wg.Add(1)
	go func(w *sync.WaitGroup, resultChan chan []map[string]any) {
		// defer w.Done()
		result, err := repo.FetchSummaryPayment(user, request)
		log.IfError(err)
		resultChan <- result
	}(&wg, summaryPaymentChan)

	// wg.Wait()

	summarySales := <-summarySalesChan
	summaryItem := <-summaryItemChan
	summaryPromo := <-summaryPromoChan
	summaryPromoItem := <-summaryPromoItemChan
	summaryGratuity := <-summaryGratuityChan
	summaryPayment := <-summaryPaymentChan

	if len(summarySales) == 0 {
		return result, nil
	}

	summarySalesRefund := make(map[string]any)
	summarySalesSuccess := make(map[string]any)
	for _, sales := range summarySales {
		if strings.ToLower(cast.ToString(sales["status"])) == "success" {
			summarySalesSuccess = sales
		} else {
			summarySalesRefund = sales
		}
	}

	gratuityByCategory := make(map[string]int)
	for _, gratuity := range summaryGratuity {
		gratuityByCategory[cast.ToString(gratuity["category"])] = cast.ToInt(gratuity["total"])
	}

	paymentByMedia := make(map[string]int) //'CASH','CARD','PIUTANG','DUTY MEALS','COMPLIMENT','VOUCHER','FREE','CASH PIUTANG','CARD PIUTANG'
	for _, payment := range summaryPayment {
		paymentByMedia[cast.ToString(payment["method"])] += cast.ToInt(payment["total"])
	}

	if cast.ToInt(summarySalesSuccess["pax"]) == 0 {
		summarySalesSuccess["pax"] = summarySalesSuccess["total_bill"]
	}

	result["discountSales"] = cast.ToInt(summaryItem["discount"]) + cast.ToInt(summarySalesSuccess["discount"]) + gratuityByCategory["discount"]
	result["totDis"] = result["discountSales"]
	result["voucher"] = cast.ToInt(summarySalesSuccess["voucher"]) + gratuityByCategory["voucher"]
	result["promo"] = cast.ToInt(summaryPromo["total"]) + cast.ToInt(summaryPromoItem["total"])
	result["service"] = gratuityByCategory["service"]
	result["tax"] = gratuityByCategory["tax"]
	result["cash"] = paymentByMedia["CASH"]
	result["card"] = paymentByMedia["CARD"]
	result["piutang"] = paymentByMedia["PIUTANG"]
	result["duty_meals"] = paymentByMedia["DUTY MEALS"]
	result["pax"] = summarySalesSuccess["pax"]
	if pax := cast.ToInt(result["pax"]); pax == 0 {
		//avoid divide by zero
		result["pax"] = 1
	}
	result["bill"] = summarySalesSuccess["total_bill"]
	result["average"] = cast.ToInt(summarySalesSuccess["grand_total"]) / cast.ToInt(result["pax"])
	result["averageBill"] = cast.ToInt(summarySalesSuccess["grand_total"]) / cast.ToInt(result["bill"])
	result["billRefund"] = summarySalesRefund["total_bill"]
	result["grandRefund"] = cast.ToInt(summarySalesRefund["grand_total"])
	result["total"] = cast.ToInt(summarySalesSuccess["grand_total"])
	result["sales"] = cast.ToInt(summarySalesSuccess["grand_total"]) + cast.ToInt(result["totDis"]) + cast.ToInt(result["promo"])
	result["totalMedia"] = cast.ToInt(result["cash"]) + cast.ToInt(result["card"]) + cast.ToInt(result["piutang"])
	result["compliment"] = paymentByMedia["COMPLIMENT"]
	result["stlDis"] = cast.ToInt(result["total"]) - cast.ToInt(result["service"]) - cast.ToInt(result["tax"])
	result["subVoid"] = 0
	result["qty_void"] = 0

	log.Info("grandTotal: %v, totDis: %v, promo: %v", summarySalesSuccess["grand_total"], result["totDis"], result["promo"])

	return result, nil
}
