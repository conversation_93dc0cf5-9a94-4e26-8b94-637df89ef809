package db

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
)

func InsertQuery(table string, data map[string]interface{}) (string, []interface{}) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?) "
	return query, values
}

func WhereAnd(data map[string]interface{}) (string, []interface{}) {
	query := ""
	values := make([]interface{}, 0)
	for k, v := range data {
		query += fmt.Sprintf("%s = ? AND ", k)
		values = append(values, v)
	}

	return query[:len(query)-4], values
}

func WhereIn(size int) string {
	whereIn := strings.Repeat("?,", size)
	whereIn = strings.TrimRight(whereIn, ",")
	return fmt.Sprintf("(%s)", whereIn)
}

func MapParam(sql string, params map[string]any) (string, []interface{}) {
	for key, v := range params {
		// if v == nil {
		// 	continue
		// }
		sql = strings.Replace(sql, fmt.Sprintf("$%s", key), cast.ToString(v), -1)
	}

	r, _ := regexp.Compile("@[a-zA-Z]+")

	result := make([]interface{}, 0)
	for {
		key := r.FindString(sql)
		if key != "" { //_, ok := params[key]; key != "" && ok
			key = strings.Replace(key, "@", "", 1)
			sql = strings.Replace(sql, fmt.Sprintf("(@%s)", key), fmt.Sprintf("@%s", key), 1)
			if s := reflect.ValueOf(params[key]); s.Kind() == reflect.Slice {
				arrLenght := 0
				for i := 0; i < s.Len(); i++ {
					if values := reflect.ValueOf(s.Index(i).Interface()); values.Kind() == reflect.Slice {
						for j := 0; j < values.Len(); j++ {
							result = append(result, values.Index(j).Interface())
							arrLenght += 1
						}
					} else {
						result = append(result, s.Index(i).Interface())
						arrLenght += 1
					}
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), WhereIn(arrLenght), 1)
			} else {
				if params[key] != nil {
					result = append(result, params[key])
				} else {
					fmt.Printf("Map Data with key '%s' Not Found! \n", key)
				}
				sql = strings.Replace(sql, fmt.Sprintf("@%s", key), " ? ", 1)
			}
		} else {
			break
		}
	}

	return sql, result
}
