package models

type ProductDetailEntity struct {
	ProductDetailID    int         `json:"product_detail_id,omitempty"`
	ProductFkid        int         `json:"product_fkid,omitempty"`
	OutletFkid         int         `json:"outlet_fkid,omitempty"`
	PriceBuy           float32     `json:"price_buy,omitempty"`
	PriceSell          int         `json:"price_sell,omitempty"`
	TransferMarkupType string      `json:"transfer_markup_type,omitempty"`
	TransferMarkup     int         `json:"transfer_markup,omitempty"`
	DataModified       int64       `json:"data_modified,omitempty"`
	VariantFkid        interface{} `json:"variant_fkid,omitempty"`
	Stock              string      `json:"stock,omitempty"`
	StockQty           int         `json:"stock_qty,omitempty"`
	DataStatus         string      `json:"data_status,omitempty"`
}

type CurrentProductPrice struct {
	ProductDetailId int     `json:"product_detail_id,omitempty"`
	Price           float32 `json:"price,omitempty"`
	StockPriceId    int64   `json:"stock_price_id,omitempty"`
}

func (cpp CurrentProductPrice) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"product_detail_id": cpp.ProductDetailId,
		"price":             cpp.Price,
		"stock_price_id":    cpp.StockPriceId,
	}
}
