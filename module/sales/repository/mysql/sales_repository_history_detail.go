package mysql

import (
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

// FetchSalesDetail implements domain.SalesRepositoryPrimary.
func (s *salesRepository) FetchSalesDetail(user domain.User, request domain.SalesReportRequest) ([]map[string]interface{}, error) {
	sql := `SELECT sd.sales_detail_id,
	coalesce(sd.employee_fkid, s.employee_fkid) as operator_id, s.employee_fkid,
	s.display_nota as nomor_nota, s.member_fkid, 
	o.name as outlet_name, 
	s.customer_name, 
	sd.qty, sd.price, sd.discount, 
	s.grand_total, s.status, 
	sd.sales_detail_id, sd.product_fkid, sd.product_detail_fkid,
	s.sales_id, s.voucher_info, s.discount_info, s.time_created,
	os.shift_fkid
  FROM 
	sales_detail sd 
	JOIN sales s ON s.sales_id = sd.sales_fkid 
	JOIN outlets o ON o.outlet_id = s.outlet_fkid 
	JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid 
  WHERE 
	o.admin_fkid = @adminId
	AND sd.sales_detail_id > @offsetId
	AND s.time_created BETWEEN @startDate AND @endDate 
	$WHERE
  ORDER BY 
	sd.sales_detail_id ASC 
  LIMIT @limit `

	var sqlWhere strings.Builder
	if len(request.Outlet) > 0 {
		sqlWhere.WriteString(" AND s.outlet_fkid in @outletIds ")
	}
	if len(request.Shift) > 0 {
		sqlWhere.WriteString(" AND os.shift_fkid in @shiftIds ")
	}

	sql = strings.Replace(sql, "$WHERE", sqlWhere.String(), 1)
	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":   user.BusinessId,
		"startDate": request.StartDate,
		"endDate":   request.EndDate,
		"outletIds": request.Outlet,
		"shiftIds":  request.Shift,
		"offsetId":  request.OffsetId,
		"limit": 200,
	})

	// return s.QueryCache("SalesDetail", sql, params...).MapArray()
	return db.QueryArray(sql, params...)
}

func (s *salesRepository) FetchSumSalesDetailTaxPerCategory(ids ...interface{}) ([]map[string]interface{}, error) {
	sql := `SELECT sales_detail_fkid,
	SUM(if(category = 'discount', total, 0)) as discount,
	SUM(if(category = 'tax', total, 0)) as tax,
	SUM(if(category = 'service', total, 0)) as service,
	SUM(if(category = 'voucher', total, 0)) as voucher
FROM
	sales_detail_tax
WHERE
	sales_detail_fkid in @ids
	GROUP BY sales_detail_fkid`

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})
	return db.QueryArray(sql, params...)
}
