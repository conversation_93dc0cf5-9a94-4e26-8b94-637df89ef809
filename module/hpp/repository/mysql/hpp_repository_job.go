package mysql

import (
	"gitlab.com/uniqdev/backend/api-report/core/db"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

// FetchAnomalyMismatchStockOut implements hpp.Repository.
func (h *hppRepository) FetchAnomalyMismatchStockOut() (*[]models.AnomalyMismatchStockOut, error) {
	sql := `SELECT tmp.*, (tmp.all_out - tmp.stock_out) as diff from (
		SELECT sp.stock_price_id, sp.stock_in_source,sum(spd.stock_out) all_out, sp.stock_out, sp.stock_in,
		GROUP_CONCAT(DISTINCT spd.stock_out_source) as sources 
		 from stock_price sp 
		join stock_price_detail spd on sp.stock_price_id=spd.stock_price_fkid
		group by spd.stock_price_fkid
		HAVING all_out != sp.stock_out and abs(all_out - sp.stock_out) > 1) tmp 
		order by diff desc `

	var result []models.AnomalyMismatchStockOut
	err := h.db.Query(sql).Model(&result)
	return &result, err
}

// FetchAnomalyOverStockOut implements hpp.Repository.
func (h *hppRepository) FetchAnomalyOverStockOut() (*models.AnomalyOverStockOut, error) {
	sql := `SELECT count(*) as count, 
	GROUP_CONCAT(DISTINCT stock_in_source) as sources, avg (stock_out-stock_in) avg_diff 
	from stock_price where stock_out > stock_in and stock_in_id != ''`
	var result models.AnomalyOverStockOut
	err := h.db.Query(sql).Model(&result)
	return &result, err
}

// FetchAnomalyOverStockOutDetail implements hpp.Repository.
func (h *hppRepository) FetchAnomalyOverStockOutDetail(productIds ...int) (*domain.Hpp, error) {
	sql := `SELECT 
	sp.*
	from stock_price sp 
	join products_detail pd on pd.product_detail_id=sp.product_detail_fkid
	join products p on p.product_id=pd.product_fkid
	where stock_out > stock_in and stock_in_id != '' 
	and created_at > 1706029200000
	and (stock_out-stock_in) >= 1
	order by 
	updated_at desc `

	if len(productIds) > 0 {
		sql += " and product_detail_fkid in (@ids) "
	}
	sql += " limit 1 "

	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": productIds,
	})

	var result domain.Hpp
	err := h.db.Query(sql, params...).Model(&result)
	return &result, err
}
