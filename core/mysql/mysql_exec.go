package mysql

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"runtime"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	dbUtil "gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/constant"
)

func (db *Repository) QueryCache(cacheKey, sql string, args ...any) (result *SqlResult) {
	if db.CacheDb == nil {
		fmt.Println("[WARNING] cache does'nt set for ", cacheKey)
		_, file, no, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%v:%v cache is not initialized", file, no))
		return db.Query(sql, args...)
	}

	key := fmt.Sprintf("%s_%s_%s", constant.GetEnv(), cacheKey, utils.ConcatData(args, "-"))
	dataCache, err := db.CacheDb.Get(key)
	log.Info("from cache of %v | size %v | err: %v", key, len(dataCache), err)

	if err == nil && len(dataCache) > 0 {
		var cacheMap []map[string]any
		err := json.Unmarshal([]byte(dataCache), &cacheMap)
		log.IfError(err)
		if err == nil && len(cacheMap) > 0 {
			log.Info("success get %v from cache", key)
			return &SqlResult{
				Data: cacheMap,
			}
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), 120*time.Second)
	defer cancel()
	result = db.QueryContext(ctx, sql, args...)

	//cache data
	log.Info("got from db %v, isEmpty: %v", key, result.IsEmpty())
	if result.Error == nil && !result.IsEmpty() {
		go db.CacheDb.Set(key, utils.SimplyToJson(result.Data), 24*14*time.Hour)
		log.Info("save cache %v", key)
	}

	return result
}

// cacheKey: unique key for the database, will automatically append by env name,
// keyField: a key in database query, should present in select and used as where,
// sql: sql query which should contains @ids keyword in it. example: 'where product_id in @ids'
func (db *Repository) QueryCacheList(cacheKey, keyField string, ids []any, cacheDurationMinute int, sql string, argMap map[string]any) (result *SqlResult) {
	argMapKey := "ids"
	if argMap == nil {
		argMap = map[string]any{
			argMapKey: ids,
		}
	}

	//default query
	sqlQuery, params := dbUtil.MapParam(sql, argMap)
	cacheKey = fmt.Sprintf("%s_%s", constant.GetEnv(), cacheKey)

	if db.CacheDb == nil {
		fmt.Println("[WARNING] cache not initialized for ", cacheKey)
		return db.Query(sqlQuery, params...)
	}

	//ids should be given in argument parameter
	// if _, ok := argMap[argMapKey]; len(argMap) > 0 && !ok {
	// 	fmt.Println("[WARNING] can not use QueryCacheList, ids not present in argMap")
	// 	return db.Query(sqlQuery, params...)
	// }

	if !strings.Contains(sql, "@ids") {
		fmt.Printf("[WARNING] can not use QueryCacheList, %s not present in your query\n", argMapKey)
		fmt.Println("query: ", sql)
		return db.Query(sqlQuery, params...)
	}

	resultCached, err := db.CacheDb.GetMapArray(cacheKey, ids...)
	log.Info("cache %v - got from cached: %v, allIds: %v | err: %v", cacheKey, len(resultCached), len(ids), err)
	idsLeft := array.RemoveDuplicateIds(resultCached, keyField, ids...)
	log.Info("cache %s - should get from mysql: %v", cacheKey, len(idsLeft))

	if len(resultCached) == len(ids) && len(idsLeft) > 0 {
		fmt.Println("ids: ", utils.SimplyToJson(ids))
		fmt.Println("cached: ", utils.SimplyToJson(resultCached))
		fmt.Println("idsLeft: ", utils.SimplyToJson(idsLeft))
	}

	if len(idsLeft) == 0 {
		log.Info("cache %v got all %v", cacheKey, len(resultCached))
		return &SqlResult{
			Data: resultCached,
		}
	}

	argMap["ids"] = idsLeft
	sqlQuery, params = dbUtil.MapParam(sql, argMap) //rebuild query and params
	resultDb, err := dbUtil.QueryArray(sqlQuery, params...)
	if err != nil {
		return &SqlResult{
			Error: err,
		}
	}

	go db.CacheDb.SetMapBatch(cacheKey, keyField, resultDb, time.Duration(cacheDurationMinute)*time.Minute)
	resultFinal := append(array.ChangeMapType(resultCached), resultDb...)
	return &SqlResult{
		Data: resultFinal,
	}
}

func (db *Repository) QueryContext(ctx context.Context, sql string, args ...any) (result *SqlResult) {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		if duration > 20*time.Second {
			log.Error("slow query detected (%v) at %v: %s", duration, log.GetCaller("repository"), strings.ReplaceAll(getSQLRaw(sql, args...), "\n", " "))
		}
	}()
	tableData := make([]map[string]any, 0)
	result = &SqlResult{Data: tableData}
	dbUtil.LogQueryStart(sql, args...)

	rows, err := db.Conn.Query(sql, args...)
	if err != nil {
		result.Error = err
		_, file, no, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s#%d [SQL Errror] %s", file, no, getSQLRaw(sql, args...)))
		return
	}

	result.SqlQuery = getSQLRaw(sql, args...)

	defer func() {
		err := rows.Close()
		if err != nil {
			fmt.Println("closing sql row error")
		}
	}()

	columns, err := rows.Columns()
	if err != nil {
		result.Error = err
		return
	}

	count := len(columns)
	values := make([]any, count)
	scanArgs := make([]any, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			result.Error = err
			return
		}

		entry := make(map[string]any)
		for i, col := range columns {
			v := values[i]
			if b, ok := v.([]byte); ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	result.Error = rows.Err()
	result.Data = tableData
	return
}

// Query sql
func (db *Repository) Query(sql string, args ...any) (result *SqlResult) {
	return db.QueryContext(context.Background(), sql, args...)
}

// Insert func
func (db *Repository) Insert(table string, data map[string]any) (sql.Result, error) {
	values := make([]any, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		if col == "lock" {
			col = "`lock`"
		}
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?) "
	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		_, file, no, _ := runtime.Caller(1)
		fmt.Printf("%s#%d SQL: %s\n", file, no, getSQLRaw(query, values...))
	}

	return res, err
}

func (db *Repository) Update(table string, data map[string]any, whereCond string, whereParams ...any) (sql.Result, error) {
	values := make([]any, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	values = append(values, whereParams...)
	query = query[:len(query)-1] + " WHERE " + whereCond

	res, err := db.Conn.Exec(query, values...)
	if err != nil {
		// _, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%v \nquery : %s | originalQuery: %v, data: %v\n", err, getSQLRaw(query, values...), query, values)
	}

	return res, err
}

func getSQLRaw(sql string, params ...any) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		if index == -1 {
			return sql
		}
		sql = sql[:index] + cast.ToString(params[i]) + sql[index+1:]
	}
	return sql
}
