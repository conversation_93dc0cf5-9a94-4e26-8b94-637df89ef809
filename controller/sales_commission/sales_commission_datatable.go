package v1

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"reflect"
)

func SalesCommissionDatatable_coba1(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	draw := string(ctx.FormValue("draw"))
	start := 0
	if ctx.FormValue("start") != nil {
		start = utils.ToInt(ctx.FormValue("start"))
	}
	limit := 10
	if ctx.FormValue("length") != nil {
		limit = utils.ToInt(ctx.FormValue("length"))
	}
	timeStart := cast.ToInt64(string(ctx.FormValue("time_start")))
	timeEnd := cast.ToInt64(string(ctx.FormValue("time_end")))

	//mengambil data konten
	chanResultData := make(chan []map[string]interface{})
	go func() {
		qResultData, err := db.QueryArray(`
			SELECT
			sd.sales_detail_id AS id,
			o.name AS outlet,
			sh.name AS shift,
			IF(pd.variant_fkid IS NULL, p.name, concat(p.name, ' (',v.variant_name,')')) AS item,
			CONCAT(p.sku, IFNULL(v.variant_sku,'')) AS sku,
			pt.name AS type,
			pc.name AS category,
			psc.name AS subcategory,
			
			sd.qty - IFNULL((sv.qty),0) AS qty,
			(sd.sub_total + IFNULL((sv.sub_total),0)) AS subtotal,
	
			sd.commission_staff,
			sd.commission_customer
	
			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid=s.sales_id
			/*LEFT JOIN sales_void sv ON sv.sales_detail_fkid=sd.sales_detail_id*/
			LEFT JOIN (
					SELECT SUM(qty) AS qty, SUM(sub_total) AS sub_total, sales_detail_fkid 
					FROM sales_void 
					LEFT JOIN sales ON sales_void.sales_fkid=sales.sales_id
					WHERE sales.time_created >= ?
					AND sales.time_created <= ?
					GROUP BY sales_detail_fkid
				) sv ON sv.sales_detail_fkid=sd.sales_detail_id
			LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid 
			LEFT JOIN open_shift os ON s.open_shift_fkid=os.open_shift_id 
			LEFT JOIN shift sh ON sh.shift_id=os.shift_fkid 
			LEFT JOIN products_detail pd ON pd.product_detail_id=sd.product_detail_fkid 
			LEFT JOIN products p ON p.product_id=pd.product_fkid 
			LEFT JOIN products_detail_variant v ON v.variant_id=pd.variant_fkid 
			LEFT JOIN products_type pt ON pt.products_type_id=p.product_type_fkid 
			LEFT JOIN products_category pc ON pc.product_category_id=p.product_category_fkid 
			LEFT JOIN products_subcategory psc ON psc.product_subcategory_id=p.product_subcategory_fkid
			WHERE s.status='success'
			AND s.data_status='on'
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND o.admin_fkid=?
			AND (sd.qty - IFNULL((sv.qty),0)) > 0
			
			LIMIT ?,?
		`, timeStart, timeEnd, timeStart, timeEnd, adminId, start, limit)
		if err != nil {
			utils.CheckErr(err)
		}

		chanResultData <- qResultData
	}()

	//mengambil data untuk footer
	chanResultDataTotal := make(chan map[string]interface{})
	go func() {
		qResultDataTotal, err2 := db.Query(`
			SELECT
				COUNT(*) AS total,
				sum(sd.qty - IFNULL((sv.qty),0)) AS qty,
				sum(sd.sub_total + IFNULL((sv.sub_total),0)) AS subtotal,
				SUM(sd.commission_staff) AS commission_staff,
				SUM(sd.commission_customer) AS commission_customer
			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid=s.sales_id
			/*LEFT JOIN sales_void sv ON sv.sales_detail_fkid=sd.sales_detail_id*/
			LEFT JOIN (
					SELECT SUM(qty) AS qty, SUM(sub_total) AS sub_total, sales_detail_fkid 
					FROM sales_void 
					LEFT JOIN sales ON sales_void.sales_fkid=sales.sales_id
					WHERE sales.time_created>=?
					AND sales.time_created<=?
					GROUP BY sales_detail_fkid
				) sv ON sv.sales_detail_fkid=sd.sales_detail_id
			LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid 
			LEFT JOIN open_shift os ON s.open_shift_fkid=os.open_shift_id 
			LEFT JOIN shift sh ON sh.shift_id=os.shift_fkid 
			LEFT JOIN products_detail pd ON pd.product_detail_id=sd.product_detail_fkid 
			LEFT JOIN products p ON p.product_id=pd.product_fkid 
			LEFT JOIN products_detail_variant v ON v.variant_id=pd.variant_fkid 
			LEFT JOIN products_type pt ON pt.products_type_id=p.product_type_fkid 
			LEFT JOIN products_category pc ON pc.product_category_id=p.product_category_fkid 
			LEFT JOIN products_subcategory psc ON psc.product_subcategory_id=p.product_subcategory_fkid
			WHERE s.status='success'
			AND s.data_status='on'
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND o.admin_fkid=?
			AND (sd.qty - IFNULL((sv.qty),0)) > 0
		`, timeStart, timeEnd, timeStart, timeEnd, adminId)
		if err2 != nil {
			utils.CheckErr(err2)
		}

		chanResultDataTotal <- qResultDataTotal
	}()

	//body := ctx.PostBody()
	//fmt.Println(cast.String(body))
	//draw2 := ctx.FormValue("cuek")
	//fmt.Println(cast.String(draw2))
	//
	//
	//draw := ctx.FormValue("draw")
	//fmt.Println(draw)
	//fmt.Println(utils.String(draw))
	//fmt.Println(cast.String(draw))
	data := <-chanResultData
	dataTotal := <-chanResultDataTotal
	recordsTotal := dataTotal["total"]
	recordsTotalFilter := recordsTotal
	if (ctx.FormValue("search[value]")) != nil {
		recordsTotalFilter = 1234
	}

	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"draw":            draw,
		"recordsTotal":    recordsTotal,
		"recordsFiltered": recordsTotalFilter,
		"data":            data,
		"total": map[string]interface{}{
			"qty":                 dataTotal["qty"],
			"subtotal":            dataTotal["subtotal"],
			"commission_staff":    dataTotal["commission_staff"],
			"commission_customer": dataTotal["commission_customer"],
		},
	})
}

func SalesCommissionDatatable(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	searchQuery := string(ctx.FormValue("search[value]"))
	draw := string(ctx.FormValue("draw"))
	start := 0
	if ctx.FormValue("start") != nil {
		start = utils.ToInt(ctx.FormValue("start"))
	}
	limit := 10
	if ctx.FormValue("length") != nil {
		limit = utils.ToInt(ctx.FormValue("length"))
	}
	timeStart := cast.ToInt64(string(ctx.FormValue("time_start")))
	timeEnd := cast.ToInt64(string(ctx.FormValue("time_end")))

	//ambil data produk
	tmpProduct := make(chan map[string]map[string]interface{})
	go func() {
		query, err := db.QueryArray(`
			SELECT DISTINCT
			(IF(pd.variant_fkid IS NULL, p.name, concat(p.name, ' (',v.variant_name,')'))) AS item,
			p.product_id, v.variant_id,
			pt.name AS type,
			pc.name AS category,
			psc.name AS subcategory,
			(IF(pd.variant_fkid IS NULL, p.sku, concat(p.sku, '-',v.variant_sku,')'))) AS sku
			FROM products_detail pd
			LEFT JOIN products p ON p.product_id=pd.product_fkid
			LEFT JOIN products_detail_variant v ON v.variant_id=pd.variant_fkid
			LEFT JOIN products_type pt ON pt.products_type_id=p.product_type_fkid 
			LEFT JOIN products_category pc ON pc.product_category_id=p.product_category_fkid 
			LEFT JOIN products_subcategory psc ON psc.product_subcategory_id=p.product_subcategory_fkid
			WHERE p.admin_fkid = ?
			AND p.data_status='on'
			HAVING item LIKE ?
		`, adminId, "%"+searchQuery+"%")
		if err != nil {
			utils.CheckErr(err)
		}
		data := make(map[string]map[string]interface{})
		for _, row := range query {
			productId := utils.ToString(row["product_id"])
			variantId := ""
			if (row["variant_id"]) != nil {
				variantId = utils.ToString(row["variant_id"])
			}

			dataToMap := map[string]interface{}{
				"item":        row["item"],
				"type":        row["type"],
				"category":    row["category"],
				"subcategory": row["subcategory"],
			}
			data[productId] = make(map[string]interface{})
			data[productId][variantId] = dataToMap
		}
		tmpProduct <- data
	}()

	//ambil data sales detail
	tmpDataCommission := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray(`
			SELECT 
		-- DISTINCT 
		-- sd.sales_detail_id,
		-- sd.product_detail_fkid,
			o.name AS outlet,
			pd.product_fkid,
			pd.variant_fkid,
			s.outlet_fkid,
			(sd.qty - IFNULL((sv.qty),0)) AS qty,
			(sd.sub_total + IFNULL((sv.sub_total),0)) AS subtotal,
			(sd.commission_staff) AS commission_staff,
			(sd.commission_customer) AS commission_customer
			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid=s.sales_id
			LEFT JOIN (
					SELECT SUM(qty) AS qty, SUM(sub_total) AS sub_total, sales_detail_fkid 
					FROM sales_void 
					LEFT JOIN sales ON sales_void.sales_fkid=sales.sales_id
					WHERE sales.time_created >= ?
					AND sales.time_created <= ?
					GROUP BY sales_detail_fkid
				) sv ON sv.sales_detail_fkid=sd.sales_detail_id
			LEFT JOIN products_detail pd ON pd.product_detail_id=sd.product_detail_fkid
			LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid
			LEFT JOIN open_shift os ON s.open_shift_fkid=os.open_shift_id 
			LEFT JOIN shift sh ON sh.shift_id=os.shift_fkid
			WHERE s.status='success'
			AND s.data_status='on'
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND o.admin_fkid = ?
			-- AND sd.product_fkid IN (4161,4261,8237,8240)
			AND sd.qty - ifnull(sv.qty,0) > 0
			HAVING outlet LIKE ?
			OR qty LIKE ?
			OR subtotal LIKE ?
			OR commission_staff LIKE ?
			OR commission_customer LIKE ?
			LIMIT ?,?
		`, timeStart, timeEnd, timeStart, timeEnd, adminId, "%"+searchQuery+"%", "%"+searchQuery+"%", "%"+searchQuery+"%", "%"+searchQuery+"%", "%"+searchQuery+"%", start, limit)
		if err != nil {
			utils.CheckErr(err)
		}
		data := []map[string]interface{}{}
		for _, row := range query {
			data = append(data, map[string]interface{}{
				"product_id":          (row["product_fkid"]),
				"variant_id":          (row["variant_fkid"]),
				"outlet_id":           (row["outlet_fkid"]),
				"outlet":              row["outlet"],
				"qty":                 row["qty"],
				"subtotal":            row["subtotal"],
				"commission_staff":    row["commission_staff"],
				"commission_customer": row["commission_customer"],
			})
		}
		tmpDataCommission <- data
	}()

	//ambil data total
	tmpDataTotal := make(chan map[string]interface{})
	go func() {
		query, err := db.Query(`
			SELECT
				COUNT(sd.sales_detail_id) AS total,
				sum(sd.qty - IFNULL((sv.qty),0)) AS qty,
				sum(sd.sub_total + IFNULL((sv.sub_total),0)) AS subtotal,
				SUM(sd.commission_staff) AS commission_staff,
				SUM(sd.commission_customer) AS commission_customer	
			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid=s.sales_id
			LEFT JOIN (
					SELECT SUM(qty) AS qty, SUM(sub_total) AS sub_total, sales_detail_fkid 
					FROM sales_void 
					LEFT JOIN sales ON sales_void.sales_fkid=sales.sales_id
					WHERE sales.time_created >= ?
					AND sales.time_created <= ?
					GROUP BY sales_detail_fkid
				) sv ON sv.sales_detail_fkid=sd.sales_detail_id
			LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid 
			-- LEFT JOIN open_shift os ON s.open_shift_fkid=os.open_shift_id 
			-- LEFT JOIN shift sh ON sh.shift_id=os.shift_fkid 
			-- LEFT JOIN products_detail pd ON pd.product_detail_id=sd.product_detail_fkid 
			-- LEFT JOIN products p ON p.product_id=pd.product_fkid 
		
			WHERE s.status='success'
			AND s.data_status='on'
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND o.admin_fkid = ?
			AND sd.qty - ifnull(sv.qty,0) > 0
		`, timeStart, timeEnd, timeStart, timeEnd, adminId)
		if err != nil {
			utils.CheckErr(err)
		}
		total := 0
		if query["total"] != nil {
			total = utils.ToInt(query["total"])
		}
		qty := 0
		if query["qty"] != nil {
			qty = utils.ToInt(query["qty"])
		}
		subtotal := 0
		if query["subtotal"] != nil {
			subtotal = utils.ToInt(query["subtotal"])
		}
		commission_staff := 0
		if query["commission_staff"] != nil {
			commission_staff = utils.ToInt(query["commission_staff"])
		}
		commission_customer := 0
		if query["commission_customer"] != nil {
			commission_customer = utils.ToInt(query["commission_customer"])
		}

		tmpDataTotal <- map[string]interface{}{
			"total":               total,
			"qty":                 qty,
			"subtotal":            subtotal,
			"commission_staff":    commission_staff,
			"commission_customer": commission_customer,
		}
	}()

	//render data untuk datatable
	dataProduct := <-tmpProduct
	dataCommission := <-tmpDataCommission
	dataRender := []map[string]interface{}{}
	for _, row := range dataCommission {
		productId := utils.ToString(row["product_id"])
		variantId := utils.ToString(row["variant_id"])
		//outletId := utils.String(row["outlet_id"])
		if d, ok := dataProduct[productId][variantId].(map[string]interface{}); ok {
			itemData := d

			dataRender = append(dataRender, map[string]interface{}{
				"id":                  itemData["item"],
				"item":                itemData["item"],
				"outlet":              row["outlet"],
				"shift":               "",
				"sku":                 itemData["sku"],
				"type":                itemData["type"],
				"category":            itemData["category"],
				"subcategory":         itemData["subcategory"],
				"qty":                 row["qty"],
				"subtotal":            row["subtotal"],
				"commission_staff":    row["commission_staff"],
				"commission_customer": row["commission_customer"],
			})
		} else {
			fmt.Println("konvert gagal", reflect.TypeOf(dataProduct[productId][variantId]))
		}
	}

	//set json output
	totalData := <-tmpDataTotal
	recordsFiltered := totalData["total"]
	if searchQuery != "" {
		recordsFiltered = 123
	}
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"draw":            draw,
		"recordsTotal":    totalData["total"],
		"recordsFiltered": recordsFiltered,
		"data":            dataRender,
		"total": map[string]interface{}{
			"qty":                 totalData["qty"],
			"subtotal":            totalData["subtotal"],
			"commission_staff":    totalData["commission_staff"],
			"commission_customer": totalData["commission_customer"],
		},
	})

}
