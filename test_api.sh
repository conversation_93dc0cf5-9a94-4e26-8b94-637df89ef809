#!/bin/bash

# Test script for the new sales report by product API
# Make sure your API server is running before executing this script

BASE_URL="http://localhost:8080"
ENDPOINT="/v1/sales-by/product"

echo "Testing Sales Report by Product API"
echo "===================================="
echo "Expected JSON format:"
echo '{"data":[{"sub_total":198320,"product_name":"Product Name","sku":"SKU001","qty":8,"category":"Category","avg":24790,"discount":10000,"voucher":1000,"promotion":1000,"grand_total":185320}]}'
echo ""

# Test 1: Basic request with all parameters
echo "Test 1: Basic request with all parameters"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&category=27,283&outlet=29,41&dataType=1&shift=12,10" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 2: Request with dataType=0 (by shift)
echo "Test 2: Request with dataType=0 (by shift)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&outlet=29,41&dataType=0&shift=12,10" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 3: Request without filters (only date and admin)
echo "Test 3: Request without filters (only date and admin)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&endDate=1755709199999&timeZone=25200&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 4: Request with missing startDate (should return validation error)
echo "Test 4: Request with missing startDate (should return validation error)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "endDate=1755709199999&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 5: Request with missing endDate (should return validation error)
echo "Test 5: Request with missing endDate (should return validation error)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1753030800000&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 6: Request with startDate >= endDate (should return validation error)
echo "Test 6: Request with startDate >= endDate (should return validation error)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=1755709199999&endDate=1753030800000&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\n"

# Test 7: Request with zero dates (should return validation error)
echo "Test 7: Request with zero dates (should return validation error)"
curl -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "user_type: admin" \
  -H "user_id: 1" \
  -H "admin_id: 10" \
  -H "outlet_access: 29,41" \
  -H "role: admin" \
  --data-raw "startDate=0&endDate=0&dataType=1" \
  | jq '.' 2>/dev/null || echo "Response received (jq not available for formatting)"

echo -e "\n\nTesting completed!"
