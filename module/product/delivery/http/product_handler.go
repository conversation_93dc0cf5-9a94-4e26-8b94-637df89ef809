package http

import (
	v2 "github.com/gofiber/fiber/v2"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

type productHandler struct {
	domain.ProductUseCase
}

func NewHttpProductHandler(app *v2.App, useCase domain.ProductUseCase) {
	handler := &productHandler{useCase}
	app.Get("/module/product", handler.Sample)
}
func (h productHandler) Sample(c *v2.Ctx) error {
	return c.SendString("this is sample of product feature route")
}
