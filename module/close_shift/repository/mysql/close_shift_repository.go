package mysql

import (
	"database/sql"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type closeShiftRepository struct {
	db mysql.Repository
}

func NewMysqlCloseShiftRepository(db *sql.DB, cache domain.CacheInterface) domain.CloseShiftRepository {
	return &closeShiftRepository{mysql.Repository{Conn: db, CacheDb: cache}}
}

// FetchSalesRecapShift implements domain.CloseShiftRepository.
func (c *closeShiftRepository) FetchSalesRecapShift(request domain.SalesReportRequest, user domain.User) (models.SalesRecapShift, error) {
	// Construct query similar to PHP model
	// // COUNT(IF(s.status = 'refund', s.sales_id, NULL)) AS bill_refund
	// SUM(s.grand_total) AS grand_total,
	// SUM(s.discount) AS discount_sales,
	// SUM(s.qty_customers) AS pax,
	// SUM(s.voucher) AS voucher_sales,
	// COUNT(s.sales_id) AS bill
	query := `SELECT 
	SUM(IF(s.status = 'success', s.grand_total, 0)) AS grand_total,
    SUM(IF(s.status = 'success', s.discount, 0)) AS discount_sales, 
    SUM(IF(s.status = 'success', s.qty_customers, 0)) AS pax,
    SUM(IF(s.status = 'success', s.voucher, 0)) AS voucher_sales,
    COUNT(IF(s.status = 'success', s.sales_id, NULL)) AS bill,
    SUM(IF(s.status = 'refund', s.grand_total, 0)) AS grand_total_refund,
    COUNT(IF(s.status = 'refund', s.sales_id, NULL)) AS bill_refund
            FROM sales s 
            JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
            JOIN outlets o ON o.outlet_id = s.outlet_fkid
            WHERE s.data_status = 'on' 
			AND o.admin_fkid = @adminId AND os.time_open BETWEEN @dateStart AND @dateEnd `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}
	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	var result models.SalesRecapShift
	err := c.db.QueryCache("sales-recap-shift", query, params...).Model(&result)
	return result, err
}

func (c *closeShiftRepository) FetchSalesDetailShift(request domain.SalesReportRequest, user domain.User) (models.SalesDetailShift, error) {
	// Construct query
	query := `SELECT  
	SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment != 'compliment',sd.sub_total,0)) AS sub_total,
	SUM(IF(s.status='Success' AND s.data_status='on' AND s.payment != 'compliment',sd.discount,0)) AS discount_detail
FROM sales_detail sd
JOIN sales s ON s.sales_id = sd.sales_fkid
JOIN outlets o ON o.outlet_id = s.outlet_fkid
JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
WHERE o.admin_fkid = @adminId 
AND os.time_open BETWEEN @dateStart AND @dateEnd  `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	var result models.SalesDetailShift
	err := c.db.QueryCache("sales-detail-shift", query, params...).Model(&result)
	return result, err
}

func (c *closeShiftRepository) FetchPayShift(request domain.SalesReportRequest, user domain.User) (models.PayShift, error) {
	query := `SELECT
	sum( if(sp.method = 'cash', sp.total, 0)) AS cash,
	sum( if(sp.method = 'card', sp.total, 0)) AS card,
	sum( if(sp.method = 'piutang', sp.total, 0)) AS piutang,
	sum( if(sp.method = 'compliment', sp.total, 0)) AS compliment,
	sum( if(sp.method = 'free', sp.total, 0)) AS free,
	sum( if(sp.method = 'duty meals', sp.total, 0)) AS duty_meals
FROM
	sales_payment sp
	LEFT JOIN sales s ON s.sales_id = sp.sales_fkid
	LEFT JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
	LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
WHERE
	s.status = 'success' AND s.data_status = 'on' 
	AND o.admin_fkid = @adminId 
	AND os.time_open BETWEEN @dateStart AND @dateEnd `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// Prepare query
	stmt, err := c.db.Conn.Prepare(query)
	if log.IfError(err) {
		return models.PayShift{}, err
	}

	// fmt.Println(">>> ", db.GetSQLRaw(query, params...))
	// Execute query
	defer stmt.Close()
	rows, err := stmt.Query(params...) //user.BusinessId, request.Shift, request.StartDate, request.EndDate
	if log.IfError(err) {
		return models.PayShift{}, err
	}

	defer rows.Close()
	// Iterate over rows
	for rows.Next() {
		// Create new PayShift struct
		var p models.PayShift
		// Scan row into struct fields
		err := rows.Scan(&p.Cash, &p.Card, &p.Piutang, &p.Compliment, &p.Free, &p.DutyMeals)
		return p, err
	}

	return models.PayShift{}, err
}

func (r *closeShiftRepository) FetchTaxShift(request domain.SalesReportRequest, user domain.User) (models.TaxShift, error) {
	// Construct query
	query := `SELECT
			  IFNULL(SUM(CASE WHEN g.tax_category='tax' THEN st.total ELSE 0 END), 0) AS tax,
			  IFNULL(SUM(CASE WHEN g.tax_category='service' THEN st.total ELSE 0 END), 0) AS service,
			  IFNULL(SUM(CASE WHEN g.tax_category='voucher' THEN st.total ELSE 0 END), 0) AS voucher,
			  IFNULL(SUM(CASE WHEN g.tax_category='discount' THEN st.total ELSE 0 END), 0) AS discount
			 FROM sales_tax st
			 JOIN sales s ON s.sales_id = st.sales_fkid
			 JOIN gratuity g ON g.gratuity_id = st.tax_fkid
			 JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid  
			 JOIN outlets o ON o.outlet_id = s.outlet_fkid
			 WHERE s.status = 'success' AND s.data_status = 'on' AND s.payment != 'compliment'
			 AND o.admin_fkid = @adminId 
			 AND os.time_open BETWEEN @dateStart AND @dateEnd `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// fmt.Println(">>> ", db.GetSQLRaw(query, params...))

	// Execute query
	rows, err := r.db.Conn.Query(query, params...)
	if log.IfError(err) {
		return models.TaxShift{}, err
	}

	defer rows.Close()
	for rows.Next() {
		var t models.TaxShift
		err := rows.Scan(&t.Tax, &t.Service, &t.Voucher, &t.Discount)
		return t, err
	}

	return models.TaxShift{}, nil
}

func (r *closeShiftRepository) FetchVoidShiftRecap(request domain.SalesReportRequest, user domain.User) (models.VoidShiftRecap, error) {
	// Construct query
	query := `SELECT 
                SUM(sv.qty) AS qty_void, 
                SUM(sv.sub_total) AS sub_void,
				sum(sv.discount) discount
               FROM sales_void sv
               JOIN sales s ON s.sales_id = sv.sales_fkid
               JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
               JOIN outlets o ON o.outlet_id = s.outlet_fkid
               WHERE s.status = 'success' AND s.data_status = 'on'  
               AND o.admin_fkid = @adminId 
               AND os.time_open BETWEEN @dateStart AND @dateEnd `

	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}
	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// Prepare statement
	stmt, err := r.db.Conn.Prepare(query)
	if log.IfError(err) {
		return models.VoidShiftRecap{}, err
	}
	defer stmt.Close()

	// Execute
	rows, err := stmt.Query(params...)
	if log.IfError(err) {
		return models.VoidShiftRecap{}, err
	}

	defer rows.Close()
	for rows.Next() {
		var r models.VoidShiftRecap
		err := rows.Scan(&r.QtyVoid, &r.SubVoid, &r.Dicount)
		return r, err
	}

	return models.VoidShiftRecap{}, nil
}

func (r *closeShiftRepository) FetchRefundShiftRecap(request domain.SalesReportRequest, user domain.User) (models.RefundShiftRecap, error) {
	// Base query
	query := `SELECT  
                SUM(s.grand_total) AS grand_total,
                COUNT(s.sales_id) AS bill_refund
               FROM sales s
               JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
               JOIN outlets o ON o.outlet_id = s.outlet_fkid
               WHERE s.status = 'refund' AND s.data_status = 'on'  
               AND o.admin_fkid = @adminId  
               AND os.time_open BETWEEN @dateStart AND @dateEnd `

	// Add filters if provided
	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}

	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	// Map params
	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// Prepare statement
	stmt, err := r.db.Conn.Prepare(query)
	if err != nil {
		return models.RefundShiftRecap{}, err
	}
	defer stmt.Close()

	// Execute
	rows, err := stmt.Query(params...)

	if err != nil {
		return models.RefundShiftRecap{}, err
	}
	defer rows.Close()
	// Iterate over rows
	for rows.Next() {

		// Create struct to hold row data
		var r models.RefundShiftRecap

		// Scan row into struct fields
		err := rows.Scan(&r.GrandTotal, &r.BillRefund)
		return r, err
	}

	// Return mapped rows
	return models.RefundShiftRecap{}, nil
}

func (r *closeShiftRepository) FetchPromoRecapProduk(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	var promoRecap models.PromoRecapTotal

	// Construct query
	query := `SELECT IFNULL(SUM(sdd.total), 0) AS promo
              FROM sales_detail_discount sdd
              LEFT JOIN sales_detail sd ON sd.sales_detail_id = sdd.sales_detail_fkid
              LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
              LEFT JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
              LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
              WHERE o.admin_fkid = @adminId 
              AND sdd.type = "promotion"
              AND s.status = "success"
              AND s.data_status = "on"
              AND os.time_open BETWEEN @dateStart AND @dateEnd `

	// Add filters if provided
	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}

	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	// Map params
	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// Execute query
	rows, err := r.db.Conn.Query(query, params...)
	if err != nil {
		return promoRecap, err
	}
	defer rows.Close()

	// Process the result
	for rows.Next() {
		err := rows.Scan(&promoRecap.Promo)
		return promoRecap, err
	}

	return promoRecap, nil
}

func (r *closeShiftRepository) FetchPromoRecapShift(request domain.SalesReportRequest, user domain.User) (models.PromoRecapTotal, error) {
	var promoRecap models.PromoRecapTotal

	// Construct query
	query := `SELECT IFNULL(SUM(sdp.promotion_value), 0) AS promo
              FROM sales_detail_promotion sdp
              LEFT JOIN sales_detail sd ON sd.sales_detail_id = sdp.sales_detail_fkid
              LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
              LEFT JOIN open_shift os ON os.open_shift_id = s.open_shift_fkid
              LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
              WHERE o.admin_fkid = @adminId 
              AND s.status = "success"
              AND s.data_status = "on"
              AND os.time_open BETWEEN @dateStart AND @dateEnd `

	// Add filters if provided
	if len(request.Shift) > 0 {
		query += " AND os.shift_fkid IN @shiftIds "
	}

	if len(request.Outlet) > 0 {
		query += " AND o.outlet_id IN @outletIds "
	}

	// Map params
	query, params := db.MapParam(query, map[string]any{
		"adminId":   user.BusinessId,
		"shiftIds":  request.Shift,
		"dateStart": request.StartDate,
		"dateEnd":   request.EndDate,
		"outletIds": request.Outlet,
	})

	// Execute query
	rows, err := r.db.Conn.Query(query, params...)
	if err != nil {
		return promoRecap, err
	}
	defer rows.Close()

	// Process the result
	for rows.Next() {
		err := rows.Scan(&promoRecap.Promo)
		return promoRecap, err
	}

	return promoRecap, nil
}
