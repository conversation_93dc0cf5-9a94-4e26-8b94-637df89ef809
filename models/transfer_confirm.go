package models

type TransferConfirm struct {
	ProductDetailFkid    int     `json:"product_detail_fkid,omitempty"`
	ProductDetailDesFkid int     `json:"product_detail_des_fkid,omitempty"`
	Qty                  int     `json:"qty,omitempty"`
	QtyConfirm           float32 `json:"qty_confirm,omitempty"`
	Markup               int     `json:"markup,omitempty"`
	MarkupType           string  `json:"markup_type,omitempty"`
	TransferProductID    int     `json:"transfer_product_id,omitempty"`
	TransferConfirmID    int     `json:"transfer_confirm_id,omitempty"`
	Discount             int     `json:"discount,omitempty"`
	Price                float32 `json:"price,omitempty"`
}

func (t *TransferConfirm) MetaData() map[string]interface{} {
	return map[string]interface{}{
		"transfer_confirm_id": t.TransferConfirmID,
		"transfer_product_id": t.TransferProductID,
		"product_detail_fkid": t.ProductDetailFkid,
	}
}
