package v1

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"reflect"
	"strings"
)

func SalesCommission_v1(ctx *fasthttp.RequestCtx) {
	adminId := string(ctx.Request.Header.Peek("admin_id"))
	start := 0
	if ctx.FormValue("start") != nil {
		start = utils.ToInt(ctx.FormValue("start"))
	}
	limit := 10
	if ctx.FormValue("limit") != nil {
		limit = utils.ToInt(ctx.FormValue("limit"))
	}
	timeStart := cast.ToInt64(string(ctx.FormValue("time_start")))
	timeEnd := cast.ToInt64(string(ctx.FormValue("time_end")))
	outletIds := utils.ToString(ctx.FormValue("outlet_id"))
	if outletIds == "" {
		outletIds = utils.ToString(ctx.Request.Header.Peek("outlet_access"))
	}
	outletIdsQuery := "'" + strings.Replace(outletIds, ",", "','", -1) + "'"

	//ambil data produk
	tmpProduct := make(chan map[string]map[string]interface{})
	go func() {
		query, err := db.QueryArray(`
			SELECT DISTINCT
			(IF(pd.variant_fkid IS NULL, p.name, concat(p.name, ' (',v.variant_name,')'))) AS item,
			p.product_id, v.variant_id,
			pt.name AS type,
			pc.name AS category,
			psc.name AS subcategory,
			(IF(pd.variant_fkid IS NULL, p.sku, concat(p.sku, '-',v.variant_sku,')'))) AS sku
			FROM products_detail pd
			LEFT JOIN products p ON p.product_id=pd.product_fkid
			LEFT JOIN products_detail_variant v ON v.variant_id=pd.variant_fkid
			LEFT JOIN products_type pt ON pt.products_type_id=p.product_type_fkid 
			LEFT JOIN products_category pc ON pc.product_category_id=p.product_category_fkid 
			LEFT JOIN products_subcategory psc ON psc.product_subcategory_id=p.product_subcategory_fkid
			WHERE p.admin_fkid = ?
			AND p.data_status = ?
		`, adminId, "on")
		if err != nil {
			utils.CheckErr(err)
		}
		data := make(map[string]map[string]interface{})
		for _, row := range query {
			productId := utils.ToString(row["product_id"])
			variantId := ""
			if (row["variant_id"]) != nil {
				variantId = utils.ToString(row["variant_id"])
			}

			dataToMap := map[string]interface{}{
				"item":        row["item"],
				"type":        row["type"],
				"category":    row["category"],
				"subcategory": row["subcategory"],
			}
			data[productId] = make(map[string]interface{})
			data[productId][variantId] = dataToMap
		}
		tmpProduct <- data
	}()

	//ambil data sales
	tmpSalesData := make(chan []map[string]interface{})
	go func() {
		queryRAW := db.GetSQLRaw(
			//query, err := db.QueryArray(
			`SELECT
			sd.sales_detail_id AS id,
			o.name AS outlet,
			s.display_nota,
			s.outlet_fkid AS outlet_id,
			pd.product_fkid AS product_id,
			pd.variant_fkid AS variant_id,
			sh.name AS shift,
			sd.qty,
			sd.sub_total AS subtotal,
			(sd.commission_staff) AS commission_staff,
			(sd.commission_customer) AS commission_customer,
			p.product_type_fkid AS product_type_id,
			p.product_category_fkid AS product_category_id,
			p.product_subcategory_fkid AS product_subcategory_id
		FROM sales_detail sd
		LEFT JOIN sales s ON sd.sales_fkid = s.sales_id
		LEFT JOIN products_detail pd ON pd.product_detail_id = sd.product_detail_fkid
		LEFT JOIN products p ON pd.product_fkid = p.product_id
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON s.open_shift_fkid = os.open_shift_id
		LEFT JOIN shift sh ON sh.shift_id = os.shift_fkid
		WHERE s.status='success'
		AND s.data_status='on'
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND o.admin_fkid = ?
		AND p.data_status = 'on'
		AND s.outlet_fkid IN ( ? )
		LIMIT ?, ?
		`, timeStart, timeEnd, adminId, outletIdsQuery, start, limit)
		//fmt.Println(queryRAW)
		query, err := db.QueryArray(queryRAW)
		if err != nil {
			utils.CheckErr(err)
		}
		tmpSalesData <- query
	}()

	tmpSalesVoid := make(chan map[string]interface{})
	go func() {
		query, err := db.QueryArray(`
			SELECT SUM(qty) AS qty, SUM(sub_total) AS subtotal, sales_detail_fkid
			FROM sales_void sv
			LEFT JOIN sales s ON sv.sales_fkid=s.sales_id
			WHERE s.outlet_fkid IN ( ? )
			AND s.time_created >= ?
			AND s.time_created <= ?
			GROUP BY sales_detail_fkid
		`, outletIdsQuery, timeStart, timeEnd)
		if err != nil {
			utils.CheckErr(err)
		}
		tmp := map[string]interface{}{}
		for _, data := range query {
			sales_detail_id := utils.ToString(data["sales_detail_fkid"])
			tmp[sales_detail_id] = map[string]interface{}{
				"qty":      data["qty"],
				"subtotal": data["subtotal"],
			}
		}
		tmpSalesVoid <- tmp
	}()

	dataSales := <-tmpSalesData
	dataSalesVoid := <-tmpSalesVoid
	fmt.Println("sv: ", dataSalesVoid)
	dataProduct := <-tmpProduct

	dataRender := []map[string]interface{}{}
	for _, row := range dataSales {
		productId := utils.ToString(row["product_id"])
		variantId := utils.ToString(row["variant_id"])

		if d, ok := dataProduct[productId][variantId].(map[string]interface{}); ok {
			itemData := d

			dataRender = append(dataRender, map[string]interface{}{
				"id":                  row["id"],
				"item":                itemData["item"],
				"outlet":              row["outlet"],
				"display_nota":        row["display_nota"],
				"shift":               row["shift"],
				"sku":                 itemData["sku"],
				"type":                itemData["type"],
				"category":            itemData["category"],
				"subcategory":         itemData["subcategory"],
				"qty":                 row["qty"],
				"subtotal":            row["subtotal"],
				"commission_staff":    row["commission_staff"],
				"commission_customer": row["commission_customer"],
			})
		} else {
			fmt.Println("konvert gagal", reflect.TypeOf(dataProduct[productId][variantId]))
		}
	}

	//paging
	url := strings.Split(cast.ToString(ctx.URI()), "?")[0]
	param := map[string]interface{}{
		"time_start":             timeStart,
		"time_end":               timeEnd,
		"outlet_id":              outletIds,
		"shift_id":               "",
		"product_type_id":        "",
		"product_category_id":    "",
		"product_subcategory_id": "",
		"limit":                  limit,
	}
	url_next := ""
	if len(dataRender) > 0 {
		param_next := map[string]interface{}{
			"start": start + limit,
		}
		param_next = utils.MergeKeys(param, param_next)
		url_next = url + "?" + utils.BuildHttpQuery(param_next)
	}
	url_prev := ""
	//start_prev := start - limit;
	//if start_prev >= 0 {
	//	param_prev := map[string]interface{}{
	//		"start": start_prev,
	//	}
	//	param_prev = utils.MergeKeys(param_prev, param)
	//	url_prev = url + "?" + utils.BuildHttpQuery(param_prev)
	//}

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status": true,
		"paging": map[string]interface{}{
			"prev": url_prev,
			"next": url_next,
		},
		"data": dataRender,
		//"sales": dataSales,
		//"void": dataSalesVoid,
		//"product": dataProduct,
	})

	/*

		//render data untuk datatable
		dataProduct := <- tmpProduct
		dataCommission := <- tmpSalesData
		dataRender := []map[string]interface{}{}
		for _, row := range dataCommission {
			productId := utils.String(row["product_id"])
			variantId := utils.String(row["variant_id"])
			//outletId := utils.String(row["outlet_id"])
			if d,ok := dataProduct[productId][variantId].(map[string]interface{});ok {
				itemData := d

				dataRender = append(dataRender, map[string]interface{}{
					"id": itemData["item"],
					"item": itemData["item"],
					"outlet": row["outlet"],
					"shift": row["shift"],
					"sku": itemData["sku"],
					"type": itemData["type"],
					"category": itemData["category"],
					"subcategory": itemData["subcategory"],
					"qty": row["qty"],
					"subtotal": row["subtotal"],
					"commission_staff": row["commission_staff"],
					"commission_customer": row["commission_customer"],
				})
			}else{
				fmt.Println("konvert gagal", reflect.TypeOf(dataProduct[productId][variantId]))
			}
		}

		////ambil data produk
		//tmpProduct := make(chan map[string]map[string]interface{})
		//go func() {}()
		data := <- tmpSalesData
		fmt.Println(len(data))


		ctx.SetContentType("application/json")
		_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
			"status": true,
			"paging": map[string]interface{}{
				"prev":"",
				"next":"",
			},
			"data": data,
		})

	*/
}
