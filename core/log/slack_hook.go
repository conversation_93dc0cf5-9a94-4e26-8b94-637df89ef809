package log

import (
	"fmt"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"os"
)

type SlackHook struct {
	HookUrl string
	Channel string
}

type SlackMessage struct {
	Attachments []SlackAttachments `json:"attachments"`
}
type SlackFields struct {
	Title string `json:"title"`
	Value string `json:"value"`
	Short bool   `json:"short"`
}
type SlackAttachments struct {
	Fallback string        `json:"fallback"`
	Text     string        `json:"text"`
	Fields   []SlackFields `json:"fields"`
	Color    string        `json:"color"`
}

func (s SlackHook) initHook() {

}

func (s SlackHook) send(output logOutput) error {
	if s.HookUrl == "" {
		return fmt.Errorf("slack not initialized")
	}

	var slackMsg SlackMessage
	var slackFields []SlackFields
	slackFields = append(slackFields, SlackFields{
		Title: "Project",
		Value: "API REPORT",
		Short: true,
	})
	slackFields = append(slackFields, SlackFields{
		Title: "Environment",
		Value: os.Getenv("ENV"),
		Short: true,
	})

	slackMsg.Attachments = append(slackMsg.Attachments, SlackAttachments{
		Fallback: output.Message,
		Text:     output.Message,
		Color:    "#F35A00",
		Fields:   slackFields,
	})

	request := utils.HttpRequest{
		Method: "POST",
		Url:    s.HookUrl,
		PostRequest: utils.PostRequest{
			Body: slackMsg,
		},
	}

	resp, err := request.Execute()
	fmt.Println("send hook: ", string(resp), "err: ", err)
	return err
}
