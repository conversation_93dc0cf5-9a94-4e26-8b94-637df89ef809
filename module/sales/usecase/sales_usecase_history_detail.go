package usecase

import (
	"math"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
)

func (s *salesUseCase) FetchSalesHistoryTransactionDetail(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	log.Info("FetchSalesHistoryTransactionDetail, param: %v", utils.SimplyToJson(request))
	repo := s.repoPrimary // s.pickRepo(request.EndDate)
	result := map[string]interface{}{
		"data":     []interface{}{},
		"offsetId": 0,
	}

	//fetch sales detail
	details, err := repo.FetchSalesDetail(user, request)
	if log.IfError(err) || len(details) == 0 {
		return result, err
	}

	detailIds := make([]interface{}, 0)
	employeeIdMap := make(map[interface{}]bool)
	shiftIdMap := make(map[interface{}]bool)
	productIdMap := make(map[interface{}]bool)
	productDetailIdMap := make(map[interface{}]bool)
	salesIdMap := make(map[interface{}]bool)
	for _, row := range details {
		detailIds = append(detailIds, row["sales_detail_id"])
		employeeIdMap[row["employee_fkid"]] = true
		employeeIdMap[row["operator_id"]] = true
		shiftIdMap[row["shift_fkid"]] = true
		productIdMap[row["product_fkid"]] = true
		productDetailIdMap[row["product_detail_fkid"]] = true
		salesIdMap[row["sales_id"]] = true
	}

	shiftIds := array.GetKeys(shiftIdMap)
	employeeIds := array.GetKeys(employeeIdMap)
	productDetailIds := array.GetKeys(productDetailIdMap)
	salesIds := array.GetKeys(salesIdMap)

	salesVoidChan := make(chan []map[string]interface{})
	salesDetailDiscountChan := make(chan []map[string]interface{})
	salesDetailPromotionChan := make(chan []map[string]interface{})
	salesDetailTaxChan := make(chan []map[string]interface{})
	employeeChan := make(chan []map[string]interface{})
	shiftChan := make(chan []map[string]interface{})
	productChan := make(chan []map[string]interface{})
	paymentChan := make(chan []map[string]interface{})

	//fetch sales_void
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesVoid(domain.SalesDetailFkid, ids...)
		log.IfError(err)
		resultChan <- result
	}(detailIds, salesVoidChan)

	//fetch sales_detail_discount, by type: discount, voucher, promotion
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetailDiscount(domain.SalesDetailId, []string{"discount", "voucher", "promotion"}, ids...)
		log.IfError(err)
		resultChan <- result
	}(detailIds, salesDetailDiscountChan)

	//fetch sales_detail_promotion
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetailPromotion(domain.SalesDetailFkid, ids...)
		log.IfError(err)
		resultChan <- result
	}(detailIds, salesDetailPromotionChan)

	//fetch sales_detail_tax, by type: tax, service, voucher, discount
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetailTaxPerCategory(ids...)
		log.IfError(err)
		resultChan <- result
	}(detailIds, salesDetailTaxChan)

	//fetch employee
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchEmployee(ids...)
		log.IfError(err)
		resultChan <- result
	}(employeeIds, employeeChan)

	//fetch product with: category, subcategory, variant
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := s.repoPrimary.FetchProductByDetailIds(ids...)
		log.IfError(err)
		resultChan <- result
	}(productDetailIds, productChan)

	//fetch shift
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchShiftByIds(ids...)
		log.IfError(err)
		resultChan <- result
	}(shiftIds, shiftChan)

	//fetch sales payment
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := s.repoPrimary.FetchSalesPayment(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, paymentChan)

	shifts := <-shiftChan
	employees := <-employeeChan
	salesDetailTaxes := <-salesDetailTaxChan
	salesDetailPromotions := <-salesDetailPromotionChan
	salesDetailDiscounts := <-salesDetailDiscountChan
	salesVoid := <-salesVoidChan
	products := <-productChan
	payments := <-paymentChan

	productsMap := array.FlatMapArray(products, "product_detail_id")
	employeeMap := array.FlatMapArray(employees, "employee_id")
	shiftMap := array.FlatMapArray(shifts, "shift_id")
	salesDetailPromoMap := array.FlatMapArray(salesDetailPromotions, "sales_detail_fkid")
	salesDetailDiscMap := array.FlatMapArray(salesDetailDiscounts, "sales_detail_id")
	salesVoidMap := array.FlatMapArray(salesVoid, "sales_detail_fkid")
	salesDetailTaxMap := array.FlatMapArray(salesDetailTaxes, "sales_detail_fkid")

	salesPaymentGroup := array.GroupBy(payments, "sales_fkid")
	timeZoneOffset := 25200
	if request.TimeZone > 0 {
		timeZoneOffset = request.TimeZone
	}

	offsetId := int64(0)
	for i, detail := range details {
		detailId := cast.ToString(detail["sales_detail_id"])
		product := productsMap[cast.ToString(detail["product_detail_fkid"])]
		discInfo := array.RemoveEmpty([]string{cast.ToString(detail["discount_info"]), cast.ToString(detail["voucher_info"])})
		payments := salesPaymentGroup[cast.ToString(detail["sales_id"])]
		salesDetailPromo := salesDetailPromoMap[detailId]
		salesDetailDisc := salesDetailDiscMap[detailId]
		salesDetailTax := salesDetailTaxMap[detailId]

		details[i]["voucher_tax"] = salesDetailTax["voucher"]
		details[i]["voucher_sales"] = salesDetailDisc["voucher"]
		details[i]["disc_tax"] = salesDetailTax["discount"]
		details[i]["disc_sales"] = salesDetailDisc["discount"]
		details[i]["total_voucher"] = cast.ToInt(salesDetailTax["voucher"]) + cast.ToInt(salesDetailDisc["voucher"])
		details[i]["total_discount"] = cast.ToInt(salesDetailTax["discount"]) + cast.ToInt(salesDetailDisc["discount"]) + cast.ToInt(detail["discount"])
		details[i]["qty"] = cast.ToInt(detail["qty"]) - int(math.Abs(cast.ToFloat64(salesVoidMap[detailId]["qty"])))
		details[i]["sub_total"] = cast.ToInt(details[i]["qty"]) * cast.ToInt(details[i]["price"])
		details[i]["promo"] = cast.ToInt(salesDetailPromo["total"]) + cast.ToInt(salesDetailDisc["total"])
		details[i]["keterangan_discount"] = strings.Join(discInfo, ",")
		details[i]["category"] = product["category_name"]
		details[i]["sub_category"] = product["subcategory_name"]
		details[i]["sku"] = product["sku"]
		details[i]["product_name"] = product["name"]
		details[i]["product"] = product["product_name"]
		details[i]["variant_name"] = product["variant_name"]

		details[i]["tax"] = salesDetailTax["tax"]
		details[i]["service"] = salesDetailTax["service"]

		details[i]["employee"] = employeeMap[cast.ToString(detail["employee_fkid"])]["name"]
		details[i]["oprator"] = employeeMap[cast.ToString(detail["operator_id"])]["name"]
		details[i]["operator"] = employeeMap[cast.ToString(detail["operator_id"])]["name"]
		details[i]["shift_name"] = shiftMap[cast.ToString(detail["shift_fkid"])]["name"]
		details[i]["tanggal"] = utils.MillisToDate(cast.ToInt64(detail["time_created"]), timeZoneOffset)
		details[i]["grand_total2"] = cast.ToInt(details[i]["sub_total"]) + cast.ToInt(details[i]["tax"]) + cast.ToInt(details[i]["service"]) - cast.ToInt(details[i]["total_discount"]) - cast.ToInt(details[i]["promo"]) - cast.ToInt(details[i]["total_voucher"])

		paymentInfo := make(map[string]int)
		for _, p := range payments {
			paymentInfo[cast.ToString(p["method"])] = cast.ToInt(p["total"])
		}
		details[i]["pembayaran"] = paymentInfo

		if id := cast.ToInt64(detailId); id > offsetId {
			offsetId = id
		}
	}

	result["data"] = details
	result["offset_id"] = offsetId
	return result, nil
}
