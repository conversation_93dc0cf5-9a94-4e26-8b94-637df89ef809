package domain

import (
	"strings"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/array"
)

const (
	UserTypeAdmin    = "admin"
	UserTypeEmployee = "employee"
)

type User struct {
	Name              string   `json:"name,omitempty"`
	Email             string   `json:"email,omitempty"`
	Phone             string   `json:"phone,omitempty"`
	UserType          string   `json:"user_type,omitempty"`
	UserId            string   `json:"user_id,omitempty"`
	BusinessId        string   `json:"business_id,omitempty"` //admin_id
	AccountId         string   `json:"account_id,omitempty"`
	OutletAccess      []int    `json:"outlet_access,omitempty"`
	NotificationToken []string `json:"notification_token,omitempty"`
	Role              string   `json:"role,omitempty"`
}

type UserRole struct {
	OutletAccess string `json:"outlet_access"`
}

func GetUserSessionOfFastHttp(ctx *fasthttp.RequestCtx) User {
	h := &ctx.Request.Header
	outlets := strings.Split(string(h.Peek("outlet_access")), ",")
	outletIds := array.TransformStringToInt(outlets)

	return User{
		UserType:     string(h.Peek("user_type")),
		UserId:       string(h.Peek("user_id")),
		BusinessId:   string(h.Peek("admin_id")),
		OutletAccess: outletIds,
		Role:         string(h.Peek("role")),
	}
}

func (u User) ValidateOuletAccess(outletIds []int) []int {
	if u.UserType == UserTypeAdmin {
		return outletIds
	}

	//if no specific outlet requested, use the ids in which user has access
	if len(outletIds) == 0 {
		return u.OutletAccess
	}

	result := make([]int, 0)
	for _, id := range outletIds {
		if array.IsIn(id, u.OutletAccess) {
			result = append(result, id)
		}
	}
	return result
}
