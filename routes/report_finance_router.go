package routes

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-report/controller"
)

func SetReportFinanceRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/finance/cash_flow/category/:category/:outletId/:shiftId/:date/:dateAdd", controller.GetReportCashFlowByCategory)
	router.GET("/finance/cash_flow/media/:category/:outletId/:shiftId/:date/:dateAdd", controller.GetReportCashFlowByMedia)

	//cashflow summary : category, media
	router.GET("/finance/cash_flow/total/:dataType/:outletId/:shiftId/:dateStart/:dateEnd/:dateAdd", controller.GetReportCashFlowTotal)
	return router
}
