package v1

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func getComplimentByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment='COMPLIMENT', s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY o.outlet_id
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getDutyMealsByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			o.outlet_id,
			o.name AS outlet_name,
			sum(IF(s.status='Success' AND s.data_status='on' AND s.payment='DUTY MEALS', s.grand_total,0)) AS sum_grandtotal
		FROM sales s
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY o.outlet_id
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getFreeByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}
	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(case when (s.status='Success' and s.data_status='on' and sd.discount = sd.sub_total) THEN sd.discount ELSE 0 END) AS free
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND sd.discount > 0
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getDiscountReceiptByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}
	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(s.discount) AS sales_discount
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND s.data_status = 'on'
		AND s.status = 'success'
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

/* KEMUNGKINAN NGE-BUG DISINI */
func getDiscountItemByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query1 := db.GetSQLRaw(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(sd.discount) - (
				SELECT IFNULL(sum(sv.discount),0)
				FROM sales_void sv
				WHERE sv.product_fkid=sd.product_fkid
				AND sv.time_created >= ?
				AND sv.time_created <= ?
			) AS discount_item
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id = sd.sales_fkid
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND s.status = 'Success'
		AND s.data_status = 'on'
		AND sd.discount < sd.sub_total
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid,sd.product_fkid
	`, timeStart, timeEnd, adminId, timeStart, timeEnd)

	query, err := db.QueryArray(`
		SELECT outlet_id, outlet_name, sum(discount_item) AS discount_item
		FROM (` + query1 + `) tb
		WHERE discount_item > 0
		GROUP BY outlet_id
	`)
	utils.CheckErr(err)
	return query
}

func getDiscountGratuityByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(st.total) AS sales_tax_discount
		FROM sales_tax st
		LEFT JOIN sales s ON s.sales_id = st.sales_fkid
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN gratuity g ON g.gratuity_id = st.tax_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND g.tax_category = 'discount'
		AND s.data_status = 'on'
		AND s.status = 'success'
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getVoucherByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = " AND s.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(CASE WHEN(s.data_status='on' and s.status='Success' and g.tax_category='voucher' `+whereInOutlet+`) THEN st.total ELSE 0 END) AS sales_tax_voucher
		FROM sales_tax st
		LEFT JOIN sales s ON s.sales_id = st.sales_fkid
		LEFT JOIN outlets o ON o.outlet_id = s.outlet_fkid
		LEFT JOIN gratuity g ON g.gratuity_id = st.tax_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?		
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getVoucherReceiptByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}
	query, err := db.QueryArray(`
		SELECT
			s.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(IF(s.data_status='on' AND s.status='Success', s.voucher, 0)) AS sales_voucher
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		`+whereInOutlet+`
		GROUP BY s.outlet_fkid
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func EntertainIncomeByShift_map(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	// INIT VARIABLE
	outletIdMap := map[string]interface{}{} //untuk menampung data outlet yang ada

	//get data
	chanCompliment := make(chan map[string]interface{})
	go func() {
		query := getComplimentByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"compliment":  data["sum_grandtotal"],
			}
		}
		chanCompliment <- dataRender
	}()
	chanDutyMeals := make(chan map[string]interface{})
	go func() {
		query := getDutyMealsByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"dutymeals":   data["sum_grandtotal"],
			}
		}
		chanDutyMeals <- dataRender
	}()
	chanFree := make(chan map[string]interface{})
	go func() {
		query := getFreeByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"free":        data["free"],
			}
		}
		chanFree <- dataRender
	}()
	chanDiscountReceipt := make(chan map[string]interface{})
	go func() {
		query := getDiscountReceiptByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":        data["outlet_id"],
				"outlet_name":      data["outlet_name"],
				"discount_receipt": data["sales_discount"],
			}
		}
		chanDiscountReceipt <- dataRender
	}()
	chanDiscountItem := make(chan map[string]interface{})
	go func() {
		query := getDiscountItemByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":     data["outlet_id"],
				"outlet_name":   data["outlet_name"],
				"discount_item": data["discount_item"],
			}
		}
		chanDiscountItem <- dataRender
	}()
	chanDiscountGratuity := make(chan map[string]interface{})
	go func() {
		query := getDiscountGratuityByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":         data["outlet_id"],
				"outlet_name":       data["outlet_name"],
				"discount_gratuity": data["sales_tax_discount"],
			}
		}
		chanDiscountGratuity <- dataRender
	}()
	chanVoucher := make(chan map[string]interface{})
	go func() {
		query := getVoucherByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":   data["outlet_id"],
				"outlet_name": data["outlet_name"],
				"voucher":     data["sales_tax_voucher"],
			}
		}
		chanVoucher <- dataRender
	}()
	chanVoucherReceipt := make(chan map[string]interface{})
	go func() {
		query := getVoucherReceiptByShift(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])
			//append outlet
			outletIdMap[id] = data["outlet_name"]

			//set map
			dataRender[id] = map[string]interface{}{
				"outlet_id":       data["outlet_id"],
				"outlet_name":     data["outlet_name"],
				"voucher_receipt": data["sales_voucher"],
			}
		}
		chanVoucherReceipt <- dataRender
	}()

	compliment := <-chanCompliment
	dutymeals := <-chanDutyMeals
	free := <-chanFree
	discountReceipt := <-chanDiscountReceipt
	discountItem := <-chanDiscountItem
	discountGratuity := <-chanDiscountGratuity
	voucher := <-chanVoucher
	voucherReceipt := <-chanVoucherReceipt

	dataRender := []map[string]interface{}{}
	for k, v := range outletIdMap {
		dataCompliment := compliment[k]
		if dataCompliment == nil {
			dataCompliment = 0
		} else {
			dataCompliment = dataCompliment.(map[string]interface{})["compliment"]
		}
		dataDutyMeals := dutymeals[k]
		if dataDutyMeals == nil {
			dataDutyMeals = 0
		} else {
			dataDutyMeals = dataDutyMeals.(map[string]interface{})["dutymeals"]
		}
		dataFree := free[k]
		if dataFree == nil {
			dataFree = 0
		} else {
			dataFree = dataFree.(map[string]interface{})["free"]
		}
		dataVoucher := voucher[k]
		if dataVoucher == nil {
			dataVoucher = 0
		} else {
			dataVoucher = dataVoucher.(map[string]interface{})["voucher"]
		}
		dataVoucherReceipt := voucherReceipt[k]
		if dataVoucherReceipt == nil {
			dataVoucherReceipt = 0
		} else {
			dataVoucherReceipt = dataVoucherReceipt.(map[string]interface{})["voucher_receipt"]
		}
		dataDiscountReceipt := discountReceipt[k]
		if dataDiscountReceipt == nil {
			dataDiscountReceipt = 0
		} else {
			dataDiscountReceipt = dataDiscountReceipt.(map[string]interface{})["discount_receipt"]
		}
		dataDiscountItem := discountItem[k]
		if dataDiscountItem == nil {
			dataDiscountItem = 0
		} else {
			dataDiscountItem = dataDiscountItem.(map[string]interface{})["discount_item"]
		}
		dataDiscountGratuity := discountGratuity[k]
		if dataDiscountGratuity == nil {
			dataDiscountGratuity = 0
		} else {
			dataDiscountGratuity = dataDiscountGratuity.(map[string]interface{})["discount_gratuity"]
		}

		dataDiscountAll := cast.ToInt64(dataDiscountReceipt) + cast.ToInt64(dataDiscountItem) + cast.ToInt64(dataDiscountGratuity)
		dataVoucherAll := cast.ToInt64(dataVoucher) + cast.ToInt64(dataVoucherReceipt)

		dataRender = append(dataRender, map[string]interface{}{
			"outlet_id":   k,
			"outlet_name": v,
			"detail": map[string]interface{}{
				"compliment": dataCompliment,
				"dutymeals":  dataDutyMeals,
				"free":       dataFree,
				"voucher":    dataVoucherAll,
				"discount":   dataDiscountAll,
			},
		})
	}

	return dataRender
}

func EntertainIncomeByShift_v1(ctx *fasthttp.RequestCtx) {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
	} else {
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess, ",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		data := EntertainIncomeByShift_map(adminId, timeStart, timeEnd, outletIds)

		//output
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: true,
			Code:   200,
			Data:   data,
		})
	}
}
