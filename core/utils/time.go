package utils

import (
	"time"
)

func MillisToDate(millis int64, timeOffset int) string {
	return time.UnixMilli(millis + int64(timeOffset*1000)).Format("2006-01-02 15:04")
}

func MillisToDateTime(timeMillis int64) (string, error) {
	_, offset := time.Now().Zone()
	diff := int64(0 - offset) //25200 is developer offset

	t := time.Unix((timeMillis/1000)+diff, 0)
	return t.Format("2006-01-02 15:04:05"), nil
}

func MillisToTime(timeMillis int64) time.Time {
	_, offset := time.Now().Zone()
	diff := int64(0 - offset) //25200 is developer offset

	t := time.Unix((timeMillis/1000)+diff, 0)
	return t
}

// get days of the month
func DaysInMonth(month, year int) int {
	// This is equivalent to time.daysIn(m, year).
	m := time.Month(month)
	return time.Date(year, m+1, 0, 0, 0, 0, 0, time.UTC).Day()
}

func CurrentMillis() int64 {
	return time.Now().UnixNano() / int64(time.Millisecond)
}

func GetTimestampMillis(daysInterval int) int64 {
	now := time.Now()
	targetDay := now.AddDate(0, 0, daysInterval)
	targetTime := time.Date(targetDay.Year(), targetDay.Month(), targetDay.Day(), 0, 0, 0, 0, time.UTC)
	return targetTime.UnixNano() / int64(time.Millisecond)
}

func GetDate() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset (WIB)
	return time.Unix(time.Now().Unix()+diff, 0).Format("02/01/2006 15:04:05")
}

// AdjustTimeMillis checks if a given timestamp (in milliseconds) falls within
// the current day, considering a provided time offset.
//
// Arguments:
//   - timeMillis: The timestamp in milliseconds (e.g., 1720684684812).
//   - timeOffset: The time offset in milliseconds (e.g., 25200000 for 7 hours difference).
//     A positive offset adds time, while a negative offset subtracts time.
//
// Returns:
//   - If the given timeMillis falls on the current day (considering the timeOffset), the
//     function returns the current time in milliseconds (with the offset applied).
//   - If the given timeMillis is not on the current day, the function returns the
//     original timeMillis value passed as an argument.
func AdjustTimeMillis(timeMillis int64, timeOffset int64) int64 {
	// Convert millis to time.Time
	t := time.Unix(0, timeMillis*int64(time.Millisecond))

	// Apply time offset
	t = t.Add(time.Duration(timeOffset) * time.Millisecond)

	// Get current time with offset
	now := time.Now().UTC().Add(time.Duration(timeOffset) * time.Millisecond)

	// Check if the given time is on the same day as now
	if t.Year() == now.Year() && t.Month() == now.Month() && t.Day() == now.Day() {
		return now.UnixNano() / int64(time.Millisecond)
	}

	return timeMillis
}
