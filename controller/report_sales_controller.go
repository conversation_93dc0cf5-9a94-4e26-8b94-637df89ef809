package controller

import (
	"encoding/json"
	"fmt"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strconv"
	"strings"
)

func GetSalesAnalysisByNominal(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	shiftId := ctx.UserValue("shiftId")
	date := ctx.UserValue("date")
	dateAdd := ctx.UserValue("dateAdd")
	length := ctx.UserValue("length")
	start := ctx.UserValue("start")

	params := make([]interface{}, 0)
	params = append(params, dateAdd, date)
	whereClause := ""
	if utils.ToString(shiftId) != "0" {
		whereClause += " and sf.shift_id = ? "
		params = append(params, shiftId)
	}
	if utils.ToString(outletId) != "0" {
		whereClause += " and s.outlet_fkid = ? "
		params = append(params, outletId)
	}
	params = append(params, length, start)

	sql := "select distinct product_detail_fkid from sales_detail sd " +
		"join sales s on sd.sales_fkid = s.sales_id " +
		"join open_shift o on s.open_shift_fkid = o.open_shift_id " +
		"join shift sf on o.shift_fkid = sf.shift_id " +
		"where from_unixtime(o.time_open / 1000 + ?, '%Y-%m') = ? " + whereClause +
		"order by product_detail_fkid limit ? offset ?"

	productIds, err := db.QueryArray(sql, params...)
	if log.IfError(err) {
		fmt.Println("Query error - ", err, "\n", sql)
	}

	if len(productIds) == 0 {
		return
	}

	params = make([]interface{}, 0)
	params = append(params, dateAdd, outletId, dateAdd, date)
	for _, id := range productIds {
		params = append(params, id["product_detail_fkid"])
	}

	whereClause = ""
	if utils.ToString(shiftId) != "0" {
		whereClause += " and sf.shift_id = ? "
		params = append(params, shiftId)
	}
	if utils.ToString(outletId) != "0" {
		whereClause += " and s.outlet_fkid = ? "
		params = append(params, outletId)
	}

	params = append(params, dateAdd)

	sql = "select min(p.sku) as sku, min(subcategory.name) as subcategory, min(p.name) product, " +
		"sum(detail.sub_total) subtotal, min(detail.product_detail_fkid) productDetailId, " +
		"from_unixtime(min(o.time_open) / 1000 + ?, '%Y-%m-%d') as tgl from sales s " +
		"join sales_detail detail on s.sales_id = detail.sales_fkid " +
		"join open_shift o on s.open_shift_fkid = o.open_shift_id " +
		"join shift sf on o.shift_fkid = sf.shift_id " +
		"join products p on detail.product_fkid = p.product_id " +
		"join products_subcategory subcategory on p.product_subcategory_fkid = subcategory.product_subcategory_id " +
		"where s.outlet_fkid=? and from_unixtime(o.time_open / 1000 + ?, '%Y-%m') = ? " +
		"AND detail.product_detail_fkid IN (" + strings.Repeat("?,", len(productIds)-1) + "?) " + whereClause +
		"and s.status='Success' " +
		"group by from_unixtime(o.time_open / 1000 + ?, '%Y-%m-%d'), detail.product_detail_fkid"

	dataArr, err := db.QueryArray(sql, params...)
	if log.IfError(err) {
		fmt.Println("Query error - ", err, "\n", sql)
	}

	dataCollect := make(map[string]map[string]interface{})
	for _, data := range dataArr {
		date := utils.ToString(data["tgl"])[8:]
		dateInt, _ := strconv.Atoi(date)
		dataByProduct := dataCollect[utils.ToString(data["product"])]
		if dataByProduct == nil {
			dataByProduct = make(map[string]interface{})
		}

		sobTotal, _ := strconv.Atoi(utils.ToString(data["subtotal"]))
		dataByProduct[utils.ToString(dateInt)] = sobTotal
		dataByProduct["product_name"] = data["product"]
		dataByProduct["sku"] = data["sku"]
		dataByProduct["category"] = data["subcategory"]
		dataByProduct["date"] = data["tgl"]
		dataCollect[utils.ToString(data["product"])] = dataByProduct
	}

	dataResult := make([]map[string]interface{}, 0)
	for _, dataById := range dataCollect {
		//dataByProduct := make(map[string]interface{})
		totalPerWeek := 0
		grandTotal := 0
		for i := 1; i <= 31; i++ {
			if dataById[strconv.Itoa(i)] == nil {
				dataById[strconv.Itoa(i)] = 0
			} else {
				totalPerWeek += dataById[strconv.Itoa(i)].(int)
			}

			if i%7 == 0 {
				dataById["w"+strconv.Itoa(i/7)] = totalPerWeek
				grandTotal += totalPerWeek
				totalPerWeek = 0
			}
		}
		dataById["total"] = grandTotal
		dataResult = append(dataResult, dataById)
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(dataResult)
}

func GetSalesAnalysisByNominalV2(ctx *fasthttp.RequestCtx) {
	outletId := ctx.UserValue("outletId")
	date := ctx.UserValue("date")
	dateAdd := ctx.UserValue("dateAdd")
	shiftId := ctx.UserValue("shiftId")

	params := make([]interface{}, 0)
	params = append(params, dateAdd, dateAdd, date)

	whereClause := ""
	if utils.ToString(shiftId) != "0" {
		whereClause += " and sf.shift_id = ? "
		params = append(params, shiftId)
	}
	if utils.ToString(outletId) != "0" {
		whereClause += " and s.outlet_fkid = ? "
		params = append(params, outletId)
	}

	params = append(params, dateAdd)

	sql := "select min(p.sku) as sku, min(subcategory.name) as subcategory, min(p.name) product, sum(detail.sub_total) subtotal, " +
		"min(detail.product_detail_fkid) productDetailId, from_unixtime(min(o.time_open) / 1000 + ?, '%Y-%m-%d') as tgl " +
		"from sales s join sales_detail detail on s.sales_id = detail.sales_fkid " +
		"join open_shift o on s.open_shift_fkid = o.open_shift_id " +
		"join products p on detail.product_fkid = p.product_id " +
		"join shift sf on o.shift_fkid = sf.shift_id " +
		"join products_subcategory subcategory on p.product_subcategory_fkid = subcategory.product_subcategory_id " +
		"where from_unixtime(o.time_open / 1000 + ?, '%Y-%m') = ? " + whereClause +
		"and s.status='Success' " +
		"group by from_unixtime(o.time_open / 1000 + ?, '%Y-%m-%d'), detail.product_detail_fkid " +
		"order by detail.product_detail_fkid, tgl"

	dataArr, err := db.QueryArray(sql, params...)
	if log.IfErrorSetStatus(ctx, err) {
		fmt.Println("Query error - ", err, "\n", sql)
		return
	}
	fmt.Println("Size : ", len(dataArr))

	lastProdId := ""
	day := 1
	totalPerWeek := 0
	dataResult := make([]map[string]interface{}, 0)
	dataPerProduct := make(map[string]interface{})

	for _, data := range dataArr {
		date := utils.ToString(data["tgl"])[8:]
		dateInt, _ := strconv.Atoi(date)

		if lastProdId == "" {
			lastProdId = utils.ToString(data["productDetailId"])
		} else {
			if lastProdId != utils.ToString(data["productDetailId"]) {
				for day <= 31 {
					dataPerProduct[strconv.Itoa(day)] = "0"
					if day%7 == 0 {
						dataPerProduct[fmt.Sprintf("w%d", day/7)] = utils.CurrencyFormat(totalPerWeek)
						totalPerWeek = 0
					}
					day++
				}
				dataPerProduct["product_name"] = data["product"]
				dataPerProduct["sku"] = data["sku"]
				dataPerProduct["category"] = data["subcategory"]
				dataPerProduct["date"] = data["tgl"]
				dataResult = append(dataResult, dataPerProduct)
				dataPerProduct = make(map[string]interface{})
				day = 1
			}
		}

		sobTotal, _ := strconv.Atoi(utils.ToString(data["subtotal"]))

		for dateInt != day {
			dataPerProduct[strconv.Itoa(day)] = "0"
			if day%7 == 0 {
				dataPerProduct[fmt.Sprintf("w%d", day/7)] = utils.CurrencyFormat(totalPerWeek)
				totalPerWeek = 0
			}
			day++
		}

		dataPerProduct[strconv.Itoa(day)] = utils.CurrencyFormat(sobTotal)
		totalPerWeek += sobTotal

		if day%7 == 0 {
			dataPerProduct[fmt.Sprintf("w%d", day/7)] = utils.CurrencyFormat(totalPerWeek)
			totalPerWeek = 0
		}

		day++
		lastProdId = utils.ToString(data["productDetailId"])
	}

	ctx.SetContentType("application/json")
	json.NewEncoder(ctx).Encode(dataResult)
}
