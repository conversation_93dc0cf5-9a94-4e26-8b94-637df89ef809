package v1

import (
	"encoding/json"
	"fmt"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"time"
)

func DashboardDataAll_HTTP(ctx *fasthttp.RequestCtx)  {
	//data post
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get post value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	timeDiff := cast.ToInt64(post["time_diff"]) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])

	timeStartPrev := cast.ToInt64(post["time_start_previous"])
	timeEndPrev := cast.ToInt64(post["time_end_previous"])
	limit := post["limit"]

	//render outletlist
	//outletIdMap := map[string]interface{}{}

	benchStart := time.Now()
	fmt.Println("bench start: ",benchStart)
	//data List
	chanEntertainIncome := make(chan []map[string]interface{})
	go func() {
		chanEntertainIncome <- EntertainIncome_map(adminId, timeStart, timeEnd, outletIds)
	}()
	chanSalesTransaction := make(chan []map[string]interface{})
	go func() {
		chanSalesTransaction <- SalesTransaction_map(adminId, timeStart, timeEnd, outletIds)
	}()
	chanSalesCategory := make(chan map[string]interface{})
	go func() {
		chanSalesCategory <- SalesByAllCategory_map(adminId, timeStart, timeEnd, outletIds)
	}()
	chanSalesAnalysis := make(chan map[string]interface{})
	go func() {
		chanSalesAnalysis <- SalesAnalysis_map(adminId, timeStart, timeEnd, timeDiff, outletIds)
	}()
	chanSalesTop := make(chan map[string]interface{})
	go func() {
		chanSalesTop <- SalesTop_map(adminId, timeStart, timeEnd, timeStartPrev, timeEndPrev, outletIds, limit)
	}()

	sales_transaction := <-chanSalesTransaction
	entertain_income := <-chanEntertainIncome
	sales_category := <-chanSalesCategory
	sales_analysis := <-chanSalesAnalysis
	sales_top := <- chanSalesTop

	benchEnd := time.Since(benchStart)
	fmt.Println("bench end  : ",benchEnd)



	dataToJsonList := map[string]interface{}{
		"sales_transaction": sales_transaction,
		"entertain_income": entertain_income,
		"sales_category": sales_category,
		"sales_analysis": sales_analysis,
		"sales_top": sales_top,
	}

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(dataToJsonList)
}
