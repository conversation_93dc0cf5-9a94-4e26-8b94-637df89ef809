// Code generated by mockery v2.38.0. DO NOT EDIT.

package mocks

import (
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	hpp "gitlab.com/uniqdev/backend/api-report/module/hpp"

	mock "github.com/stretchr/testify/mock"

	models "gitlab.com/uniqdev/backend/api-report/models"
)

// Repository is an autogenerated mock type for the Repository type
type Repository struct {
	mock.Mock
}

type Repository_Expecter struct {
	mock *mock.Mock
}

func (_m *Repository) EXPECT() *Repository_Expecter {
	return &Repository_Expecter{mock: &_m.Mock}
}

// AdjustStockPriceDetail provides a mock function with given fields: adjust, remove, stockNew
func (_m *Repository) AdjustStockPriceDetail(adjust []models.StockPriceDetailEntity, remove []int64, stockNew []map[string]interface{}) (int64, error) {
	ret := _m.Called(adjust, remove, stockNew)

	if len(ret) == 0 {
		panic("no return value specified for AdjustStockPriceDetail")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func([]models.StockPriceDetailEntity, []int64, []map[string]interface{}) (int64, error)); ok {
		return rf(adjust, remove, stockNew)
	}
	if rf, ok := ret.Get(0).(func([]models.StockPriceDetailEntity, []int64, []map[string]interface{}) int64); ok {
		r0 = rf(adjust, remove, stockNew)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func([]models.StockPriceDetailEntity, []int64, []map[string]interface{}) error); ok {
		r1 = rf(adjust, remove, stockNew)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_AdjustStockPriceDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AdjustStockPriceDetail'
type Repository_AdjustStockPriceDetail_Call struct {
	*mock.Call
}

// AdjustStockPriceDetail is a helper method to define mock.On call
//   - adjust []models.StockPriceDetailEntity
//   - remove []int64
//   - stockNew []map[string]interface{}
func (_e *Repository_Expecter) AdjustStockPriceDetail(adjust interface{}, remove interface{}, stockNew interface{}) *Repository_AdjustStockPriceDetail_Call {
	return &Repository_AdjustStockPriceDetail_Call{Call: _e.mock.On("AdjustStockPriceDetail", adjust, remove, stockNew)}
}

func (_c *Repository_AdjustStockPriceDetail_Call) Run(run func(adjust []models.StockPriceDetailEntity, remove []int64, stockNew []map[string]interface{})) *Repository_AdjustStockPriceDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]models.StockPriceDetailEntity), args[1].([]int64), args[2].([]map[string]interface{}))
	})
	return _c
}

func (_c *Repository_AdjustStockPriceDetail_Call) Return(_a0 int64, _a1 error) *Repository_AdjustStockPriceDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_AdjustStockPriceDetail_Call) RunAndReturn(run func([]models.StockPriceDetailEntity, []int64, []map[string]interface{}) (int64, error)) *Repository_AdjustStockPriceDetail_Call {
	_c.Call.Return(run)
	return _c
}

// AdjustStockPriceQtyOut provides a mock function with given fields: stockPrice
func (_m *Repository) AdjustStockPriceQtyOut(stockPrice map[int64]float32) error {
	ret := _m.Called(stockPrice)

	if len(ret) == 0 {
		panic("no return value specified for AdjustStockPriceQtyOut")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(map[int64]float32) error); ok {
		r0 = rf(stockPrice)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_AdjustStockPriceQtyOut_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'AdjustStockPriceQtyOut'
type Repository_AdjustStockPriceQtyOut_Call struct {
	*mock.Call
}

// AdjustStockPriceQtyOut is a helper method to define mock.On call
//   - stockPrice map[int64]float32
func (_e *Repository_Expecter) AdjustStockPriceQtyOut(stockPrice interface{}) *Repository_AdjustStockPriceQtyOut_Call {
	return &Repository_AdjustStockPriceQtyOut_Call{Call: _e.mock.On("AdjustStockPriceQtyOut", stockPrice)}
}

func (_c *Repository_AdjustStockPriceQtyOut_Call) Run(run func(stockPrice map[int64]float32)) *Repository_AdjustStockPriceQtyOut_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(map[int64]float32))
	})
	return _c
}

func (_c *Repository_AdjustStockPriceQtyOut_Call) Return(_a0 error) *Repository_AdjustStockPriceQtyOut_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_AdjustStockPriceQtyOut_Call) RunAndReturn(run func(map[int64]float32) error) *Repository_AdjustStockPriceQtyOut_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteHppById provides a mock function with given fields: stockPriceId
func (_m *Repository) DeleteHppById(stockPriceId int) error {
	ret := _m.Called(stockPriceId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteHppById")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(stockPriceId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteHppById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteHppById'
type Repository_DeleteHppById_Call struct {
	*mock.Call
}

// DeleteHppById is a helper method to define mock.On call
//   - stockPriceId int
func (_e *Repository_Expecter) DeleteHppById(stockPriceId interface{}) *Repository_DeleteHppById_Call {
	return &Repository_DeleteHppById_Call{Call: _e.mock.On("DeleteHppById", stockPriceId)}
}

func (_c *Repository_DeleteHppById_Call) Run(run func(stockPriceId int)) *Repository_DeleteHppById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_DeleteHppById_Call) Return(_a0 error) *Repository_DeleteHppById_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteHppById_Call) RunAndReturn(run func(int) error) *Repository_DeleteHppById_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteHppByIdSoftly provides a mock function with given fields: stockPriceId
func (_m *Repository) DeleteHppByIdSoftly(stockPriceId int) error {
	ret := _m.Called(stockPriceId)

	if len(ret) == 0 {
		panic("no return value specified for DeleteHppByIdSoftly")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int) error); ok {
		r0 = rf(stockPriceId)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteHppByIdSoftly_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteHppByIdSoftly'
type Repository_DeleteHppByIdSoftly_Call struct {
	*mock.Call
}

// DeleteHppByIdSoftly is a helper method to define mock.On call
//   - stockPriceId int
func (_e *Repository_Expecter) DeleteHppByIdSoftly(stockPriceId interface{}) *Repository_DeleteHppByIdSoftly_Call {
	return &Repository_DeleteHppByIdSoftly_Call{Call: _e.mock.On("DeleteHppByIdSoftly", stockPriceId)}
}

func (_c *Repository_DeleteHppByIdSoftly_Call) Run(run func(stockPriceId int)) *Repository_DeleteHppByIdSoftly_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_DeleteHppByIdSoftly_Call) Return(_a0 error) *Repository_DeleteHppByIdSoftly_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteHppByIdSoftly_Call) RunAndReturn(run func(int) error) *Repository_DeleteHppByIdSoftly_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteStockPriceDetail provides a mock function with given fields: id
func (_m *Repository) DeleteStockPriceDetail(id int64) error {
	ret := _m.Called(id)

	if len(ret) == 0 {
		panic("no return value specified for DeleteStockPriceDetail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int64) error); ok {
		r0 = rf(id)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteStockPriceDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteStockPriceDetail'
type Repository_DeleteStockPriceDetail_Call struct {
	*mock.Call
}

// DeleteStockPriceDetail is a helper method to define mock.On call
//   - id int64
func (_e *Repository_Expecter) DeleteStockPriceDetail(id interface{}) *Repository_DeleteStockPriceDetail_Call {
	return &Repository_DeleteStockPriceDetail_Call{Call: _e.mock.On("DeleteStockPriceDetail", id)}
}

func (_c *Repository_DeleteStockPriceDetail_Call) Run(run func(id int64)) *Repository_DeleteStockPriceDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64))
	})
	return _c
}

func (_c *Repository_DeleteStockPriceDetail_Call) Return(_a0 error) *Repository_DeleteStockPriceDetail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteStockPriceDetail_Call) RunAndReturn(run func(int64) error) *Repository_DeleteStockPriceDetail_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteStockPriceDetailByOutsourceId provides a mock function with given fields: id, source
func (_m *Repository) DeleteStockPriceDetailByOutsourceId(id string, source string) error {
	ret := _m.Called(id, source)

	if len(ret) == 0 {
		panic("no return value specified for DeleteStockPriceDetailByOutsourceId")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(string, string) error); ok {
		r0 = rf(id, source)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteStockPriceDetailByOutsourceId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteStockPriceDetailByOutsourceId'
type Repository_DeleteStockPriceDetailByOutsourceId_Call struct {
	*mock.Call
}

// DeleteStockPriceDetailByOutsourceId is a helper method to define mock.On call
//   - id string
//   - source string
func (_e *Repository_Expecter) DeleteStockPriceDetailByOutsourceId(id interface{}, source interface{}) *Repository_DeleteStockPriceDetailByOutsourceId_Call {
	return &Repository_DeleteStockPriceDetailByOutsourceId_Call{Call: _e.mock.On("DeleteStockPriceDetailByOutsourceId", id, source)}
}

func (_c *Repository_DeleteStockPriceDetailByOutsourceId_Call) Run(run func(id string, source string)) *Repository_DeleteStockPriceDetailByOutsourceId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *Repository_DeleteStockPriceDetailByOutsourceId_Call) Return(_a0 error) *Repository_DeleteStockPriceDetailByOutsourceId_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteStockPriceDetailByOutsourceId_Call) RunAndReturn(run func(string, string) error) *Repository_DeleteStockPriceDetailByOutsourceId_Call {
	_c.Call.Return(run)
	return _c
}

// DeleteUnUsedStockPrice provides a mock function with given fields:
func (_m *Repository) DeleteUnUsedStockPrice() error {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for DeleteUnUsedStockPrice")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_DeleteUnUsedStockPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'DeleteUnUsedStockPrice'
type Repository_DeleteUnUsedStockPrice_Call struct {
	*mock.Call
}

// DeleteUnUsedStockPrice is a helper method to define mock.On call
func (_e *Repository_Expecter) DeleteUnUsedStockPrice() *Repository_DeleteUnUsedStockPrice_Call {
	return &Repository_DeleteUnUsedStockPrice_Call{Call: _e.mock.On("DeleteUnUsedStockPrice")}
}

func (_c *Repository_DeleteUnUsedStockPrice_Call) Run(run func()) *Repository_DeleteUnUsedStockPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Repository_DeleteUnUsedStockPrice_Call) Return(_a0 error) *Repository_DeleteUnUsedStockPrice_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_DeleteUnUsedStockPrice_Call) RunAndReturn(run func() error) *Repository_DeleteUnUsedStockPrice_Call {
	_c.Call.Return(run)
	return _c
}

// FetchAnomalyMismatchStockOut provides a mock function with given fields:
func (_m *Repository) FetchAnomalyMismatchStockOut() (*[]models.AnomalyMismatchStockOut, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchAnomalyMismatchStockOut")
	}

	var r0 *[]models.AnomalyMismatchStockOut
	var r1 error
	if rf, ok := ret.Get(0).(func() (*[]models.AnomalyMismatchStockOut, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *[]models.AnomalyMismatchStockOut); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.AnomalyMismatchStockOut)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchAnomalyMismatchStockOut_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchAnomalyMismatchStockOut'
type Repository_FetchAnomalyMismatchStockOut_Call struct {
	*mock.Call
}

// FetchAnomalyMismatchStockOut is a helper method to define mock.On call
func (_e *Repository_Expecter) FetchAnomalyMismatchStockOut() *Repository_FetchAnomalyMismatchStockOut_Call {
	return &Repository_FetchAnomalyMismatchStockOut_Call{Call: _e.mock.On("FetchAnomalyMismatchStockOut")}
}

func (_c *Repository_FetchAnomalyMismatchStockOut_Call) Run(run func()) *Repository_FetchAnomalyMismatchStockOut_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Repository_FetchAnomalyMismatchStockOut_Call) Return(_a0 *[]models.AnomalyMismatchStockOut, _a1 error) *Repository_FetchAnomalyMismatchStockOut_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchAnomalyMismatchStockOut_Call) RunAndReturn(run func() (*[]models.AnomalyMismatchStockOut, error)) *Repository_FetchAnomalyMismatchStockOut_Call {
	_c.Call.Return(run)
	return _c
}

// FetchAnomalyOverStockOut provides a mock function with given fields:
func (_m *Repository) FetchAnomalyOverStockOut() (*models.AnomalyOverStockOut, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchAnomalyOverStockOut")
	}

	var r0 *models.AnomalyOverStockOut
	var r1 error
	if rf, ok := ret.Get(0).(func() (*models.AnomalyOverStockOut, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *models.AnomalyOverStockOut); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*models.AnomalyOverStockOut)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchAnomalyOverStockOut_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchAnomalyOverStockOut'
type Repository_FetchAnomalyOverStockOut_Call struct {
	*mock.Call
}

// FetchAnomalyOverStockOut is a helper method to define mock.On call
func (_e *Repository_Expecter) FetchAnomalyOverStockOut() *Repository_FetchAnomalyOverStockOut_Call {
	return &Repository_FetchAnomalyOverStockOut_Call{Call: _e.mock.On("FetchAnomalyOverStockOut")}
}

func (_c *Repository_FetchAnomalyOverStockOut_Call) Run(run func()) *Repository_FetchAnomalyOverStockOut_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Repository_FetchAnomalyOverStockOut_Call) Return(_a0 *models.AnomalyOverStockOut, _a1 error) *Repository_FetchAnomalyOverStockOut_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchAnomalyOverStockOut_Call) RunAndReturn(run func() (*models.AnomalyOverStockOut, error)) *Repository_FetchAnomalyOverStockOut_Call {
	_c.Call.Return(run)
	return _c
}

// FetchAnomalyOverStockOutDetail provides a mock function with given fields: productIds
func (_m *Repository) FetchAnomalyOverStockOutDetail(productIds ...int) (*domain.Hpp, error) {
	_va := make([]interface{}, len(productIds))
	for _i := range productIds {
		_va[_i] = productIds[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchAnomalyOverStockOutDetail")
	}

	var r0 *domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) (*domain.Hpp, error)); ok {
		return rf(productIds...)
	}
	if rf, ok := ret.Get(0).(func(...int) *domain.Hpp); ok {
		r0 = rf(productIds...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(productIds...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchAnomalyOverStockOutDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchAnomalyOverStockOutDetail'
type Repository_FetchAnomalyOverStockOutDetail_Call struct {
	*mock.Call
}

// FetchAnomalyOverStockOutDetail is a helper method to define mock.On call
//   - productIds ...int
func (_e *Repository_Expecter) FetchAnomalyOverStockOutDetail(productIds ...interface{}) *Repository_FetchAnomalyOverStockOutDetail_Call {
	return &Repository_FetchAnomalyOverStockOutDetail_Call{Call: _e.mock.On("FetchAnomalyOverStockOutDetail",
		append([]interface{}{}, productIds...)...)}
}

func (_c *Repository_FetchAnomalyOverStockOutDetail_Call) Run(run func(productIds ...int)) *Repository_FetchAnomalyOverStockOutDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(int)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchAnomalyOverStockOutDetail_Call) Return(_a0 *domain.Hpp, _a1 error) *Repository_FetchAnomalyOverStockOutDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchAnomalyOverStockOutDetail_Call) RunAndReturn(run func(...int) (*domain.Hpp, error)) *Repository_FetchAnomalyOverStockOutDetail_Call {
	_c.Call.Return(run)
	return _c
}

// FetchCurrentPrice provides a mock function with given fields: productDetailIds
func (_m *Repository) FetchCurrentPrice(productDetailIds ...int) ([]models.CurrentProductPrice, error) {
	_va := make([]interface{}, len(productDetailIds))
	for _i := range productDetailIds {
		_va[_i] = productDetailIds[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchCurrentPrice")
	}

	var r0 []models.CurrentProductPrice
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) ([]models.CurrentProductPrice, error)); ok {
		return rf(productDetailIds...)
	}
	if rf, ok := ret.Get(0).(func(...int) []models.CurrentProductPrice); ok {
		r0 = rf(productDetailIds...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.CurrentProductPrice)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(productDetailIds...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchCurrentPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchCurrentPrice'
type Repository_FetchCurrentPrice_Call struct {
	*mock.Call
}

// FetchCurrentPrice is a helper method to define mock.On call
//   - productDetailIds ...int
func (_e *Repository_Expecter) FetchCurrentPrice(productDetailIds ...interface{}) *Repository_FetchCurrentPrice_Call {
	return &Repository_FetchCurrentPrice_Call{Call: _e.mock.On("FetchCurrentPrice",
		append([]interface{}{}, productDetailIds...)...)}
}

func (_c *Repository_FetchCurrentPrice_Call) Run(run func(productDetailIds ...int)) *Repository_FetchCurrentPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(int)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchCurrentPrice_Call) Return(_a0 []models.CurrentProductPrice, _a1 error) *Repository_FetchCurrentPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchCurrentPrice_Call) RunAndReturn(run func(...int) ([]models.CurrentProductPrice, error)) *Repository_FetchCurrentPrice_Call {
	_c.Call.Return(run)
	return _c
}

// FetchCurrentPriceOfProduct provides a mock function with given fields: productDetailId
func (_m *Repository) FetchCurrentPriceOfProduct(productDetailId int) (float32, error) {
	ret := _m.Called(productDetailId)

	if len(ret) == 0 {
		panic("no return value specified for FetchCurrentPriceOfProduct")
	}

	var r0 float32
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (float32, error)); ok {
		return rf(productDetailId)
	}
	if rf, ok := ret.Get(0).(func(int) float32); ok {
		r0 = rf(productDetailId)
	} else {
		r0 = ret.Get(0).(float32)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productDetailId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchCurrentPriceOfProduct_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchCurrentPriceOfProduct'
type Repository_FetchCurrentPriceOfProduct_Call struct {
	*mock.Call
}

// FetchCurrentPriceOfProduct is a helper method to define mock.On call
//   - productDetailId int
func (_e *Repository_Expecter) FetchCurrentPriceOfProduct(productDetailId interface{}) *Repository_FetchCurrentPriceOfProduct_Call {
	return &Repository_FetchCurrentPriceOfProduct_Call{Call: _e.mock.On("FetchCurrentPriceOfProduct", productDetailId)}
}

func (_c *Repository_FetchCurrentPriceOfProduct_Call) Run(run func(productDetailId int)) *Repository_FetchCurrentPriceOfProduct_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchCurrentPriceOfProduct_Call) Return(_a0 float32, _a1 error) *Repository_FetchCurrentPriceOfProduct_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchCurrentPriceOfProduct_Call) RunAndReturn(run func(int) (float32, error)) *Repository_FetchCurrentPriceOfProduct_Call {
	_c.Call.Return(run)
	return _c
}

// FetchHppByProductId provides a mock function with given fields: productDetailId
func (_m *Repository) FetchHppByProductId(productDetailId int) ([]domain.Hpp, error) {
	ret := _m.Called(productDetailId)

	if len(ret) == 0 {
		panic("no return value specified for FetchHppByProductId")
	}

	var r0 []domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]domain.Hpp, error)); ok {
		return rf(productDetailId)
	}
	if rf, ok := ret.Get(0).(func(int) []domain.Hpp); ok {
		r0 = rf(productDetailId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productDetailId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchHppByProductId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchHppByProductId'
type Repository_FetchHppByProductId_Call struct {
	*mock.Call
}

// FetchHppByProductId is a helper method to define mock.On call
//   - productDetailId int
func (_e *Repository_Expecter) FetchHppByProductId(productDetailId interface{}) *Repository_FetchHppByProductId_Call {
	return &Repository_FetchHppByProductId_Call{Call: _e.mock.On("FetchHppByProductId", productDetailId)}
}

func (_c *Repository_FetchHppByProductId_Call) Run(run func(productDetailId int)) *Repository_FetchHppByProductId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchHppByProductId_Call) Return(_a0 []domain.Hpp, _a1 error) *Repository_FetchHppByProductId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchHppByProductId_Call) RunAndReturn(run func(int) ([]domain.Hpp, error)) *Repository_FetchHppByProductId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchHppByStockInId provides a mock function with given fields: stockInId, source
func (_m *Repository) FetchHppByStockInId(stockInId string, source string) ([]domain.Hpp, error) {
	ret := _m.Called(stockInId, source)

	if len(ret) == 0 {
		panic("no return value specified for FetchHppByStockInId")
	}

	var r0 []domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]domain.Hpp, error)); ok {
		return rf(stockInId, source)
	}
	if rf, ok := ret.Get(0).(func(string, string) []domain.Hpp); ok {
		r0 = rf(stockInId, source)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(stockInId, source)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchHppByStockInId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchHppByStockInId'
type Repository_FetchHppByStockInId_Call struct {
	*mock.Call
}

// FetchHppByStockInId is a helper method to define mock.On call
//   - stockInId string
//   - source string
func (_e *Repository_Expecter) FetchHppByStockInId(stockInId interface{}, source interface{}) *Repository_FetchHppByStockInId_Call {
	return &Repository_FetchHppByStockInId_Call{Call: _e.mock.On("FetchHppByStockInId", stockInId, source)}
}

func (_c *Repository_FetchHppByStockInId_Call) Run(run func(stockInId string, source string)) *Repository_FetchHppByStockInId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *Repository_FetchHppByStockInId_Call) Return(_a0 []domain.Hpp, _a1 error) *Repository_FetchHppByStockInId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchHppByStockInId_Call) RunAndReturn(run func(string, string) ([]domain.Hpp, error)) *Repository_FetchHppByStockInId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchLastHppByProductId provides a mock function with given fields: productDetailId
func (_m *Repository) FetchLastHppByProductId(productDetailId int) (domain.Hpp, error) {
	ret := _m.Called(productDetailId)

	if len(ret) == 0 {
		panic("no return value specified for FetchLastHppByProductId")
	}

	var r0 domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (domain.Hpp, error)); ok {
		return rf(productDetailId)
	}
	if rf, ok := ret.Get(0).(func(int) domain.Hpp); ok {
		r0 = rf(productDetailId)
	} else {
		r0 = ret.Get(0).(domain.Hpp)
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productDetailId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchLastHppByProductId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchLastHppByProductId'
type Repository_FetchLastHppByProductId_Call struct {
	*mock.Call
}

// FetchLastHppByProductId is a helper method to define mock.On call
//   - productDetailId int
func (_e *Repository_Expecter) FetchLastHppByProductId(productDetailId interface{}) *Repository_FetchLastHppByProductId_Call {
	return &Repository_FetchLastHppByProductId_Call{Call: _e.mock.On("FetchLastHppByProductId", productDetailId)}
}

func (_c *Repository_FetchLastHppByProductId_Call) Run(run func(productDetailId int)) *Repository_FetchLastHppByProductId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchLastHppByProductId_Call) Return(_a0 domain.Hpp, _a1 error) *Repository_FetchLastHppByProductId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchLastHppByProductId_Call) RunAndReturn(run func(int) (domain.Hpp, error)) *Repository_FetchLastHppByProductId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchNearestStockPrice provides a mock function with given fields: productDetailId, createdAt, stockInSource
func (_m *Repository) FetchNearestStockPrice(productDetailId int, createdAt int64, stockInSource string) ([]domain.Hpp, error) {
	ret := _m.Called(productDetailId, createdAt, stockInSource)

	if len(ret) == 0 {
		panic("no return value specified for FetchNearestStockPrice")
	}

	var r0 []domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(int, int64, string) ([]domain.Hpp, error)); ok {
		return rf(productDetailId, createdAt, stockInSource)
	}
	if rf, ok := ret.Get(0).(func(int, int64, string) []domain.Hpp); ok {
		r0 = rf(productDetailId, createdAt, stockInSource)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(int, int64, string) error); ok {
		r1 = rf(productDetailId, createdAt, stockInSource)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchNearestStockPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchNearestStockPrice'
type Repository_FetchNearestStockPrice_Call struct {
	*mock.Call
}

// FetchNearestStockPrice is a helper method to define mock.On call
//   - productDetailId int
//   - createdAt int64
//   - stockInSource string
func (_e *Repository_Expecter) FetchNearestStockPrice(productDetailId interface{}, createdAt interface{}, stockInSource interface{}) *Repository_FetchNearestStockPrice_Call {
	return &Repository_FetchNearestStockPrice_Call{Call: _e.mock.On("FetchNearestStockPrice", productDetailId, createdAt, stockInSource)}
}

func (_c *Repository_FetchNearestStockPrice_Call) Run(run func(productDetailId int, createdAt int64, stockInSource string)) *Repository_FetchNearestStockPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(int64), args[2].(string))
	})
	return _c
}

func (_c *Repository_FetchNearestStockPrice_Call) Return(_a0 []domain.Hpp, _a1 error) *Repository_FetchNearestStockPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchNearestStockPrice_Call) RunAndReturn(run func(int, int64, string) ([]domain.Hpp, error)) *Repository_FetchNearestStockPrice_Call {
	_c.Call.Return(run)
	return _c
}

// FetchProductByProductDetailId provides a mock function with given fields: productDetailId
func (_m *Repository) FetchProductByProductDetailId(productDetailId int) (map[string]interface{}, error) {
	ret := _m.Called(productDetailId)

	if len(ret) == 0 {
		panic("no return value specified for FetchProductByProductDetailId")
	}

	var r0 map[string]interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (map[string]interface{}, error)); ok {
		return rf(productDetailId)
	}
	if rf, ok := ret.Get(0).(func(int) map[string]interface{}); ok {
		r0 = rf(productDetailId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(productDetailId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchProductByProductDetailId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchProductByProductDetailId'
type Repository_FetchProductByProductDetailId_Call struct {
	*mock.Call
}

// FetchProductByProductDetailId is a helper method to define mock.On call
//   - productDetailId int
func (_e *Repository_Expecter) FetchProductByProductDetailId(productDetailId interface{}) *Repository_FetchProductByProductDetailId_Call {
	return &Repository_FetchProductByProductDetailId_Call{Call: _e.mock.On("FetchProductByProductDetailId", productDetailId)}
}

func (_c *Repository_FetchProductByProductDetailId_Call) Run(run func(productDetailId int)) *Repository_FetchProductByProductDetailId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchProductByProductDetailId_Call) Return(_a0 map[string]interface{}, _a1 error) *Repository_FetchProductByProductDetailId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchProductByProductDetailId_Call) RunAndReturn(run func(int) (map[string]interface{}, error)) *Repository_FetchProductByProductDetailId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchPurchaseConfirmByPurchaseProduct provides a mock function with given fields: purchaseProductId
func (_m *Repository) FetchPurchaseConfirmByPurchaseProduct(purchaseProductId int) ([]map[string]interface{}, error) {
	ret := _m.Called(purchaseProductId)

	if len(ret) == 0 {
		panic("no return value specified for FetchPurchaseConfirmByPurchaseProduct")
	}

	var r0 []map[string]interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(int) ([]map[string]interface{}, error)); ok {
		return rf(purchaseProductId)
	}
	if rf, ok := ret.Get(0).(func(int) []map[string]interface{}); ok {
		r0 = rf(purchaseProductId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]map[string]interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(purchaseProductId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchPurchaseConfirmByPurchaseProduct_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchPurchaseConfirmByPurchaseProduct'
type Repository_FetchPurchaseConfirmByPurchaseProduct_Call struct {
	*mock.Call
}

// FetchPurchaseConfirmByPurchaseProduct is a helper method to define mock.On call
//   - purchaseProductId int
func (_e *Repository_Expecter) FetchPurchaseConfirmByPurchaseProduct(purchaseProductId interface{}) *Repository_FetchPurchaseConfirmByPurchaseProduct_Call {
	return &Repository_FetchPurchaseConfirmByPurchaseProduct_Call{Call: _e.mock.On("FetchPurchaseConfirmByPurchaseProduct", purchaseProductId)}
}

func (_c *Repository_FetchPurchaseConfirmByPurchaseProduct_Call) Run(run func(purchaseProductId int)) *Repository_FetchPurchaseConfirmByPurchaseProduct_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchPurchaseConfirmByPurchaseProduct_Call) Return(_a0 []map[string]interface{}, _a1 error) *Repository_FetchPurchaseConfirmByPurchaseProduct_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchPurchaseConfirmByPurchaseProduct_Call) RunAndReturn(run func(int) ([]map[string]interface{}, error)) *Repository_FetchPurchaseConfirmByPurchaseProduct_Call {
	_c.Call.Return(run)
	return _c
}

// FetchPurchaseProductByConfirmId provides a mock function with given fields: purchaseConfirmId
func (_m *Repository) FetchPurchaseProductByConfirmId(purchaseConfirmId ...int) (*[]models.PurchaseProductStockIn, error) {
	_va := make([]interface{}, len(purchaseConfirmId))
	for _i := range purchaseConfirmId {
		_va[_i] = purchaseConfirmId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchPurchaseProductByConfirmId")
	}

	var r0 *[]models.PurchaseProductStockIn
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) (*[]models.PurchaseProductStockIn, error)); ok {
		return rf(purchaseConfirmId...)
	}
	if rf, ok := ret.Get(0).(func(...int) *[]models.PurchaseProductStockIn); ok {
		r0 = rf(purchaseConfirmId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.PurchaseProductStockIn)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(purchaseConfirmId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchPurchaseProductByConfirmId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchPurchaseProductByConfirmId'
type Repository_FetchPurchaseProductByConfirmId_Call struct {
	*mock.Call
}

// FetchPurchaseProductByConfirmId is a helper method to define mock.On call
//   - purchaseConfirmId ...int
func (_e *Repository_Expecter) FetchPurchaseProductByConfirmId(purchaseConfirmId ...interface{}) *Repository_FetchPurchaseProductByConfirmId_Call {
	return &Repository_FetchPurchaseProductByConfirmId_Call{Call: _e.mock.On("FetchPurchaseProductByConfirmId",
		append([]interface{}{}, purchaseConfirmId...)...)}
}

func (_c *Repository_FetchPurchaseProductByConfirmId_Call) Run(run func(purchaseConfirmId ...int)) *Repository_FetchPurchaseProductByConfirmId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(int)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchPurchaseProductByConfirmId_Call) Return(_a0 *[]models.PurchaseProductStockIn, _a1 error) *Repository_FetchPurchaseProductByConfirmId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchPurchaseProductByConfirmId_Call) RunAndReturn(run func(...int) (*[]models.PurchaseProductStockIn, error)) *Repository_FetchPurchaseProductByConfirmId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchPurchaseProductById provides a mock function with given fields: purchaseProductId
func (_m *Repository) FetchPurchaseProductById(purchaseProductId int) (map[string]interface{}, error) {
	ret := _m.Called(purchaseProductId)

	if len(ret) == 0 {
		panic("no return value specified for FetchPurchaseProductById")
	}

	var r0 map[string]interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (map[string]interface{}, error)); ok {
		return rf(purchaseProductId)
	}
	if rf, ok := ret.Get(0).(func(int) map[string]interface{}); ok {
		r0 = rf(purchaseProductId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(purchaseProductId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchPurchaseProductById_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchPurchaseProductById'
type Repository_FetchPurchaseProductById_Call struct {
	*mock.Call
}

// FetchPurchaseProductById is a helper method to define mock.On call
//   - purchaseProductId int
func (_e *Repository_Expecter) FetchPurchaseProductById(purchaseProductId interface{}) *Repository_FetchPurchaseProductById_Call {
	return &Repository_FetchPurchaseProductById_Call{Call: _e.mock.On("FetchPurchaseProductById", purchaseProductId)}
}

func (_c *Repository_FetchPurchaseProductById_Call) Run(run func(purchaseProductId int)) *Repository_FetchPurchaseProductById_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchPurchaseProductById_Call) Return(_a0 map[string]interface{}, _a1 error) *Repository_FetchPurchaseProductById_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchPurchaseProductById_Call) RunAndReturn(run func(int) (map[string]interface{}, error)) *Repository_FetchPurchaseProductById_Call {
	_c.Call.Return(run)
	return _c
}

// FetchPurchaseRetur provides a mock function with given fields: returId
func (_m *Repository) FetchPurchaseRetur(returId int) (map[string]interface{}, error) {
	ret := _m.Called(returId)

	if len(ret) == 0 {
		panic("no return value specified for FetchPurchaseRetur")
	}

	var r0 map[string]interface{}
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (map[string]interface{}, error)); ok {
		return rf(returId)
	}
	if rf, ok := ret.Get(0).(func(int) map[string]interface{}); ok {
		r0 = rf(returId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]interface{})
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(returId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchPurchaseRetur_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchPurchaseRetur'
type Repository_FetchPurchaseRetur_Call struct {
	*mock.Call
}

// FetchPurchaseRetur is a helper method to define mock.On call
//   - returId int
func (_e *Repository_Expecter) FetchPurchaseRetur(returId interface{}) *Repository_FetchPurchaseRetur_Call {
	return &Repository_FetchPurchaseRetur_Call{Call: _e.mock.On("FetchPurchaseRetur", returId)}
}

func (_c *Repository_FetchPurchaseRetur_Call) Run(run func(returId int)) *Repository_FetchPurchaseRetur_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchPurchaseRetur_Call) Return(_a0 map[string]interface{}, _a1 error) *Repository_FetchPurchaseRetur_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchPurchaseRetur_Call) RunAndReturn(run func(int) (map[string]interface{}, error)) *Repository_FetchPurchaseRetur_Call {
	_c.Call.Return(run)
	return _c
}

// FetchSalesBreakdown provides a mock function with given fields: salesId
func (_m *Repository) FetchSalesBreakdown(salesId string) (*[]models.SalesBreakdown, error) {
	ret := _m.Called(salesId)

	if len(ret) == 0 {
		panic("no return value specified for FetchSalesBreakdown")
	}

	var r0 *[]models.SalesBreakdown
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*[]models.SalesBreakdown, error)); ok {
		return rf(salesId)
	}
	if rf, ok := ret.Get(0).(func(string) *[]models.SalesBreakdown); ok {
		r0 = rf(salesId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.SalesBreakdown)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(salesId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchSalesBreakdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchSalesBreakdown'
type Repository_FetchSalesBreakdown_Call struct {
	*mock.Call
}

// FetchSalesBreakdown is a helper method to define mock.On call
//   - salesId string
func (_e *Repository_Expecter) FetchSalesBreakdown(salesId interface{}) *Repository_FetchSalesBreakdown_Call {
	return &Repository_FetchSalesBreakdown_Call{Call: _e.mock.On("FetchSalesBreakdown", salesId)}
}

func (_c *Repository_FetchSalesBreakdown_Call) Run(run func(salesId string)) *Repository_FetchSalesBreakdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *Repository_FetchSalesBreakdown_Call) Return(_a0 *[]models.SalesBreakdown, _a1 error) *Repository_FetchSalesBreakdown_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchSalesBreakdown_Call) RunAndReturn(run func(string) (*[]models.SalesBreakdown, error)) *Repository_FetchSalesBreakdown_Call {
	_c.Call.Return(run)
	return _c
}

// FetchSalesDetailBySalesId provides a mock function with given fields: salesId
func (_m *Repository) FetchSalesDetailBySalesId(salesId string) (*[]models.SalesDetail, error) {
	ret := _m.Called(salesId)

	if len(ret) == 0 {
		panic("no return value specified for FetchSalesDetailBySalesId")
	}

	var r0 *[]models.SalesDetail
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (*[]models.SalesDetail, error)); ok {
		return rf(salesId)
	}
	if rf, ok := ret.Get(0).(func(string) *[]models.SalesDetail); ok {
		r0 = rf(salesId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.SalesDetail)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(salesId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchSalesDetailBySalesId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchSalesDetailBySalesId'
type Repository_FetchSalesDetailBySalesId_Call struct {
	*mock.Call
}

// FetchSalesDetailBySalesId is a helper method to define mock.On call
//   - salesId string
func (_e *Repository_Expecter) FetchSalesDetailBySalesId(salesId interface{}) *Repository_FetchSalesDetailBySalesId_Call {
	return &Repository_FetchSalesDetailBySalesId_Call{Call: _e.mock.On("FetchSalesDetailBySalesId", salesId)}
}

func (_c *Repository_FetchSalesDetailBySalesId_Call) Run(run func(salesId string)) *Repository_FetchSalesDetailBySalesId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string))
	})
	return _c
}

func (_c *Repository_FetchSalesDetailBySalesId_Call) Return(_a0 *[]models.SalesDetail, _a1 error) *Repository_FetchSalesDetailBySalesId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchSalesDetailBySalesId_Call) RunAndReturn(run func(string) (*[]models.SalesDetail, error)) *Repository_FetchSalesDetailBySalesId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchSpoil provides a mock function with given fields: spoilId
func (_m *Repository) FetchSpoil(spoilId ...int) ([]models.SpoilEntity, error) {
	_va := make([]interface{}, len(spoilId))
	for _i := range spoilId {
		_va[_i] = spoilId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchSpoil")
	}

	var r0 []models.SpoilEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) ([]models.SpoilEntity, error)); ok {
		return rf(spoilId...)
	}
	if rf, ok := ret.Get(0).(func(...int) []models.SpoilEntity); ok {
		r0 = rf(spoilId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.SpoilEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(spoilId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchSpoil_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchSpoil'
type Repository_FetchSpoil_Call struct {
	*mock.Call
}

// FetchSpoil is a helper method to define mock.On call
//   - spoilId ...int
func (_e *Repository_Expecter) FetchSpoil(spoilId ...interface{}) *Repository_FetchSpoil_Call {
	return &Repository_FetchSpoil_Call{Call: _e.mock.On("FetchSpoil",
		append([]interface{}{}, spoilId...)...)}
}

func (_c *Repository_FetchSpoil_Call) Run(run func(spoilId ...int)) *Repository_FetchSpoil_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(int)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchSpoil_Call) Return(_a0 []models.SpoilEntity, _a1 error) *Repository_FetchSpoil_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchSpoil_Call) RunAndReturn(run func(...int) ([]models.SpoilEntity, error)) *Repository_FetchSpoil_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockOpname provides a mock function with given fields: stockOpnameIds
func (_m *Repository) FetchStockOpname(stockOpnameIds ...interface{}) ([]models.StockOpnameEntity, error) {
	var _ca []interface{}
	_ca = append(_ca, stockOpnameIds...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockOpname")
	}

	var r0 []models.StockOpnameEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(...interface{}) ([]models.StockOpnameEntity, error)); ok {
		return rf(stockOpnameIds...)
	}
	if rf, ok := ret.Get(0).(func(...interface{}) []models.StockOpnameEntity); ok {
		r0 = rf(stockOpnameIds...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.StockOpnameEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(stockOpnameIds...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockOpname_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockOpname'
type Repository_FetchStockOpname_Call struct {
	*mock.Call
}

// FetchStockOpname is a helper method to define mock.On call
//   - stockOpnameIds ...interface{}
func (_e *Repository_Expecter) FetchStockOpname(stockOpnameIds ...interface{}) *Repository_FetchStockOpname_Call {
	return &Repository_FetchStockOpname_Call{Call: _e.mock.On("FetchStockOpname",
		append([]interface{}{}, stockOpnameIds...)...)}
}

func (_c *Repository_FetchStockOpname_Call) Run(run func(stockOpnameIds ...interface{})) *Repository_FetchStockOpname_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]interface{}, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(interface{})
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchStockOpname_Call) Return(_a0 []models.StockOpnameEntity, _a1 error) *Repository_FetchStockOpname_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockOpname_Call) RunAndReturn(run func(...interface{}) ([]models.StockOpnameEntity, error)) *Repository_FetchStockOpname_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockOutHpp provides a mock function with given fields: productDetailId
func (_m *Repository) FetchStockOutHpp(productDetailId ...int) ([]domain.Hpp, error) {
	_va := make([]interface{}, len(productDetailId))
	for _i := range productDetailId {
		_va[_i] = productDetailId[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockOutHpp")
	}

	var r0 []domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(...int) ([]domain.Hpp, error)); ok {
		return rf(productDetailId...)
	}
	if rf, ok := ret.Get(0).(func(...int) []domain.Hpp); ok {
		r0 = rf(productDetailId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(...int) error); ok {
		r1 = rf(productDetailId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockOutHpp_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockOutHpp'
type Repository_FetchStockOutHpp_Call struct {
	*mock.Call
}

// FetchStockOutHpp is a helper method to define mock.On call
//   - productDetailId ...int
func (_e *Repository_Expecter) FetchStockOutHpp(productDetailId ...interface{}) *Repository_FetchStockOutHpp_Call {
	return &Repository_FetchStockOutHpp_Call{Call: _e.mock.On("FetchStockOutHpp",
		append([]interface{}{}, productDetailId...)...)}
}

func (_c *Repository_FetchStockOutHpp_Call) Run(run func(productDetailId ...int)) *Repository_FetchStockOutHpp_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]int, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(int)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchStockOutHpp_Call) Return(_a0 []domain.Hpp, _a1 error) *Repository_FetchStockOutHpp_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockOutHpp_Call) RunAndReturn(run func(...int) ([]domain.Hpp, error)) *Repository_FetchStockOutHpp_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPrice provides a mock function with given fields:
func (_m *Repository) FetchStockPrice() (*[]domain.Hpp, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPrice")
	}

	var r0 *[]domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func() (*[]domain.Hpp, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() *[]domain.Hpp); ok {
		r0 = rf()
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPrice'
type Repository_FetchStockPrice_Call struct {
	*mock.Call
}

// FetchStockPrice is a helper method to define mock.On call
func (_e *Repository_Expecter) FetchStockPrice() *Repository_FetchStockPrice_Call {
	return &Repository_FetchStockPrice_Call{Call: _e.mock.On("FetchStockPrice")}
}

func (_c *Repository_FetchStockPrice_Call) Run(run func()) *Repository_FetchStockPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run()
	})
	return _c
}

func (_c *Repository_FetchStockPrice_Call) Return(_a0 *[]domain.Hpp, _a1 error) *Repository_FetchStockPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPrice_Call) RunAndReturn(run func() (*[]domain.Hpp, error)) *Repository_FetchStockPrice_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceByIdAndSource provides a mock function with given fields: id, source
func (_m *Repository) FetchStockPriceByIdAndSource(id string, source string) (*[]domain.Hpp, error) {
	ret := _m.Called(id, source)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceByIdAndSource")
	}

	var r0 *[]domain.Hpp
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (*[]domain.Hpp, error)); ok {
		return rf(id, source)
	}
	if rf, ok := ret.Get(0).(func(string, string) *[]domain.Hpp); ok {
		r0 = rf(id, source)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]domain.Hpp)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(id, source)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceByIdAndSource_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceByIdAndSource'
type Repository_FetchStockPriceByIdAndSource_Call struct {
	*mock.Call
}

// FetchStockPriceByIdAndSource is a helper method to define mock.On call
//   - id string
//   - source string
func (_e *Repository_Expecter) FetchStockPriceByIdAndSource(id interface{}, source interface{}) *Repository_FetchStockPriceByIdAndSource_Call {
	return &Repository_FetchStockPriceByIdAndSource_Call{Call: _e.mock.On("FetchStockPriceByIdAndSource", id, source)}
}

func (_c *Repository_FetchStockPriceByIdAndSource_Call) Run(run func(id string, source string)) *Repository_FetchStockPriceByIdAndSource_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *Repository_FetchStockPriceByIdAndSource_Call) Return(_a0 *[]domain.Hpp, _a1 error) *Repository_FetchStockPriceByIdAndSource_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceByIdAndSource_Call) RunAndReturn(run func(string, string) (*[]domain.Hpp, error)) *Repository_FetchStockPriceByIdAndSource_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceByOutSourceId provides a mock function with given fields: id, idSource
func (_m *Repository) FetchStockPriceByOutSourceId(id string, idSource string) ([]models.StockPriceWithDetailQty, error) {
	ret := _m.Called(id, idSource)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceByOutSourceId")
	}

	var r0 []models.StockPriceWithDetailQty
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]models.StockPriceWithDetailQty, error)); ok {
		return rf(id, idSource)
	}
	if rf, ok := ret.Get(0).(func(string, string) []models.StockPriceWithDetailQty); ok {
		r0 = rf(id, idSource)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.StockPriceWithDetailQty)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(id, idSource)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceByOutSourceId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceByOutSourceId'
type Repository_FetchStockPriceByOutSourceId_Call struct {
	*mock.Call
}

// FetchStockPriceByOutSourceId is a helper method to define mock.On call
//   - id string
//   - idSource string
func (_e *Repository_Expecter) FetchStockPriceByOutSourceId(id interface{}, idSource interface{}) *Repository_FetchStockPriceByOutSourceId_Call {
	return &Repository_FetchStockPriceByOutSourceId_Call{Call: _e.mock.On("FetchStockPriceByOutSourceId", id, idSource)}
}

func (_c *Repository_FetchStockPriceByOutSourceId_Call) Run(run func(id string, idSource string)) *Repository_FetchStockPriceByOutSourceId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *Repository_FetchStockPriceByOutSourceId_Call) Return(_a0 []models.StockPriceWithDetailQty, _a1 error) *Repository_FetchStockPriceByOutSourceId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceByOutSourceId_Call) RunAndReturn(run func(string, string) ([]models.StockPriceWithDetailQty, error)) *Repository_FetchStockPriceByOutSourceId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceDetail provides a mock function with given fields: stockPriceId
func (_m *Repository) FetchStockPriceDetail(stockPriceId ...interface{}) (*[]models.StockPriceDetailEntity, error) {
	var _ca []interface{}
	_ca = append(_ca, stockPriceId...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceDetail")
	}

	var r0 *[]models.StockPriceDetailEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(...interface{}) (*[]models.StockPriceDetailEntity, error)); ok {
		return rf(stockPriceId...)
	}
	if rf, ok := ret.Get(0).(func(...interface{}) *[]models.StockPriceDetailEntity); ok {
		r0 = rf(stockPriceId...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.StockPriceDetailEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(stockPriceId...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceDetail'
type Repository_FetchStockPriceDetail_Call struct {
	*mock.Call
}

// FetchStockPriceDetail is a helper method to define mock.On call
//   - stockPriceId ...interface{}
func (_e *Repository_Expecter) FetchStockPriceDetail(stockPriceId ...interface{}) *Repository_FetchStockPriceDetail_Call {
	return &Repository_FetchStockPriceDetail_Call{Call: _e.mock.On("FetchStockPriceDetail",
		append([]interface{}{}, stockPriceId...)...)}
}

func (_c *Repository_FetchStockPriceDetail_Call) Run(run func(stockPriceId ...interface{})) *Repository_FetchStockPriceDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]interface{}, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(interface{})
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchStockPriceDetail_Call) Return(_a0 *[]models.StockPriceDetailEntity, _a1 error) *Repository_FetchStockPriceDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceDetail_Call) RunAndReturn(run func(...interface{}) (*[]models.StockPriceDetailEntity, error)) *Repository_FetchStockPriceDetail_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceDetailByOutSourceId provides a mock function with given fields: id, idSource
func (_m *Repository) FetchStockPriceDetailByOutSourceId(id string, idSource string) ([]models.StockPriceDetailEntity, error) {
	ret := _m.Called(id, idSource)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceDetailByOutSourceId")
	}

	var r0 []models.StockPriceDetailEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) ([]models.StockPriceDetailEntity, error)); ok {
		return rf(id, idSource)
	}
	if rf, ok := ret.Get(0).(func(string, string) []models.StockPriceDetailEntity); ok {
		r0 = rf(id, idSource)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.StockPriceDetailEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(id, idSource)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceDetailByOutSourceId_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceDetailByOutSourceId'
type Repository_FetchStockPriceDetailByOutSourceId_Call struct {
	*mock.Call
}

// FetchStockPriceDetailByOutSourceId is a helper method to define mock.On call
//   - id string
//   - idSource string
func (_e *Repository_Expecter) FetchStockPriceDetailByOutSourceId(id interface{}, idSource interface{}) *Repository_FetchStockPriceDetailByOutSourceId_Call {
	return &Repository_FetchStockPriceDetailByOutSourceId_Call{Call: _e.mock.On("FetchStockPriceDetailByOutSourceId", id, idSource)}
}

func (_c *Repository_FetchStockPriceDetailByOutSourceId_Call) Run(run func(id string, idSource string)) *Repository_FetchStockPriceDetailByOutSourceId_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(string), args[1].(string))
	})
	return _c
}

func (_c *Repository_FetchStockPriceDetailByOutSourceId_Call) Return(_a0 []models.StockPriceDetailEntity, _a1 error) *Repository_FetchStockPriceDetailByOutSourceId_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceDetailByOutSourceId_Call) RunAndReturn(run func(string, string) ([]models.StockPriceDetailEntity, error)) *Repository_FetchStockPriceDetailByOutSourceId_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceDetailByStockIn provides a mock function with given fields: inId, inSource, stockOutSources
func (_m *Repository) FetchStockPriceDetailByStockIn(inId string, inSource string, stockOutSources ...string) ([]models.StockPriceDetailEntity, error) {
	_va := make([]interface{}, len(stockOutSources))
	for _i := range stockOutSources {
		_va[_i] = stockOutSources[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, inId, inSource)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceDetailByStockIn")
	}

	var r0 []models.StockPriceDetailEntity
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, ...string) ([]models.StockPriceDetailEntity, error)); ok {
		return rf(inId, inSource, stockOutSources...)
	}
	if rf, ok := ret.Get(0).(func(string, string, ...string) []models.StockPriceDetailEntity); ok {
		r0 = rf(inId, inSource, stockOutSources...)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]models.StockPriceDetailEntity)
		}
	}

	if rf, ok := ret.Get(1).(func(string, string, ...string) error); ok {
		r1 = rf(inId, inSource, stockOutSources...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceDetailByStockIn_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceDetailByStockIn'
type Repository_FetchStockPriceDetailByStockIn_Call struct {
	*mock.Call
}

// FetchStockPriceDetailByStockIn is a helper method to define mock.On call
//   - inId string
//   - inSource string
//   - stockOutSources ...string
func (_e *Repository_Expecter) FetchStockPriceDetailByStockIn(inId interface{}, inSource interface{}, stockOutSources ...interface{}) *Repository_FetchStockPriceDetailByStockIn_Call {
	return &Repository_FetchStockPriceDetailByStockIn_Call{Call: _e.mock.On("FetchStockPriceDetailByStockIn",
		append([]interface{}{inId, inSource}, stockOutSources...)...)}
}

func (_c *Repository_FetchStockPriceDetailByStockIn_Call) Run(run func(inId string, inSource string, stockOutSources ...string)) *Repository_FetchStockPriceDetailByStockIn_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]string, len(args)-2)
		for i, a := range args[2:] {
			if a != nil {
				variadicArgs[i] = a.(string)
			}
		}
		run(args[0].(string), args[1].(string), variadicArgs...)
	})
	return _c
}

func (_c *Repository_FetchStockPriceDetailByStockIn_Call) Return(_a0 []models.StockPriceDetailEntity, _a1 error) *Repository_FetchStockPriceDetailByStockIn_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceDetailByStockIn_Call) RunAndReturn(run func(string, string, ...string) ([]models.StockPriceDetailEntity, error)) *Repository_FetchStockPriceDetailByStockIn_Call {
	_c.Call.Return(run)
	return _c
}

// FetchStockPriceWithDetail provides a mock function with given fields: param
func (_m *Repository) FetchStockPriceWithDetail(param *domain.StockPriceRequest) ([]domain.StockPriceResponse, error) {
	ret := _m.Called(param)

	if len(ret) == 0 {
		panic("no return value specified for FetchStockPriceWithDetail")
	}

	var r0 []domain.StockPriceResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(*domain.StockPriceRequest) ([]domain.StockPriceResponse, error)); ok {
		return rf(param)
	}
	if rf, ok := ret.Get(0).(func(*domain.StockPriceRequest) []domain.StockPriceResponse); ok {
		r0 = rf(param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]domain.StockPriceResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(*domain.StockPriceRequest) error); ok {
		r1 = rf(param)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchStockPriceWithDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchStockPriceWithDetail'
type Repository_FetchStockPriceWithDetail_Call struct {
	*mock.Call
}

// FetchStockPriceWithDetail is a helper method to define mock.On call
//   - param *domain.StockPriceRequest
func (_e *Repository_Expecter) FetchStockPriceWithDetail(param interface{}) *Repository_FetchStockPriceWithDetail_Call {
	return &Repository_FetchStockPriceWithDetail_Call{Call: _e.mock.On("FetchStockPriceWithDetail", param)}
}

func (_c *Repository_FetchStockPriceWithDetail_Call) Run(run func(param *domain.StockPriceRequest)) *Repository_FetchStockPriceWithDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(*domain.StockPriceRequest))
	})
	return _c
}

func (_c *Repository_FetchStockPriceWithDetail_Call) Return(_a0 []domain.StockPriceResponse, _a1 error) *Repository_FetchStockPriceWithDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchStockPriceWithDetail_Call) RunAndReturn(run func(*domain.StockPriceRequest) ([]domain.StockPriceResponse, error)) *Repository_FetchStockPriceWithDetail_Call {
	_c.Call.Return(run)
	return _c
}

// FetchTransferConfirm provides a mock function with given fields: transferId
func (_m *Repository) FetchTransferConfirm(transferId int) (*[]models.TransferConfirm, error) {
	ret := _m.Called(transferId)

	if len(ret) == 0 {
		panic("no return value specified for FetchTransferConfirm")
	}

	var r0 *[]models.TransferConfirm
	var r1 error
	if rf, ok := ret.Get(0).(func(int) (*[]models.TransferConfirm, error)); ok {
		return rf(transferId)
	}
	if rf, ok := ret.Get(0).(func(int) *[]models.TransferConfirm); ok {
		r0 = rf(transferId)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*[]models.TransferConfirm)
		}
	}

	if rf, ok := ret.Get(1).(func(int) error); ok {
		r1 = rf(transferId)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_FetchTransferConfirm_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'FetchTransferConfirm'
type Repository_FetchTransferConfirm_Call struct {
	*mock.Call
}

// FetchTransferConfirm is a helper method to define mock.On call
//   - transferId int
func (_e *Repository_Expecter) FetchTransferConfirm(transferId interface{}) *Repository_FetchTransferConfirm_Call {
	return &Repository_FetchTransferConfirm_Call{Call: _e.mock.On("FetchTransferConfirm", transferId)}
}

func (_c *Repository_FetchTransferConfirm_Call) Run(run func(transferId int)) *Repository_FetchTransferConfirm_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int))
	})
	return _c
}

func (_c *Repository_FetchTransferConfirm_Call) Return(_a0 *[]models.TransferConfirm, _a1 error) *Repository_FetchTransferConfirm_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_FetchTransferConfirm_Call) RunAndReturn(run func(int) (*[]models.TransferConfirm, error)) *Repository_FetchTransferConfirm_Call {
	_c.Call.Return(run)
	return _c
}

// Filter provides a mock function with given fields: filter
func (_m *Repository) Filter(filter domain.StockPriceFilter) hpp.Repository {
	ret := _m.Called(filter)

	if len(ret) == 0 {
		panic("no return value specified for Filter")
	}

	var r0 hpp.Repository
	if rf, ok := ret.Get(0).(func(domain.StockPriceFilter) hpp.Repository); ok {
		r0 = rf(filter)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(hpp.Repository)
		}
	}

	return r0
}

// Repository_Filter_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'Filter'
type Repository_Filter_Call struct {
	*mock.Call
}

// Filter is a helper method to define mock.On call
//   - filter domain.StockPriceFilter
func (_e *Repository_Expecter) Filter(filter interface{}) *Repository_Filter_Call {
	return &Repository_Filter_Call{Call: _e.mock.On("Filter", filter)}
}

func (_c *Repository_Filter_Call) Run(run func(filter domain.StockPriceFilter)) *Repository_Filter_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.StockPriceFilter))
	})
	return _c
}

func (_c *Repository_Filter_Call) Return(_a0 hpp.Repository) *Repository_Filter_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_Filter_Call) RunAndReturn(run func(domain.StockPriceFilter) hpp.Repository) *Repository_Filter_Call {
	_c.Call.Return(run)
	return _c
}

// InsertStockOutDetail provides a mock function with given fields: stockDetail
func (_m *Repository) InsertStockOutDetail(stockDetail domain.StockPriceDetail) error {
	ret := _m.Called(stockDetail)

	if len(ret) == 0 {
		panic("no return value specified for InsertStockOutDetail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(domain.StockPriceDetail) error); ok {
		r0 = rf(stockDetail)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_InsertStockOutDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertStockOutDetail'
type Repository_InsertStockOutDetail_Call struct {
	*mock.Call
}

// InsertStockOutDetail is a helper method to define mock.On call
//   - stockDetail domain.StockPriceDetail
func (_e *Repository_Expecter) InsertStockOutDetail(stockDetail interface{}) *Repository_InsertStockOutDetail_Call {
	return &Repository_InsertStockOutDetail_Call{Call: _e.mock.On("InsertStockOutDetail", stockDetail)}
}

func (_c *Repository_InsertStockOutDetail_Call) Run(run func(stockDetail domain.StockPriceDetail)) *Repository_InsertStockOutDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.StockPriceDetail))
	})
	return _c
}

func (_c *Repository_InsertStockOutDetail_Call) Return(_a0 error) *Repository_InsertStockOutDetail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_InsertStockOutDetail_Call) RunAndReturn(run func(domain.StockPriceDetail) error) *Repository_InsertStockOutDetail_Call {
	_c.Call.Return(run)
	return _c
}

// InsertStockPrice provides a mock function with given fields: _a0
func (_m *Repository) InsertStockPrice(_a0 domain.Hpp) (int64, error) {
	ret := _m.Called(_a0)

	if len(ret) == 0 {
		panic("no return value specified for InsertStockPrice")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(domain.Hpp) (int64, error)); ok {
		return rf(_a0)
	}
	if rf, ok := ret.Get(0).(func(domain.Hpp) int64); ok {
		r0 = rf(_a0)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(domain.Hpp) error); ok {
		r1 = rf(_a0)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_InsertStockPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'InsertStockPrice'
type Repository_InsertStockPrice_Call struct {
	*mock.Call
}

// InsertStockPrice is a helper method to define mock.On call
//   - _a0 domain.Hpp
func (_e *Repository_Expecter) InsertStockPrice(_a0 interface{}) *Repository_InsertStockPrice_Call {
	return &Repository_InsertStockPrice_Call{Call: _e.mock.On("InsertStockPrice", _a0)}
}

func (_c *Repository_InsertStockPrice_Call) Run(run func(_a0 domain.Hpp)) *Repository_InsertStockPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(domain.Hpp))
	})
	return _c
}

func (_c *Repository_InsertStockPrice_Call) Return(_a0 int64, _a1 error) *Repository_InsertStockPrice_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_InsertStockPrice_Call) RunAndReturn(run func(domain.Hpp) (int64, error)) *Repository_InsertStockPrice_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePriceBuyProduct provides a mock function with given fields: productDetailId, priceBuy
func (_m *Repository) UpdatePriceBuyProduct(productDetailId int, priceBuy int) error {
	ret := _m.Called(productDetailId, priceBuy)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePriceBuyProduct")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, int) error); ok {
		r0 = rf(productDetailId, priceBuy)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdatePriceBuyProduct_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePriceBuyProduct'
type Repository_UpdatePriceBuyProduct_Call struct {
	*mock.Call
}

// UpdatePriceBuyProduct is a helper method to define mock.On call
//   - productDetailId int
//   - priceBuy int
func (_e *Repository_Expecter) UpdatePriceBuyProduct(productDetailId interface{}, priceBuy interface{}) *Repository_UpdatePriceBuyProduct_Call {
	return &Repository_UpdatePriceBuyProduct_Call{Call: _e.mock.On("UpdatePriceBuyProduct", productDetailId, priceBuy)}
}

func (_c *Repository_UpdatePriceBuyProduct_Call) Run(run func(productDetailId int, priceBuy int)) *Repository_UpdatePriceBuyProduct_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(int))
	})
	return _c
}

func (_c *Repository_UpdatePriceBuyProduct_Call) Return(_a0 error) *Repository_UpdatePriceBuyProduct_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdatePriceBuyProduct_Call) RunAndReturn(run func(int, int) error) *Repository_UpdatePriceBuyProduct_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePriceBuyProducts provides a mock function with given fields: productPrice
func (_m *Repository) UpdatePriceBuyProducts(productPrice ...models.CurrentProductPrice) error {
	_va := make([]interface{}, len(productPrice))
	for _i := range productPrice {
		_va[_i] = productPrice[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePriceBuyProducts")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(...models.CurrentProductPrice) error); ok {
		r0 = rf(productPrice...)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdatePriceBuyProducts_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePriceBuyProducts'
type Repository_UpdatePriceBuyProducts_Call struct {
	*mock.Call
}

// UpdatePriceBuyProducts is a helper method to define mock.On call
//   - productPrice ...models.CurrentProductPrice
func (_e *Repository_Expecter) UpdatePriceBuyProducts(productPrice ...interface{}) *Repository_UpdatePriceBuyProducts_Call {
	return &Repository_UpdatePriceBuyProducts_Call{Call: _e.mock.On("UpdatePriceBuyProducts",
		append([]interface{}{}, productPrice...)...)}
}

func (_c *Repository_UpdatePriceBuyProducts_Call) Run(run func(productPrice ...models.CurrentProductPrice)) *Repository_UpdatePriceBuyProducts_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]models.CurrentProductPrice, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(models.CurrentProductPrice)
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_UpdatePriceBuyProducts_Call) Return(_a0 error) *Repository_UpdatePriceBuyProducts_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdatePriceBuyProducts_Call) RunAndReturn(run func(...models.CurrentProductPrice) error) *Repository_UpdatePriceBuyProducts_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePriceBuySalesBreakdown provides a mock function with given fields: updates
func (_m *Repository) UpdatePriceBuySalesBreakdown(updates []models.PriceBuyUpdate) error {
	ret := _m.Called(updates)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePriceBuySalesBreakdown")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func([]models.PriceBuyUpdate) error); ok {
		r0 = rf(updates)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdatePriceBuySalesBreakdown_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePriceBuySalesBreakdown'
type Repository_UpdatePriceBuySalesBreakdown_Call struct {
	*mock.Call
}

// UpdatePriceBuySalesBreakdown is a helper method to define mock.On call
//   - updates []models.PriceBuyUpdate
func (_e *Repository_Expecter) UpdatePriceBuySalesBreakdown(updates interface{}) *Repository_UpdatePriceBuySalesBreakdown_Call {
	return &Repository_UpdatePriceBuySalesBreakdown_Call{Call: _e.mock.On("UpdatePriceBuySalesBreakdown", updates)}
}

func (_c *Repository_UpdatePriceBuySalesBreakdown_Call) Run(run func(updates []models.PriceBuyUpdate)) *Repository_UpdatePriceBuySalesBreakdown_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]models.PriceBuyUpdate))
	})
	return _c
}

func (_c *Repository_UpdatePriceBuySalesBreakdown_Call) Return(_a0 error) *Repository_UpdatePriceBuySalesBreakdown_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdatePriceBuySalesBreakdown_Call) RunAndReturn(run func([]models.PriceBuyUpdate) error) *Repository_UpdatePriceBuySalesBreakdown_Call {
	_c.Call.Return(run)
	return _c
}

// UpdatePriceBuySalesDetail provides a mock function with given fields: updates
func (_m *Repository) UpdatePriceBuySalesDetail(updates []models.PriceBuyUpdate) error {
	ret := _m.Called(updates)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePriceBuySalesDetail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func([]models.PriceBuyUpdate) error); ok {
		r0 = rf(updates)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdatePriceBuySalesDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdatePriceBuySalesDetail'
type Repository_UpdatePriceBuySalesDetail_Call struct {
	*mock.Call
}

// UpdatePriceBuySalesDetail is a helper method to define mock.On call
//   - updates []models.PriceBuyUpdate
func (_e *Repository_Expecter) UpdatePriceBuySalesDetail(updates interface{}) *Repository_UpdatePriceBuySalesDetail_Call {
	return &Repository_UpdatePriceBuySalesDetail_Call{Call: _e.mock.On("UpdatePriceBuySalesDetail", updates)}
}

func (_c *Repository_UpdatePriceBuySalesDetail_Call) Run(run func(updates []models.PriceBuyUpdate)) *Repository_UpdatePriceBuySalesDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].([]models.PriceBuyUpdate))
	})
	return _c
}

func (_c *Repository_UpdatePriceBuySalesDetail_Call) Return(_a0 error) *Repository_UpdatePriceBuySalesDetail_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdatePriceBuySalesDetail_Call) RunAndReturn(run func([]models.PriceBuyUpdate) error) *Repository_UpdatePriceBuySalesDetail_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStockPrice provides a mock function with given fields: stockPriceId, data
func (_m *Repository) UpdateStockPrice(stockPriceId int, data map[string]interface{}) error {
	ret := _m.Called(stockPriceId, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStockPrice")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, map[string]interface{}) error); ok {
		r0 = rf(stockPriceId, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdateStockPrice_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStockPrice'
type Repository_UpdateStockPrice_Call struct {
	*mock.Call
}

// UpdateStockPrice is a helper method to define mock.On call
//   - stockPriceId int
//   - data map[string]interface{}
func (_e *Repository_Expecter) UpdateStockPrice(stockPriceId interface{}, data interface{}) *Repository_UpdateStockPrice_Call {
	return &Repository_UpdateStockPrice_Call{Call: _e.mock.On("UpdateStockPrice", stockPriceId, data)}
}

func (_c *Repository_UpdateStockPrice_Call) Run(run func(stockPriceId int, data map[string]interface{})) *Repository_UpdateStockPrice_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(map[string]interface{}))
	})
	return _c
}

func (_c *Repository_UpdateStockPrice_Call) Return(_a0 error) *Repository_UpdateStockPrice_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdateStockPrice_Call) RunAndReturn(run func(int, map[string]interface{}) error) *Repository_UpdateStockPrice_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStockPriceDetailQty provides a mock function with given fields: stockPriceDetailId, qtyOut
func (_m *Repository) UpdateStockPriceDetailQty(stockPriceDetailId int64, qtyOut float32) error {
	ret := _m.Called(stockPriceDetailId, qtyOut)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStockPriceDetailQty")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int64, float32) error); ok {
		r0 = rf(stockPriceDetailId, qtyOut)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdateStockPriceDetailQty_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStockPriceDetailQty'
type Repository_UpdateStockPriceDetailQty_Call struct {
	*mock.Call
}

// UpdateStockPriceDetailQty is a helper method to define mock.On call
//   - stockPriceDetailId int64
//   - qtyOut float32
func (_e *Repository_Expecter) UpdateStockPriceDetailQty(stockPriceDetailId interface{}, qtyOut interface{}) *Repository_UpdateStockPriceDetailQty_Call {
	return &Repository_UpdateStockPriceDetailQty_Call{Call: _e.mock.On("UpdateStockPriceDetailQty", stockPriceDetailId, qtyOut)}
}

func (_c *Repository_UpdateStockPriceDetailQty_Call) Run(run func(stockPriceDetailId int64, qtyOut float32)) *Repository_UpdateStockPriceDetailQty_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int64), args[1].(float32))
	})
	return _c
}

func (_c *Repository_UpdateStockPriceDetailQty_Call) Return(_a0 error) *Repository_UpdateStockPriceDetailQty_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdateStockPriceDetailQty_Call) RunAndReturn(run func(int64, float32) error) *Repository_UpdateStockPriceDetailQty_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStockPriceOutBasedDetail provides a mock function with given fields: stockPriceIds
func (_m *Repository) UpdateStockPriceOutBasedDetail(stockPriceIds ...interface{}) (int64, error) {
	var _ca []interface{}
	_ca = append(_ca, stockPriceIds...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStockPriceOutBasedDetail")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(...interface{}) (int64, error)); ok {
		return rf(stockPriceIds...)
	}
	if rf, ok := ret.Get(0).(func(...interface{}) int64); ok {
		r0 = rf(stockPriceIds...)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(...interface{}) error); ok {
		r1 = rf(stockPriceIds...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Repository_UpdateStockPriceOutBasedDetail_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStockPriceOutBasedDetail'
type Repository_UpdateStockPriceOutBasedDetail_Call struct {
	*mock.Call
}

// UpdateStockPriceOutBasedDetail is a helper method to define mock.On call
//   - stockPriceIds ...interface{}
func (_e *Repository_Expecter) UpdateStockPriceOutBasedDetail(stockPriceIds ...interface{}) *Repository_UpdateStockPriceOutBasedDetail_Call {
	return &Repository_UpdateStockPriceOutBasedDetail_Call{Call: _e.mock.On("UpdateStockPriceOutBasedDetail",
		append([]interface{}{}, stockPriceIds...)...)}
}

func (_c *Repository_UpdateStockPriceOutBasedDetail_Call) Run(run func(stockPriceIds ...interface{})) *Repository_UpdateStockPriceOutBasedDetail_Call {
	_c.Call.Run(func(args mock.Arguments) {
		variadicArgs := make([]interface{}, len(args)-0)
		for i, a := range args[0:] {
			if a != nil {
				variadicArgs[i] = a.(interface{})
			}
		}
		run(variadicArgs...)
	})
	return _c
}

func (_c *Repository_UpdateStockPriceOutBasedDetail_Call) Return(_a0 int64, _a1 error) *Repository_UpdateStockPriceOutBasedDetail_Call {
	_c.Call.Return(_a0, _a1)
	return _c
}

func (_c *Repository_UpdateStockPriceOutBasedDetail_Call) RunAndReturn(run func(...interface{}) (int64, error)) *Repository_UpdateStockPriceOutBasedDetail_Call {
	_c.Call.Return(run)
	return _c
}

// UpdateStockPriceQtyIn provides a mock function with given fields: stockPriceId, qtyIn
func (_m *Repository) UpdateStockPriceQtyIn(stockPriceId int, qtyIn float32) error {
	ret := _m.Called(stockPriceId, qtyIn)

	if len(ret) == 0 {
		panic("no return value specified for UpdateStockPriceQtyIn")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(int, float32) error); ok {
		r0 = rf(stockPriceId, qtyIn)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Repository_UpdateStockPriceQtyIn_Call is a *mock.Call that shadows Run/Return methods with type explicit version for method 'UpdateStockPriceQtyIn'
type Repository_UpdateStockPriceQtyIn_Call struct {
	*mock.Call
}

// UpdateStockPriceQtyIn is a helper method to define mock.On call
//   - stockPriceId int
//   - qtyIn float32
func (_e *Repository_Expecter) UpdateStockPriceQtyIn(stockPriceId interface{}, qtyIn interface{}) *Repository_UpdateStockPriceQtyIn_Call {
	return &Repository_UpdateStockPriceQtyIn_Call{Call: _e.mock.On("UpdateStockPriceQtyIn", stockPriceId, qtyIn)}
}

func (_c *Repository_UpdateStockPriceQtyIn_Call) Run(run func(stockPriceId int, qtyIn float32)) *Repository_UpdateStockPriceQtyIn_Call {
	_c.Call.Run(func(args mock.Arguments) {
		run(args[0].(int), args[1].(float32))
	})
	return _c
}

func (_c *Repository_UpdateStockPriceQtyIn_Call) Return(_a0 error) *Repository_UpdateStockPriceQtyIn_Call {
	_c.Call.Return(_a0)
	return _c
}

func (_c *Repository_UpdateStockPriceQtyIn_Call) RunAndReturn(run func(int, float32) error) *Repository_UpdateStockPriceQtyIn_Call {
	_c.Call.Return(run)
	return _c
}

// NewRepository creates a new instance of Repository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *Repository {
	mock := &Repository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
