package crm

import "gitlab.com/uniqdev/backend/api-report/domain"

type Repository interface {
	QueryParam(param domain.RequestParam) Repository
	FetchCustomerList(domain.User) ([]map[string]interface{}, error)
	FetchCustomerTransactionList(user domain.User) ([]map[string]interface{}, error)
	FetchSalesMember(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error)
	FetchMemberRegisterRange(adminId int, param domain.RequestParam) ([]map[string]interface{}, error)
}
