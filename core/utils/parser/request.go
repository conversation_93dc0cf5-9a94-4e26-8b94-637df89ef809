package parser

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-report/controller/dashboard/v1"
	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/exception"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
)

func RequestParam(ctx *fasthttp.RequestCtx) domain.RequestParam {
	format := cast.ToString(ctx.UserValue("format"))

	param := domain.RequestParam{}
	if format == "datatable" {
		param.Search = string(ctx.PostArgs().Peek("search[value]"))
		param.Length = cast.ToInt(ctx.PostArgs().Peek("length"))
		param.Start = cast.ToInt(ctx.PostArgs().Peek("start"))
		param.Format = format

		orderColumn := cast.ToInt(ctx.PostArgs().Peek("order[0][column]"))
		orderColumnName := cast.ToString(ctx.PostArgs().Peek(fmt.Sprintf("columns[%d][data]", orderColumn)))
		if orderColumn >= 0 && orderColumnName != "" {
			param.Order = domain.RequestOrder{
				Column:    orderColumnName,
				Direction: cast.ToString(ctx.PostArgs().Peek("order[0][dir]")),
			}
		}

		//filter
		if outletIdStr := string(ctx.PostArgs().Peek("outletId")); ctx.PostArgs().Has("outletId") {
			param.OutletId = make([]int, 0)
			for _, id := range strings.Split(outletIdStr, ",") {
				param.OutletId = append(param.OutletId, cast.ToInt(id))
			}
		}
		param.DateStart = cast.ToInt64(ctx.PostArgs().Peek("startDate"))
		param.DateEnd = cast.ToInt64(ctx.PostArgs().Peek("endDate"))
		param.Period = string(ctx.PostArgs().Peek("period"))
		param.Offset = cast.ToInt(ctx.PostArgs().Peek("offset"))
		param.GroupBy = string(ctx.PostArgs().Peek("groupBy"))
	}
	return param
}

// to handle different keys sent by client
func takeAny(ctx *fasthttp.RequestCtx, keys ...string) string {
	for _, key := range keys {
		val := cast.ToString(ctx.PostArgs().Peek(key))
		if val != "" {
			return val
		}
	}
	return ""
}

func ParseRequestParm(ctx *fasthttp.RequestCtx) map[string][]byte {
	result := make(map[string][]byte)
	if ctx.IsPost() {
		ctx.PostArgs().VisitAll(func(key, value []byte) {
			result[string(key)] = value
		})
	} else if ctx.IsGet() {
		ctx.QueryArgs().VisitAll(func(key, value []byte) {
			result[string(key)] = value
		})
	}
	return result
}

func RequestParamSales(ctx *fasthttp.RequestCtx) domain.SalesReportRequest {
	user := domain.GetUserSessionOfFastHttp(ctx)
	// post := ctx.Request.PostArgs()

	params := ParseRequestParm(ctx)
	// method := cast.ToString(params["method"])
	outletIds := array.TransformStringToInt(strings.Split(cast.ToString(params["outlet"]), ","))
	shiftIds := array.TransformStringToInt(strings.Split(cast.ToString(params["shift"]), ","))
	methods := strings.Split(cast.ToString(params["method"]), ",")
	bankIds := array.TransformStringToInt(strings.Split(cast.ToString(params["bank"]), ","))
	timeZone := cast.ToInt(params["timeZone"])

	if params["method"] == nil {
		methods = []string{}
	}

	if timeZone == 0 {
		timeZone = 25200 // default timezone
	}

	endDate := cast.ToInt64(params["endDate"])

	//to avoid caching returning old data, if time end is greater than now, set to now
	if endDate > time.Now().UnixMilli() {
		endDate = time.Now().UnixMilli()
	}

	return domain.SalesReportRequest{
		StartDate:     cast.ToInt64(params["startDate"]),
		EndDate:       endDate,
		DataType:      cast.ToInt(params["dataType"]),
		Outlet:        user.ValidateOuletAccess(outletIds),
		Shift:         shiftIds,
		OffsetId:      cast.ToInt64(params["offsetId"]),
		TimeZone:      timeZone,
		Page:          cast.ToInt(params["page"]),
		PromotionId:   cast.ToInt(params["promotion_id"]),
		PaymentMethod: methods,
		BankId:        bankIds,
		Customer:      string(params["customer"]),
		Contact:       string(params["contact"]),
		DataStatus:    string(params["dataStatus"]),
	}
}

func RequestParamDashboard(ctx *fasthttp.RequestCtx) (domain.DashboardRequest, error) {
	var request domain.DashboardRequest
	err := json.Unmarshal(ctx.PostBody(), &request)
	if err != nil {
		return request, err
	}

	validationErr := v1.MyValidation(request)
	if len(validationErr) > 0 {
		return request, &exception.ValidationError{Data: validationErr}
	}

	return request, nil
}
