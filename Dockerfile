# syntax=docker/dockerfile:1.4
# build stage
FROM golang:alpine AS builder
#RUN apk --no-cache add build-base git bzr mercurial gcc
WORKDIR /src
COPY go.mod .
COPY go.sum .
RUN GO111MODULE=on go mod download
COPY . .
# RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o app-report server.go

# 3. use BuildKit cache mounts for both module- and build-cache
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 \
    go build -o app-report server.go

#final stage
FROM alpine
COPY config/template /config/template
COPY --from=builder src/app-report ./
ENTRYPOINT ["./app-report"]
