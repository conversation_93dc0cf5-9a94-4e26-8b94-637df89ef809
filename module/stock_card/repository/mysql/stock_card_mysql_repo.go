package mysql

import (
	"database/sql"
	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	cast2 "gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/stock_card/models"
	"sort"
	"strings"
)

type stockCardRepositoryMysql struct {
	mysql.Repository
}

func NewMysqlStockCardRepository(db *sql.DB) domain.StockCardRepositoryMysql {
	return &stockCardRepositoryMysql{mysql.Repository{Conn: db}}
}

func (s stockCardRepositoryMysql) FetchProductDetail(adminId string, outletId string, productId string, variantId string) (map[string]interface{}, error) {
	variantId = strings.ToLower(variantId)

	sql := "SELECT pd.* " +
		"FROM products_detail pd " +
		"LEFT JOIN products p ON pd.product_fkid=p.product_id " +
		"WHERE p.admin_fkid = ? " +
		"AND pd.outlet_fkid = ? " +
		"AND pd.product_fkid = ? "

	if variantId == "" || variantId == "null" {
		sql += "AND pd.variant_fkid IS NULL"
		return db.Query(sql, adminId, outletId, productId)
	} else {
		sql += "AND pd.variant_fkid = ?"
		return db.Query(sql, adminId, outletId, productId, variantId)
	}
}

func (s stockCardRepositoryMysql) FetchLastOpname(productdetailId string, timemillisMax int64) (int64, error) {
	sql := "SELECT max(so.time_created) AS last_time_opname " +
		"FROM stock_opname so " +
		"WHERE so.product_detail_fkid = ? " +
		"AND so.time_created < ?"

	result, err := db.Query(sql, productdetailId, timemillisMax)
	if err != nil {
		return cast.ToInt64(0), err
	}

	timeLastOpname := cast.ToInt64(0)
	if result != nil {
		timeLastOpname = cast.ToInt64(result["last_time_opname"])
	}

	return timeLastOpname, nil
}

func (s stockCardRepositoryMysql) FetchOpenAfterOpname(productdetailId string, timeStart int64, timeEnd int64) (float32, error) {
	data, err := s.FetchTransaction(productdetailId, timeStart, timeEnd)

	var totalCalculate float32
	for _, item := range data {
		totalCalculate += item.Calculate
	}

	return totalCalculate, err
}

func (s stockCardRepositoryMysql) FetchProductionPrimary(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				p.data_created,
				p.production_id AS id,
				p.admin_fkid,
				'production_primary_product' AS data_source,
				p.qty_primary AS qty,
				-(p.qty_primary) AS calculate,
				0 AS stock_in,
				p.qty_primary AS stock_out,
				0 AS opname,

				(CASE WHEN (p.employee_fkid>0)
				THEN (SELECT name FROM employee WHERE employee_id=p.employee_fkid)
				ELSE (SELECT name FROM admin WHERE admin_id=p.admin_fkid)
				END) AS operator
			FROM production p 
			WHERE p.product_detail_fkid = ? 
			AND p.data_created >= ? 
			AND p.data_created <= ? 
			AND p.data_delete_at IS NULL`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchProductionDetail(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				pd.data_created,
				pd.productiondetail_id AS id,
				p.admin_fkid,
				CONCAT('production_',pd.detail_type) AS data_source,
				pd.qty,
				(IF(pd.detail_type='ingredient',-(pd.qty),(pd.qty))) AS calculate,
				(IF(pd.detail_type='ingredient',0,(pd.qty))) AS stock_in,
				(IF(pd.detail_type='ingredient',(pd.qty),0)) AS stock_out,
				0 AS opname,

				(CASE WHEN (p.employee_fkid>0)
				THEN (SELECT name FROM employee WHERE employee_id=p.employee_fkid)
				ELSE (SELECT name FROM admin WHERE admin_id=p.admin_fkid)
				END) AS operator
			FROM production_detail pd
			LEFT JOIN production p ON pd.production_fkid = p.production_id
			WHERE pd.product_detail_fkid = ?
			AND pd.data_created >= ?
			AND pd.data_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchStockOpname(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				so.time_created AS data_created,
				so.opname_id AS id,
				so.admin_fkid,
				'opname' AS data_source,
				so.opname AS qty,
				so.opname AS calculate,
				0 AS stock_in,
				0 AS stock_out,
				so.opname,

				(CASE WHEN (so.employee_fkid>0)
				THEN (SELECT name FROM employee WHERE employee_id=so.employee_fkid)
				ELSE (SELECT name FROM admin WHERE admin_id=so.admin_fkid)
				END) AS operator
			FROM
				stock_opname so
			WHERE
				so.product_detail_fkid = ?
				AND so.time_created >= ?
				AND so.time_created <= ?
				AND so.data_status = ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd, "on")
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchRetur(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				rp.data_created,
				rp.retur_product_id AS id,
				rp.admin_fkid,
				'retur' AS data_source,
				rp.qty_retur AS qty,
				-(rp.qty_retur) AS calculate,
				0 AS stock_in,
				rp.qty_retur AS stock_out,
				0 AS opname,
				(CASE WHEN (rp.employee_fkid>0)
				THEN (SELECT name FROM employee WHERE employee_id=rp.employee_fkid)
				ELSE (SELECT name FROM admin WHERE admin_id=rp.admin_fkid)
				END) AS operator
			FROM
				retur_products rp
			LEFT JOIN
				purchase_products pp ON rp.purchase_product_fkid = pp.purchase_products_id
			LEFT JOIN
				purchase p ON pp.purchase_fkid = p.purchase_id
			LEFT JOIN
				products_detail pd ON pd.product_detail_id = pp.products_fkid
			WHERE
				pp.products_fkid = ?
				AND rp.data_created >= ?
				AND rp.data_created <= ?
				AND rp.data_status = ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd, "on")
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchPurchaseConfirm(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	// (CASE WHEN (pc.employe_fkid>0)
	// 			THEN (SELECT name FROM employee WHERE employee_id=pc.employe_fkid)
	// 			ELSE (SELECT name FROM admin WHERE admin_id=p.admin_fkid)
	// 			END) AS operator
	sql := `SELECT
				p.purchase_id AS parent_id,
				pc.date_created AS data_created,
				pc.purchase_confrim_id AS id,
				p.admin_fkid,
				'purchase_confirm' AS data_source,
				pc.qty_arive AS qty,
				pc.qty_arive AS calculate,
				pc.qty_arive AS stock_in,
				0 AS stock_out,
				0 AS opname,

				pc.user AS operator
			FROM purchase_confrim pc
			LEFT JOIN purchase_products pp ON pc.purchase_product_fkid = pp.purchase_products_id
			LEFT JOIN purchase p ON pp.purchase_fkid = p.purchase_id
			WHERE pp.products_fkid = ?
			AND pc.date_confirm >= ?
			AND pc.date_confirm <= ?
			AND pc.data_status = ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd, "on")
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSales(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				s.time_created AS data_created,
				sd.sales_detail_id AS id,
				o.admin_fkid AS admin_fkid,
				'sales' AS data_source,
				(sd.qty) AS qty,
				-(sd.qty) AS calculate,
				0 AS stock_in,
				sd.qty AS stock_out,
				0 AS opname,
				e.name AS operator
			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid = s.sales_id
			LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
			LEFT JOIN employee e ON s.employee_fkid = e.employee_id
			WHERE sd.product_detail_fkid = ?
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND s.data_status = ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd, "on")
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSalesRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT '' AS parent_id,
			sr.time_created AS data_created,
			sr.sales_refund_id AS id,
			o.admin_fkid,
			'sales_refund' AS data_source,
			(SELECT SUM(qty) FROM sales_detail WHERE product_detail_fkid= ? AND sales_fkid=s.sales_id) - (SELECT IFNULL(sum(qty),0) FROM sales_void WHERE product_detail_fkid=? AND sales_fkid=sr.sales_fkid) AS qty,
			(SELECT SUM(qty) FROM sales_detail WHERE product_detail_fkid= ? AND sales_fkid=s.sales_id) - (SELECT IFNULL(sum(qty),0) FROM sales_void WHERE product_detail_fkid=? AND sales_fkid=sr.sales_fkid) AS calculate,
			(SELECT SUM(qty) FROM sales_detail WHERE product_detail_fkid= ? AND sales_fkid=s.sales_id) - (SELECT IFNULL(sum(qty),0) FROM sales_void WHERE product_detail_fkid=? AND sales_fkid=sr.sales_fkid) AS stock_in,
			0 AS stock_out,
			0 AS opname,
			e.name AS operator
			FROM sales_refund sr
			LEFT JOIN sales s ON sr.sales_fkid = s.sales_id
			LEFT JOIN sales_detail sd ON s.sales_id = sd.sales_fkid
			LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
			LEFT JOIN employee e ON sr.employee_fkid = e.employee_id
			WHERE sd.product_detail_fkid = ?
			AND sr.time_created >= ?
			AND sr.time_created <= ?
			AND s.status = ?
			AND s.data_status = ?`
	result, err := db.QueryArray(sql, productdetailId, productdetailId, productdetailId, productdetailId, productdetailId, productdetailId, productdetailId, timeStart, timeEnd, "refund", "on")
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSalesVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				'' AS parent_id,
				sv.time_created AS data_created,
				sv.sales_void_id AS id,
				o.admin_fkid,
				'sales_void' AS data_source,
				sv.qty,
				sv.qty AS calculate,
				sv.qty AS stock_in,
				0 AS stock_out,
				0 AS opname,
				e.name AS operator
			FROM sales_void sv
			LEFT JOIN sales s ON sv.sales_fkid = s.sales_id
			LEFT JOIN	outlets o ON s.outlet_fkid = o.outlet_id
			LEFT JOIN	employee e ON sv.employee_fkid = e.employee_id
			WHERE	sv.product_detail_fkid = ?
			AND sv.time_created >= ?
			AND sv.time_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchTransferOut(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				t.transfer_id AS parent_id,
				tc.data_created,
				tc.transfer_confirm_id AS id,
				t.user_fkid AS admin_fkid,
				'transfer_confirm' AS data_source,
				tc.qty_confirm AS qty,
				-(tc.qty_confirm) AS calculate,
				0 AS stock_in,
				tc.qty_confirm AS stock_out,
				0 AS opname,
				(CASE
					WHEN (tc.employe_fkid > 0) THEN (SELECT name FROM employee WHERE employee_id = tc.employe_fkid)
					ELSE (SELECT name FROM admin WHERE admin_id = t.user_fkid)
				END) AS operator
			FROM
				transfer_confirm tc
			LEFT JOIN
				transfer_products tp ON tc.transfer_product_fkid = tp.transfer_product_id
			LEFT JOIN
				transfer t ON tp.transfer_fkid = t.transfer_id
			WHERE
				tp.product_detail_fkid = ?
				AND tc.data_created >= ?
				AND tc.data_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchTransferIn(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
				t.transfer_id AS parent_id,
				tc.data_created,
				tc.transfer_confirm_id AS id,
				t.user_fkid AS admin_fkid,
				'transfer_confirm' AS data_source,
				tc.qty_confirm AS qty,
				tc.qty_confirm AS calculate,
				tc.qty_confirm AS stock_in,
				0 AS stock_out,
				0 AS opname,

				(CASE WHEN (tc.employe_fkid>0)
				THEN (SELECT name FROM employee WHERE employee_id=tc.employe_fkid)
				ELSE (SELECT name FROM admin WHERE admin_id=t.user_fkid)
				END) AS operator
			FROM transfer_confirm tc
			LEFT JOIN transfer_products tp ON tc.transfer_product_fkid = tp.transfer_product_id
			LEFT JOIN transfer t ON tp.transfer_fkid = t.transfer_id
			WHERE tp.product_detail_des_fkid = ?
			AND tc.data_created >= ?
			AND tc.data_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSpoil(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
			    '' AS parent_id,
			    s.time_created AS data_created,
			    s.spoil_id AS id,
			    s.admin_fkid,
			    'spoil' AS data_source,
			    s.qty,
			    -(s.qty) AS calculate,
			    0 AS stock_in,
			    s.qty AS stock_out,
			    0 AS opname,
			    (CASE
			        WHEN (s.user_input > 0) THEN (SELECT name FROM employee WHERE employee_id = s.user_input)
			        ELSE (SELECT name FROM admin WHERE admin_id = s.admin_fkid)
			    END) AS operator
			FROM
			    spoils s
			WHERE
			    s.product_detail_fkid = ?
			    AND s.time_created >= ?
			    AND s.time_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
			    '' AS parent_id,
			    sb.time_created AS data_created,
			    sb.sales_breakdown_id AS id,
			    p.admin_fkid,
			    'sales_breakdown' AS data_source,
			    sb.qty_total AS qty,
			    -(sb.qty_total) AS calculate,
			    0 AS stock_in,
			    sb.qty_total AS stock_out,
			    0 AS opname,
			    '' AS operator
			FROM
			    sales_breakdown sb
			LEFT JOIN
			    products_detail pd ON pd.product_detail_id = sb.product_detail_fkid
			LEFT JOIN
			    products p ON p.product_id = pd.product_fkid
			WHERE
			    sb.status = ?
			    AND sb.product_detail_fkid = ?
			    AND sb.time_created >= ?
			    AND sb.time_created <= ?`
	result, err := db.QueryArray(sql, "success", productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
			    '' AS parent_id,
			    sv.time_created AS data_created,
			    sb.sales_breakdown_id AS id,
			    p.admin_fkid,
			    'sales_breakdown_void' AS data_source,
			    sb.qty_total AS qty,
			    sb.qty_total AS calculate,
			    sb.qty_total AS stock_in,
			    0 AS stock_out,
			    0 AS opname,
			    e.name AS operator
			FROM
			    sales_void sv
			LEFT JOIN
			    sales_breakdown sb ON sv.sales_void_id = sb.sales_void_fkid
			LEFT JOIN
			    employee e ON e.employee_id = sv.employee_fkid
			LEFT JOIN
			    products_detail pd ON sb.product_detail_fkid = pd.product_detail_id
			LEFT JOIN
			    products p ON pd.product_fkid = p.product_id
			WHERE
			    sb.product_detail_fkid = ?
			    AND sb.time_created >= ?
			    AND sb.time_created <= ?`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	sql := `SELECT
			    '' AS parent_id,
			    sr.time_created AS data_created,
			    sd.sales_detail_id AS id,
			    p.admin_fkid,
			    'sales_breakdown_refund' AS data_source,
			    SUM(
			        IF(sb.status = 'void', (-1 * sb.qty_total), (1 * sb.qty_total))
			    ) AS qty,
			    SUM(
			        IF(sb.status = 'void', (-1 * sb.qty_total), (1 * sb.qty_total))
			    ) AS calculate,
			    SUM(
			        IF(sb.status = 'void', (-1 * sb.qty_total), (1 * sb.qty_total))
			    ) AS stock_in,
			    0 AS stock_out,
			    0 AS opname,
			    '' AS operator
			FROM
			    sales_breakdown sb
			LEFT JOIN
			    sales_detail sd ON sb.sales_detail_fkid = sd.sales_detail_id
			LEFT JOIN
			    sales s ON s.sales_id = sd.sales_fkid
			LEFT JOIN
			    sales_refund sr ON sr.sales_fkid = s.sales_id
			LEFT JOIN
			    products_detail pd ON pd.product_detail_id = sb.product_detail_fkid
			LEFT JOIN
			    products p ON p.product_id = pd.product_fkid
			WHERE
			    sb.sales_status = 'refund'
			    AND pd.product_detail_id = ?
			    AND sb.time_created >= ?
			    AND sb.time_created <= ?
			GROUP BY
			    id, admin_fkid`
	result, err := db.QueryArray(sql, productdetailId, timeStart, timeEnd)
	if err != nil {
		log.IfError(err)
		return nil, err
	}

	var data []models.StockCardTransactionDBResponse
	err = cast2.ToModel(result, &data)
	log.IfError(err)
	return data, err
}

func (s stockCardRepositoryMysql) FetchTransaction(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	chanProductionPrimary := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchProductionPrimary(productdetailId, timeStart, timeEnd)
		chanProductionPrimary <- result
	}()

	chanProductionDetail := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchProductionDetail(productdetailId, timeStart, timeEnd)
		chanProductionDetail <- result
	}()

	chanStockOpname := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchStockOpname(productdetailId, timeStart, timeEnd)
		chanStockOpname <- result
	}()

	chanRetur := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchRetur(productdetailId, timeStart, timeEnd)
		chanRetur <- result
	}()

	chanPurchaseConfirm := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchPurchaseConfirm(productdetailId, timeStart, timeEnd)
		chanPurchaseConfirm <- result
	}()

	chanSales := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSales(productdetailId, timeStart, timeEnd)
		chanSales <- result
	}()

	chanSalesRefund := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesRefund(productdetailId, timeStart, timeEnd)
		chanSalesRefund <- result
	}()

	chanSalesVoid := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesVoid(productdetailId, timeStart, timeEnd)
		chanSalesVoid <- result
	}()

	chanTransferOut := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchTransferOut(productdetailId, timeStart, timeEnd)
		chanTransferOut <- result
	}()

	chanTransferIn := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchTransferIn(productdetailId, timeStart, timeEnd)
		chanTransferIn <- result
	}()

	chanSpoil := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSpoil(productdetailId, timeStart, timeEnd)
		chanSpoil <- result
	}()

	chanSalesBreakdown := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdown(productdetailId, timeStart, timeEnd)
		chanSalesBreakdown <- result
	}()

	chanSalesBreakdownVoid := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdownVoid(productdetailId, timeStart, timeEnd)
		chanSalesBreakdownVoid <- result
	}()

	chanSalesBreakdownRefund := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdownRefund(productdetailId, timeStart, timeEnd)
		chanSalesBreakdownRefund <- result
	}()

	// Menggunakan goroutine untuk menggabungkan hasil dari channel
	// Daftar channel
	channels := []chan []models.StockCardTransactionDBResponse{
		chanProductionPrimary,
		chanProductionDetail,
		chanStockOpname,
		chanRetur,
		chanPurchaseConfirm,
		chanSales,
		chanSalesRefund,
		chanSalesVoid,
		chanTransferOut,
		chanTransferIn,
		chanSpoil,
		chanSalesBreakdown,
		chanSalesBreakdownVoid,
		chanSalesBreakdownRefund,
	}

	// Menggunakan goroutine untuk menggabungkan hasil dari semua channel
	chanMergedResult := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		var resultMergedResult []models.StockCardTransactionDBResponse

		// Loop untuk mengambil hasil dari semua channel
		for _, ch := range channels {
			result := <-ch
			//log.Debug("Channel from %v to %v -> %v", timeStart, timeEnd, result)
			resultMergedResult = append(resultMergedResult, result...)
		}

		// Kirim hasil gabungan ke channel baru
		chanMergedResult <- resultMergedResult
	}()

	// Ambil hasil gabungan dari channel baru
	finalResult := <-chanMergedResult

	// Tampilkan hasil
	//log.Debug("Final Result: %v", finalResult)

	// Mengurutkan finalResult berdasarkan kriteria yang diinginkan
	sort.Slice(finalResult, func(i, j int) bool {
		// Urutkan berdasarkan data_created, data_source, dan id
		if finalResult[i].DataCreated != finalResult[j].DataCreated {
			return finalResult[i].DataCreated < finalResult[j].DataCreated
		}
		if finalResult[i].DataSource != finalResult[j].DataSource {
			return finalResult[i].DataSource < finalResult[j].DataSource
		}
		return finalResult[i].ID < finalResult[j].ID
	})

	// Tampilkan hasil yang sudah diurutkan
	//fmt.Println("Sorted Result: ", finalResult)

	return finalResult, nil
}
