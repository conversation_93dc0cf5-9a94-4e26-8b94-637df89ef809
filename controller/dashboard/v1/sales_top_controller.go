package v1

import (
	"encoding/json"
	"net/http"
	"strings"

	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func SalesTop_v1(ctx *fasthttp.RequestCtx) {
	limit := ctx.UserValue("limit")
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: false,
			Code:   400,
			Data:   err,
		})
	} else {
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		timeStartPrevious := timeStart - 1 - (timeEnd - timeStart)
		timeEndPrevious := timeStart - 1
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess, ",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//set legend
		timeStartTime := utils.MillisToTime(timeStart + timeDiff)
		timeEndTime := utils.MillisToTime(timeEnd + timeDiff)
		timeStartPreviousTime := utils.MillisToTime(timeStartPrevious + timeDiff)
		legendNowText := ""
		legendPreviousText := ""
		if timeStartTime.Format("02-01-2006") == timeEndTime.Format("02-01-2006") {
			legendNowText = timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		} else {
			legendNowText = timeStartTime.Format("02-01-2006") + " to " + timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartPreviousTime.Format("02-01-2006") + " to " + timeStartTime.AddDate(0, 0, -1).Format("02-01-2006")
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		//render semua data
		response := map[string]interface{}{
			"result": SalesTop_map(adminId, timeStart, timeEnd, timeStartPrevious, timeEndPrevious, outletIds, limit),
			"legend": map[string]interface{}{
				"now":  legendNowText,
				"prev": legendPreviousText,
			},
		}

		//output
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status: true,
			Code:   200,
			Data:   response,
		})
	}
}
