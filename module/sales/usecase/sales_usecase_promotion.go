package usecase

import (
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/transform"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

func (s salesUseCase) FetchSalesPromotion(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	var repo domain.SalesRepository
	if request.EndDate > repository.GetMaxTimeSync() {
		repo = s.repoPrimary
	} else {
		repo = s.repoReplica
	}

	salesPromotion, err := repo.FetchSalesPromotion(user, request)
	if err != nil {
		return map[string]interface{}{}, err
	}

	if len(salesPromotion) == 0 {
		return map[string]interface{}{}, err
	}

	productIdsMap := make(map[int64]bool, 0)
	shiftIdsMap := make(map[int]bool, 0)
	for _, row := range salesPromotion {
		productIdsMap[cast.ToInt64(row["product_detail_fkid"])] = true
		shiftIdsMap[cast.ToInt(row["shift_fkid"])] = true
	}

	productIds := make([]interface{}, 0)
	for id, _ := range productIdsMap {
		productIds = append(productIds, id)
	}

	products, err := s.repoPrimary.FetchProductByDetailIds(productIds...)
	if err != nil {
		return map[string]interface{}{}, err
	}

	productMap := transform.MapByKey("product_detail_id", products)

	shiftIds := make([]interface{}, 0)
	for id, _ := range shiftIdsMap {
		shiftIds = append(shiftIds, id)
	}
	shifts, err := s.repoPrimary.FetchShiftByIds(shiftIds...)
	if err != nil {
		return map[string]interface{}{}, err
	}

	shiftMap := transform.MapByKey("id", shifts)

	for i, row := range salesPromotion {
		product := productMap[cast.ToString(row["product_detail_fkid"])]
		salesPromotion[i] = transform.MergeMap(row, product)
		salesPromotion[i]["shift"] = shiftMap[cast.ToString(row["shift_fkid"])]["name"]
	}

	result := map[string]interface{}{
		"data": salesPromotion,
	}

	return result, nil
}
