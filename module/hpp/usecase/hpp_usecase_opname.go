package usecase

import (
	"fmt"
	"math"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (h *hppUseCase) HandleStockOpname(opnameIds []int) {
	//fetch the product_id
	stockOpnames, err := h.repo.FetchStockOpname(cast.ToInterfaceArray(opnameIds)...)
	if err != nil {
		return
	}

	log.Info("lenIds: %v | lenData: %v | stockOpanmeIds: %v", len(opnameIds), len(stockOpnames), opnameIds)
	if len(stockOpnames) == 0 {
		log.IfError(fmt.Errorf("stock opname not found: %v", opnameIds))
		return
	}

	productDetailIdMap := make(map[int]bool)
	for _, opname := range stockOpnames {
		productDetailIdMap[opname.ProductDetailFKID] = true
	}
	productDetailIds := array.MapKey[int, bool](productDetailIdMap)
	log.Info("productIds: %v", productDetailIds)

	// using filter fetch stockPrice (with all product ids)
	filter := domain.StockPriceFilter{
		ProductDetailIds: productDetailIds,
		IsAvailable:      true,
	}
	stockPriceAllProducts, err := h.repo.Filter(filter).FetchStockPrice()
	if log.IfError(err) {
		return
	}
	if stockPriceAllProducts == nil {
		stockPriceAllProducts = &[]domain.Hpp{}
	}

	// Store stockPrice per product detail id, in map
	stockPriceMap := make(map[int][]domain.Hpp)
	for _, stockPrice := range *stockPriceAllProducts {
		stockPriceMap[stockPrice.ProductDetailFkid] = append(stockPriceMap[stockPrice.ProductDetailFkid], stockPrice)
	}

	for _, opname := range stockOpnames {
		stockPrices := stockPriceMap[opname.ProductDetailFKID]

		availStock := float32(0)
		lastPrice := float32(0)
		for _, stock := range stockPrices {
			availStock += stock.StockIn - stock.StockOut
			lastPrice = stock.Price
		}

		//if no stockPrices (active stock-price), last price get from product
		//note: price can also be fetched from inactive stock price (if any)
		if len(stockPrices) == 0 {
			details, err := h.repoProduct.FetchProductDetail([]int{opname.ProductDetailFKID})
			if !log.IfError(err) && len(details) > 0 {
				lastPrice = cast.ToFloat32(details[0].PriceBuy)
			}
		}

		//check diff
		//real < avail : treat at sales

		diff := float32(opname.Opname) - availStock
		// diff := opname.GetBalance() //calculate diff from stock-opname table
		log.Info("product '%v' | avail: %v | opname: %v | diff (db) %v", opname.ProductDetailFKID, availStock, opname.Opname, diff)
		if bl := (float32(opname.Opname) - availStock); math.Abs(float64(bl-opname.GetBalance())) > 0 {
			msg := fmt.Sprintf("balance not the same, from fifo: %v, from db: %v, prodDetId: %v, opnameId: %v, stockPrices: %v", bl, opname.GetBalance(), opname.ProductDetailFKID, opname.OpnameID, utils.SimplyToJson(stockPrices))
			log.Info("stockPriceMap: %v", utils.SimplyToJson(stockPriceMap))
			if len(stockPrices) > 0 {
				log.IfError(fmt.Errorf(msg))
			} else {
				log.Info(msg)
			}
		}

		//rule: if minuse, treat as sales (stock out), if plus treat as stock in
		if diff < 0 {
			diff = diff * -1
			for _, stock := range stockPrices {
				readyToUse := (stock.StockIn) - (stock.StockOut)
				stockTaken := float32(0)
				if readyToUse > diff {
					stockTaken = diff
				} else {
					stockTaken = readyToUse
				}

				//update stock_out stock_price
				newStockOut := stockTaken + stock.StockOut
				log.Info("stockPrice: %v | new stockOut: %v | addToNewDetai: %v", stock.StockPriceId, newStockOut, stockTaken)

				//insert to stock_price_detail
				err = h.repo.InsertStockOutDetail(domain.StockPriceDetail{
					StockPriceId:   stock.StockPriceId,
					Qty:            float32(stockTaken),
					StockOutId:     cast.ToString(opname.OpnameID),
					StockOutSource: domain.StockOpname.String(),
				})
				if log.IfError(err) {
					break
				}

				diff -= stockTaken
				if diff <= 0 {
					break
				}
			}

			//keep insert to stock_price, event qty is 0
			//this to make system easy when adjustment happen in the future
			stockInQty := float32(0)
			if len(stockPrices) == 0 {
				stockInQty = opname.Opname
			}
			id, err := h.repo.InsertStockPrice(domain.Hpp{
				ProductDetailFkid: opname.ProductDetailFKID,
				StockIn:           stockInQty,
				Price:             lastPrice,
				StockInId:         cast.ToString(opname.OpnameID),
				StockInSource:     domain.StockOpname.String(),
				StockInMetaData:   opname.GetMetaData(),
				StockOutMetaData:  opname.GetMetaData(),
			})
			if !log.IfError(err) {
				log.Info("stock price added: %v, in: %v, inId: %v", id, stockInQty, opname.OpnameID)
			}
		} else if diff > 0 {
			log.Info("adding new stockPrice: prodId %v | lastPrice: %v | qtyDiff: %v", opname.ProductDetailFKID, lastPrice, diff)
			h.logStockIn(&[]models.StockInProduct{
				{
					ProductDetailId: opname.ProductDetailFKID,
					Qty:             diff,
					Price:           lastPrice,
					MetaData:        opname.GetMetaData(),
					CreatedAt:       opname.TimeCreated,
				},
			}, domain.StockOpname.String(), cast.ToString(opname.OpnameID))
		} else if len(stockPrices) == 0 || diff == 0 { //if data not exist or balance 0
			id, err := h.repo.InsertStockPrice(domain.Hpp{
				ProductDetailFkid: opname.ProductDetailFKID,
				StockIn:           diff,
				Price:             lastPrice,
				StockInId:         cast.ToString(opname.OpnameID),
				StockInSource:     domain.StockOpname.String(),
				StockInMetaData:   opname.GetMetaData(),
			})
			if err == nil {
				log.Info("stock price added: %v, in: %v, inId: %v", id, diff, opname.OpnameID)
				log.Info("%v. adding new stockPrice: prodId %v, qty: %v, price: %v", id, opname.ProductDetailFKID, opname.Opname)
			}
		}
	}
}
