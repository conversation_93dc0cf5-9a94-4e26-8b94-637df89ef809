package usecase

import (
	"encoding/json"
	"fmt"
	"testing"

	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
	"gitlab.com/uniqdev/backend/api-report/module/purchase"
)

func Test_mapSet(t *testing.T) {
	data := map[string]interface{}{
		"stock_price_fkid": 1,
	}
	data["name"] = utils.CurrentMillis()
	fmt.Println(data)
}

func Test_map(t *testing.T) {
	data := make(map[string][]string)
	data["one"] = append(data["one"], "two")

	ones := &[]domain.Hpp{
		{StockPriceId: 1},
		{StockPriceId: 2},
		{StockPriceId: 3},
	}

	all := make(map[int][]domain.Hpp)
	for _, data := range *ones {
		all[data.StockPriceId] = append(all[data.StockPriceId], data)
	}
	allJs, _ := json.Marshal(all)
	fmt.Println(string(allJs))
}

func Test_array(t *testing.T) {
	data := make([]models.StockPriceEntity, 0)

	singleData := models.StockPriceEntity{
		ID:       1,
		StockOut: 1,
	}
	data = append(data, singleData)
	data[0].StockOut = 2

	fmt.Println("ori: ", singleData.StockOut)
	fmt.Println("new: ", data[0].StockOut)

	dataJson, _ := json.Marshal(data)
	singleJson, _ := json.Marshal(singleData)
	fmt.Println(string(dataJson))
	fmt.Println(string(singleJson))
}

func Test_hppUseCase_recordTransferConfirm(t *testing.T) {
	type fields struct {
		repo         hpp.Repository
		repoPurchase purchase.Repository
		repoProduct  domain.ProductRepository
	}
	type args struct {
		transferId int
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{"test1", fields{}, args{1}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo:         tt.fields.repo,
				repoPurchase: tt.fields.repoPurchase,
				repoProduct:  tt.fields.repoProduct,
			}
			h.recordTransferConfirm(tt.args.transferId)
		})
	}
}
