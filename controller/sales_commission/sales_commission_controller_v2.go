package v1

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strings"
)

func SalesCommission(ctx *fasthttp.RequestCtx)  {
	//init
	adminId := utils.ToString(ctx.Request.Header.Peek("admin_id"))

	//data input
	start := 0
	if ctx.FormValue("start") != nil {
		start = utils.ToInt(ctx.FormValue("start"))
	}
	limit := 10
	if ctx.FormValue("limit") != nil {
		limit = utils.ToInt(ctx.FormValue("limit"))
	}
	timeStart := cast.ToInt64(string(ctx.FormValue("time_start")))
	timeEnd := cast.ToInt64(string(ctx.FormValue("time_end")))
	//outlet
	outletIds := utils.ToString(ctx.FormValue("outlet_id"))
	if outletIds == "" {
		outletIds = utils.ToString(ctx.Request.Header.Peek("outlet_access"))
	}
	queryWhereInOutletIds := strings.TrimSpace(outletIds)
	//product type
	productTypeIds := strings.TrimSpace(utils.ToString(ctx.FormValue("product_type_id")))
	queryWhereInProductTypeId := ""
	if productTypeIds != "" {
		queryWhereInProductTypeId = " AND p.product_type_fkid IN ( "+productTypeIds+" )"
	}
	//product category
	productCategoryIds := strings.TrimSpace(utils.ToString(ctx.FormValue("product_category_id")))
	queryWhereInProductCategoryId := ""
	if productCategoryIds != "" {
		queryWhereInProductCategoryId = " AND p.product_category_fkid IN ( "+productCategoryIds+" )"
	}
	//product subcategory
	productSubCategoryIds := strings.TrimSpace(utils.ToString(ctx.FormValue("product_subcategory_id")))
	queryWhereInProductSubCategoryId := ""
	if productSubCategoryIds != "" {
		queryWhereInProductSubCategoryId = " AND p.product_subcategory_fkid IN ( "+productSubCategoryIds+" )"
	}
	//shift
	shiftIds := strings.TrimSpace(utils.ToString(ctx.FormValue("shift_id")))
	queryWhereInShiftId := ""
	if shiftIds != "" {
		queryWhereInShiftId = " AND sh.shift_id IN ("+shiftIds+")"
	}

	fmt.Printf("admin: %s | time: %s/%s | page: %s/%s | outlet: %s \n", adminId, timeStart,timeEnd, start,limit, outletIds)

	//ambil data produk
	chanDataProduct := make(chan map[string]map[string]interface{})
	chanDataProductIdsForWhereIn := make(chan string)
	go func() {
		query, err := db.QueryArray(`
			SELECT DISTINCT
			(IF(pd.variant_fkid IS NULL, p.name, concat(p.name, ' (',v.variant_name,')'))) AS item,
			p.product_id, v.variant_id,
			pt.name AS type,
			pc.name AS category,
			psc.name AS subcategory,
			(IF(pd.variant_fkid IS NULL, p.sku, concat(p.sku, '-',v.variant_sku,')'))) AS sku
			FROM products_detail pd
			LEFT JOIN products p ON p.product_id=pd.product_fkid
			LEFT JOIN products_detail_variant v ON v.variant_id=pd.variant_fkid
			LEFT JOIN products_type pt ON pt.products_type_id=p.product_type_fkid 
			LEFT JOIN products_category pc ON pc.product_category_id=p.product_category_fkid 
			LEFT JOIN products_subcategory psc ON psc.product_subcategory_id=p.product_subcategory_fkid
			WHERE p.admin_fkid = ?
			AND p.data_status = ? `+
			queryWhereInProductTypeId+
			queryWhereInProductCategoryId+
			queryWhereInProductSubCategoryId, adminId, "on")
		if err != nil {
			utils.CheckErr(err)
		}

		tmpData := map[string]map[string]interface{}{}
		tmpProductIds := ""
		for _, data := range query {
			productId := utils.ToString(data["product_id"])
			variantId := ""
			if (data["variant_id"]) != nil {
				variantId = utils.ToString(data["variant_id"])
			}

			//render
			dataToMap := map[string]interface{}{
				"item": data["item"],
				"sku": data["sku"],
				"type": data["type"],
				"category": data["category"],
				"subcategory": data["subcategory"],
			}
			tmpData[productId] = map[string]interface{}{} //make(map[string]interface{})
			tmpData[productId][variantId] = dataToMap

			if tmpData[productId] != nil {
				tmpProductIds += productId+","
			}
		}
		tmpProductIds = strings.TrimRight(tmpProductIds,",")
		chanDataProductIdsForWhereIn <- tmpProductIds
		chanDataProduct <- tmpData
	}()

	//ambil data konten void
	chanDataSalesVoid := make(chan map[string]interface{})
	go func() {
		query, err := db.QueryArray(`
			SELECT SUM(qty) AS qty, SUM(sub_total) AS subtotal, sales_detail_fkid
			FROM sales_void
			LEFT JOIN sales ON sales_void.sales_fkid=sales.sales_id
			WHERE sales.time_created >= ?
			AND sales.time_created <= ?
			AND sales.outlet_fkid IN ( ? )
			GROUP BY sales_detail_fkid
		`, timeStart, timeEnd, queryWhereInOutletIds)
		if err != nil {
			fmt.Println(err)
		}
		data := map[string]interface{}{}
		for _, data := range query {
			salesDetailId := utils.ToString(data["sales_detail_fkid"])
			data[salesDetailId] = map[string]interface{}{
				"qty": data["qty"],
				"subtotal": data["subtotal"],
			}
		}
		chanDataSalesVoid <-data
	}()

	//mengambil data konten
	dataProductIdsForWhereIn := <-chanDataProductIdsForWhereIn
	chanDataSales := make(chan []map[string]interface{})
	go func() {
		queryWhereInProductId := ""
		if dataProductIdsForWhereIn != "" {
			queryWhereInProductId = "AND sd.product_fkid IN ("+dataProductIdsForWhereIn+")"
		}
		query, err := db.QueryArray(`
			SELECT
			sd.sales_detail_id AS id,
			o.name AS outlet,
			s.display_nota,
			s.outlet_fkid AS outlet_id,
			pd.product_fkid AS product_id,
			pd.variant_fkid AS variant_id,
			sh.name AS shift,
			sd.qty,
			sd.sub_total AS subtotal,
			sd.commission_staff,
			sd.commission_customer

			FROM sales_detail sd
			LEFT JOIN sales s ON sd.sales_fkid=s.sales_id
			LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid 
			LEFT JOIN open_shift os ON s.open_shift_fkid=os.open_shift_id 
			LEFT JOIN shift sh ON sh.shift_id=os.shift_fkid 
			LEFT JOIN products_detail pd ON pd.product_detail_id=sd.product_detail_fkid 
			LEFT JOIN products p ON p.product_id=pd.product_fkid
			
			WHERE s.status='success'
			AND s.data_status='on'
			AND s.outlet_fkid IN (`+queryWhereInOutletIds+`)
			AND s.time_created >= ?
			AND s.time_created <= ?
			AND o.admin_fkid = ?
			AND p.data_status='on'
			`+queryWhereInShiftId+
			queryWhereInProductId+
			/*
			queryWhereInProductTypeId+
			queryWhereInProductCategoryId+
			queryWhereInProductSubCategoryId+
			*/
			` LIMIT ?, ?
		`, timeStart, timeEnd, adminId, start, limit)
		if err != nil {
			utils.CheckErr(err)
		}

		chanDataSales <- query
	}()


	//data
	dataProduct := <-chanDataProduct
	dataSales := <-chanDataSales //[]map[string]interface{}{}
	dataVoid := <-chanDataSalesVoid
	dataRender := []map[string]interface{}{}
	for _, sale := range dataSales {
		salesDetailId := utils.ToString(sale["id"])
		dataVoid := dataVoid[salesDetailId]
		voidQty := cast.ToInt64(0)
		voidSubtotal := cast.ToInt64(0)
		if dataVoid != nil {
			void := dataVoid.(map[string]int64)
			voidQty = void["qty"]
			voidSubtotal = void["subtotal"]
		}
		saleQty := cast.ToInt64(sale["qty"]) - voidQty
		saleSubtotal := cast.ToInt64(sale["subtotal"]) + voidSubtotal

		//fmt.Println("sale qty clean: ", saleQty)
		if saleQty > 0 {
			productId := utils.ToString(sale["product_id"])
			variantId := utils.ToString(sale["variant_id"])
			dataItem := dataProduct[productId][variantId]
			if dataItem != nil {
				item := dataItem.(map[string]interface{})
				dataRender = append(dataRender, map[string]interface{}{
					"id": sale["id"],
					"display_nota": sale["display_nota"],
					"item": item["item"],
					"sku": item["sku"],
					"type": item["type"],
					"category": item["category"],
					"subcategory": item["subcategory"],
					"shift": sale["shift"],
					"outlet": sale["outlet"],
					"qty": saleQty,
					"subtotal": saleSubtotal,
					"commission_staff": cast.ToInt64(sale["commission_staff"]),
					"commission_customer": cast.ToInt64(sale["commission_customer"]),
				})
			}
		}
	}
	//fmt.Println(dataRender)
	//fmt.Println(dataVoid)

	//paging
	url := strings.Split(cast.ToString(ctx.URI()),"?")[0]
	param := map[string]interface{}{
		"time_start": timeStart,
		"time_end": timeEnd,
		"outlet_id": outletIds,
		"shift_id": shiftIds,
		"product_type_id": productTypeIds,
		"product_category_id": productCategoryIds,
		"product_subcategory_id": productSubCategoryIds,
		"limit": limit,
	}
	url_next := ""
	if len(dataRender) > 0 {
		param_next := map[string]interface{}{
			"start": start+limit,
		}
		param_next = utils.MergeKeys(param, param_next)
		url_next = url + "?" + utils.BuildHttpQuery(param_next)
	}
	url_prev := ""
	start_prev := start - limit;
	if start_prev >= 0 {
		param_prev := map[string]interface{}{
			"start": start_prev,
		}
		param_prev = utils.MergeKeys(param_prev, param)
		url_prev = url + "?" + utils.BuildHttpQuery(param_prev)
	}

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status": true,
		"paging": map[string]interface{}{
			"prev": url_prev,
			"next": url_next,
		},
		"data": dataRender,
	})
}