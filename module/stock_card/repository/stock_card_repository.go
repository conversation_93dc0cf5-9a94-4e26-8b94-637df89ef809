package repository

import (
	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
	"gitlab.com/uniqdev/backend/api-report/module/stock_card/models"
	"sort"
)

type stockCardRepository struct {
	repo         domain.StockCardRepositoryMiddleware
	repoMysql    domain.StockCardRepositoryMysql
	repoBigquery domain.StockCardRepositoryBigquery
}

func NewStockCardRepositoryMiddleware(repoMysql domain.StockCardRepositoryMysql, repoBigquery domain.StockCardRepositoryBigquery) domain.StockCardRepositoryMiddleware {
	return stockCardRepository{repoMysql: repoMysql, repoBigquery: repoBigquery}
}

func (s stockCardRepository) FetchProductDetail(adminId string, outletId string, productId string, variantId string) (map[string]interface{}, error) {
	return s.repoMysql.FetchProductDetail(adminId, outletId, productId, variantId)
}

func (s stockCardRepository) FetchLastOpname(productdetailId string, timemillisMax int64) (int64, error) {
	return s.repoMysql.FetchLastOpname(productdetailId, timemillisMax)
}

func (s stockCardRepository) FetchOpenAfterOpname(productdetailId string, timemillisLastOpname int64, timeStart int64) (float32, error) {
	//return s.repoMysql.FetchOpenAfterOpname(productdetailId, timemillisLastOpname, timeStart)

	data, err := s.FetchTransaction(productdetailId, timemillisLastOpname, timeStart)

	var totalCalculate float32
	for _, item := range data {
		totalCalculate += item.Calculate
	}

	return totalCalculate, err
}

func (s stockCardRepository) FetchProductionPrimary(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchProductionPrimary(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchProductionDetail(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchProductionDetail(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchStockOpname(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchStockOpname(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchRetur(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchRetur(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchPurchaseConfirm(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchPurchaseConfirm(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSales(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSales(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSalesRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSalesRefund(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSalesVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSalesVoid(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchTransferOut(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchTransferOut(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchTransferIn(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchTransferIn(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSpoil(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSpoil(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSalesBreakdown(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	timeMaxSync := repository.GetMaxTimeSync()
	timeStartMysql := cast.ToInt64(0)
	timeEndMysql := cast.ToInt64(0)
	timeStartBQ := cast.ToInt64(0)
	timeEndBQ := cast.ToInt64(0)

	log.Debug("time max sync: %v", timeMaxSync)
	log.Debug("range %v to %v", timeStart, timeEnd)
	//set flag time BQ
	if timeStart <= timeMaxSync {
		timeStartBQ = timeStart
	}
	if timeEnd > timeMaxSync {
		timeEndBQ = timeMaxSync
		if timeStartBQ > 0 {
			timeStart = timeMaxSync + 1
		}
	}

	//set flag time MySQL
	if timeStart > timeMaxSync {
		timeStartMysql = timeStart
	}
	if timeEnd > timeMaxSync {
		timeEndMysql = timeEnd
	}

	// async get data sales breakdown
	chanSalesBreakdownMysql := make(chan []models.StockCardTransactionDBResponse)
	chanSalesBreakdownBigquery := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		var result []models.StockCardTransactionDBResponse
		if timeStartMysql != 0 && timeEndMysql != 0 {
			log.Debug("Get Sales Breakdown MySQL from %v to %v", timeStartMysql, timeEndMysql)
			result, _ = s.repoMysql.FetchSalesBreakdown(productdetailId, timeStartMysql, timeEndMysql)
		}
		chanSalesBreakdownMysql <- result
	}()
	go func() {
		var result []models.StockCardTransactionDBResponse
		var err error
		if timeStartBQ != 0 && timeEndBQ != 0 {
			log.Debug("Get Sales Breakdown BQ from %v to %v", timeStartBQ, timeEndBQ)
			result, err = s.repoBigquery.FetchSalesBreakdown(productdetailId, timeStartBQ, timeEndBQ)
			if err != nil {
				log.Debug("Get Sales Breakdown from BQ failed, try with MySQL")
				result, _ = s.repoMysql.FetchSalesBreakdown(productdetailId, timeStartBQ, timeEndBQ)
			}
		}
		chanSalesBreakdownBigquery <- result
	}()

	// MERGE RESULT
	// Menggunakan goroutine untuk menggabungkan hasil dari semua channel
	channels := []chan []models.StockCardTransactionDBResponse{
		chanSalesBreakdownBigquery,
		chanSalesBreakdownMysql,
	}
	chanMergedResult := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		var resultMergedResult []models.StockCardTransactionDBResponse

		// Loop untuk mengambil hasil dari semua channel
		for _, ch := range channels {
			result := <-ch
			//log.Debug("Channel from %v to %v -> %v", timeStart, timeEnd, result)
			resultMergedResult = append(resultMergedResult, result...)
		}

		// Kirim hasil gabungan ke channel baru
		chanMergedResult <- resultMergedResult
	}()

	// Ambil hasil gabungan dari channel baru
	finalResult := <-chanMergedResult

	// Mengurutkan finalResult berdasarkan kriteria yang diinginkan
	sort.Slice(finalResult, func(i, j int) bool {
		// Urutkan berdasarkan data_created, data_source, dan id
		if finalResult[i].DataCreated != finalResult[j].DataCreated {
			return finalResult[i].DataCreated < finalResult[j].DataCreated
		}
		if finalResult[i].DataSource != finalResult[j].DataSource {
			return finalResult[i].DataSource < finalResult[j].DataSource
		}
		return finalResult[i].ID < finalResult[j].ID
	})

	// Tampilkan hasil yang sudah diurutkan
	//fmt.Println("Sorted Result: ", finalResult)

	return finalResult, nil
}

func (s stockCardRepository) FetchSalesBreakdownVoid(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSalesBreakdownVoid(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchSalesBreakdownRefund(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	return s.repoMysql.FetchSalesBreakdownRefund(productdetailId, timeStart, timeEnd)
}

func (s stockCardRepository) FetchTransaction(productdetailId string, timeStart int64, timeEnd int64) ([]models.StockCardTransactionDBResponse, error) {
	chanProductionPrimary := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchProductionPrimary(productdetailId, timeStart, timeEnd)
		chanProductionPrimary <- result
	}()

	chanProductionDetail := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchProductionDetail(productdetailId, timeStart, timeEnd)
		chanProductionDetail <- result
	}()

	chanStockOpname := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchStockOpname(productdetailId, timeStart, timeEnd)
		chanStockOpname <- result
	}()

	chanRetur := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchRetur(productdetailId, timeStart, timeEnd)
		chanRetur <- result
	}()

	chanPurchaseConfirm := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchPurchaseConfirm(productdetailId, timeStart, timeEnd)
		chanPurchaseConfirm <- result
	}()

	chanSales := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSales(productdetailId, timeStart, timeEnd)
		chanSales <- result
	}()

	chanSalesRefund := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesRefund(productdetailId, timeStart, timeEnd)
		chanSalesRefund <- result
	}()

	chanSalesVoid := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesVoid(productdetailId, timeStart, timeEnd)
		chanSalesVoid <- result
	}()

	chanTransferOut := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchTransferOut(productdetailId, timeStart, timeEnd)
		chanTransferOut <- result
	}()

	chanTransferIn := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchTransferIn(productdetailId, timeStart, timeEnd)
		chanTransferIn <- result
	}()

	chanSpoil := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSpoil(productdetailId, timeStart, timeEnd)
		chanSpoil <- result
	}()

	chanSalesBreakdown := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdown(productdetailId, timeStart, timeEnd)
		chanSalesBreakdown <- result
	}()

	chanSalesBreakdownVoid := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdownVoid(productdetailId, timeStart, timeEnd)
		chanSalesBreakdownVoid <- result
	}()

	chanSalesBreakdownRefund := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		result, _ := s.FetchSalesBreakdownRefund(productdetailId, timeStart, timeEnd)
		chanSalesBreakdownRefund <- result
	}()

	// Menggunakan goroutine untuk menggabungkan hasil dari channel
	// Daftar channel
	channels := []chan []models.StockCardTransactionDBResponse{
		chanProductionPrimary,
		chanProductionDetail,
		chanStockOpname,
		chanRetur,
		chanPurchaseConfirm,
		chanSales,
		chanSalesRefund,
		chanSalesVoid,
		chanTransferOut,
		chanTransferIn,
		chanSpoil,
		chanSalesBreakdown,
		chanSalesBreakdownVoid,
		chanSalesBreakdownRefund,
	}

	// Menggunakan goroutine untuk menggabungkan hasil dari semua channel
	chanMergedResult := make(chan []models.StockCardTransactionDBResponse)
	go func() {
		var resultMergedResult []models.StockCardTransactionDBResponse

		// Loop untuk mengambil hasil dari semua channel
		for _, ch := range channels {
			result := <-ch
			//log.Debug("Channel from %v to %v -> %v", timeStart, timeEnd, result)
			resultMergedResult = append(resultMergedResult, result...)
		}

		// Kirim hasil gabungan ke channel baru
		chanMergedResult <- resultMergedResult
	}()

	// Ambil hasil gabungan dari channel baru
	finalResult := <-chanMergedResult

	// Tampilkan hasil
	//log.Debug("Final Result: %v", finalResult)

	// Mengurutkan finalResult berdasarkan kriteria yang diinginkan
	sort.Slice(finalResult, func(i, j int) bool {
		// Urutkan berdasarkan data_created, data_source, dan id
		if finalResult[i].DataCreated != finalResult[j].DataCreated {
			return finalResult[i].DataCreated < finalResult[j].DataCreated
		}
		if finalResult[i].DataSource != finalResult[j].DataSource {
			return finalResult[i].DataSource < finalResult[j].DataSource
		}
		return finalResult[i].ID < finalResult[j].ID
	})

	// Tampilkan hasil yang sudah diurutkan
	//fmt.Println("Sorted Result: ", finalResult)

	return finalResult, nil
}
