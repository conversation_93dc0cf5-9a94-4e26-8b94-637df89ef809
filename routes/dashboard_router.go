package routes

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-report/controller/dashboard/v1"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
)

func SetDashboardRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.POST("/v0/dashboard/sales_category", v1.SalesByAllCategory_HTTP)
	router.POST("/v0/dashboard/sales_top", v1.SalesTop_HTTP)
	router.POST("/v0/dashboard/sales_top/:limit", v1.SalesTop_HTTP)
	router.POST("/v0/dashboard/sales_analysis", v1.SalesAnalysis_HTTP)
	router.POST("/v0/dashboard/sales_transaction", v1.SalesTransaction_HTTP)
	router.POST("/v0/dashboard/entertain_income", v1.EntertainIncome_HTTP)
	router.POST("/v0/dashboard/all", v1.DashboardDataAll_HTTP)

	router.POST("/v1/dashboard/sales_analysis", auth.ValidateToken(v1.SalesAnalysis_v1))
	router.POST("/v1/dashboard/sales_top/:limit", auth.ValidateToken(v1.SalesTop_v1))
	router.POST("/v1/dashboard/sales_category", auth.ValidateToken(v1.SalesCategory_v1))
	router.POST("/v1/dashboard/entertain_income", auth.ValidateToken(v1.EntertainIncome_v1))
	router.POST("/v1/dashboard/sales_transaction", auth.ValidateToken(v1.SalesTransaction_v1))
	router.POST("/v1/dashboard/cash_flow", auth.ValidateToken(v1.CashFlow_v1))
	router.POST("/v1/dashboard/sales_transaction_info", auth.ValidateToken(v1.SalesTransactionInfo_v1))

	//router.POST("/v1/dashboard/cash_flow/by_sales", auth.ValidateToken(auth.AccessRole(v1.CashFlow_v1,"dashboard","access", "view_cashflow")))
	router.POST("/v1/dashboard/cash_flow/by_shift", auth.ValidateToken(auth.AccessRole(v1.CashFlowByShift_v1, "dashboard", "access", "view_cashflow")))
	//router.POST("/v1/dashboard/entertain_income/by_sales", auth.ValidateToken(auth.AccessRole(v1.EntertainIncome_v1, "dashboard", "access", "view_entertainincome")))
	router.POST("/v1/dashboard/entertain_income/by_shift", auth.ValidateToken(auth.AccessRole(v1.EntertainIncomeByShift_v1, "dashboard", "access", "view_entertainincome")))
	router.POST("/v1/dashboard/sales_analysis/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesAnalysis_v1, "dashboard", "access", "view_sales_analysis")))
	router.POST("/v1/dashboard/sales_analysis/by_shift", auth.ValidateToken(auth.AccessRole(v1.SalesAnalysisByShift_v1, "dashboard", "access", "view_sales_analysis")))
	//router.POST("/v1/dashboard/sales_transaction/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesTransactionInfo_v1, "dashboard", "access", "view_sales_transaction")))
	router.POST("/v1/dashboard/sales_transaction/by_shift", auth.ValidateToken(auth.AccessRole(v1.SalesTransactionByShift_v1, "dashboard", "access", "view_sales_transaction")))
	router.POST("/v1/dashboard/sales_top/:limit/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesTop_v1, "dashboard", "access", "view_sales_top")))
	router.POST("/v1/dashboard/sales_top/:limit/by_shift", auth.ValidateToken(auth.AccessRole(v1.SalesTopByShift_v1, "dashboard", "access", "view_sales_top")))
	router.POST("/v0/dashboard/sales_category/by_sales", auth.ValidateToken(auth.AccessRole(v1.SalesCategory_v1, "dashboard", "access", "view_sales_categories")))
	router.POST("/v1/dashboard/sales_category/by_shift", auth.ValidateToken(auth.AccessRole(v1.SalesCategoryByShift_v1, "dashboard", "access", "view_sales_categories")))
	return router
}
