package array

import (
	"reflect"
	"testing"
)

func TestCopyData(t *testing.T) {
	type args struct {
		data interface{}
	}
	tests := []struct {
		name string
		args args
		want interface{}
	}{
		{"test1", args{data: map[string]interface{}{"a": "1"}}, map[string]interface{}{"a": "1"}},
		{"test1", args{data: map[string]map[string]interface{}{"a": {"b": 2}}}, map[string]map[string]interface{}{"a": {"b": 2}}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := CopyData(tt.args.data); !reflect.DeepEqual(got, tt.want) {
				t.<PERSON>("CopyData() = %v, want %v", got, tt.want)
			}
		})
	}
}
