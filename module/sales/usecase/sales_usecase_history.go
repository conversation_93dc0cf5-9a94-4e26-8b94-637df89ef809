package usecase

import (
	"fmt"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/core/utils/take"
	"gitlab.com/uniqdev/backend/api-report/core/utils/transform"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func (s *salesUseCase) FetchSalesHistoryTransaction(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	repo := s.pickRepo(request.EndDate)
	result := map[string]interface{}{
		"data":     []interface{}{},
		"offsetId": 0,
	}

	data, err := repo.FetchSalesHistoryTransaction(user, request)
	if err != nil || len(data) == 0 {
		return result, nil
	}

	employeeIds := array.GetKeys(array.FlatMapArray(data, "employee_fkid"))
	shiftIds := array.GetKeys(array.FlatMapArray(data, "shift_fkid"))
	promoIds := array.GetKeys(array.FlatMapArray(data, "promotion_fkid"))
	salesIds := array.GetKeys(array.FlatMapArray(data, "sales_id"))
	salesIdsOrder := make([]interface{}, 0)
	salesTagIds := make([]int, 0)

	for _, row := range data {
		if ids := strings.Split(cast.ToString(row["promotion_fkids"]), ","); len(ids) > 0 {
			idsInts := transform.ToIntArray(cast.ToString(row["promotion_fkids"]))
			promoIds = append(promoIds, array.TransformToInterface(idsInts)...)
		}

		if salesId, ok := row["sales_id"].(string); ok {
			if strings.HasPrefix(salesId, "INV-") {
				salesIdsOrder = append(salesIdsOrder, salesId)
			}
		}

		if salesTagId, ok := row["sales_tag_fkid"]; ok {
			salesTagIds = append(salesTagIds, cast.ToInt(salesTagId))
		}
	}

	employees, err := s.repoPrimary.FetchEmployee(employeeIds)
	if err != nil {
		return result, err
	}

	shifts, err := s.repoPrimary.FetchShiftByIds(shiftIds...)
	if err != nil {
		return result, err
	}

	payments, err := s.repoPrimary.FetchSalesPayment(salesIds...)
	if err != nil {
		return result, err
	}

	salesTags, err := s.repoPrimary.FetchSalesTagByIds(salesTagIds)
	log.IfError(err)

	fmt.Println(">> salesIdsOrder", salesIdsOrder)
	salesOrderType, err := s.repoPrimary.FetchOrderTypeOfOrderSales(salesIdsOrder...)
	log.IfError(err)

	salesOrderTypeMap := make(map[string]string)
	if salesOrderType != nil {
		for _, order := range *salesOrderType {
			salesOrderTypeMap[order.OrderSalesId] = order.OrderType
		}
	}

	var promotions []map[string]interface{}
	if len(promoIds) > 0 {
		promotions, err = s.repoPrimary.FetchPromotion(promoIds...)
		if err != nil {
			return result, err
		}
	}

	salesTagMap := make(map[int]string)
	if salesTags != nil {
		for _, salesTag := range *salesTags {
			salesTagMap[salesTag.SalesTagID] = salesTag.Name
		}
	}

	employeeMap := array.FlatMapArray(employees, "employee_id")
	shiftMap := array.FlatMapArray(shifts, "shift_id")
	promoMap := array.FlatMapArray(promotions, "promotion_id")
	salesPaymentMap := array.FlatMapArray(payments, "sales_fkid")
	fmt.Println(len(promotions), len(promoIds), len(payments))
	if len(promotions) <= 3 {
		fmt.Println(promoMap)
	}

	offsetId := 0
	for i, row := range data {
		if timeCreated := cast.ToInt(row["time_created"]); timeCreated > offsetId {
			offsetId = timeCreated
		}

		salesId := cast.ToString(row["sales_id"])
		payment := salesPaymentMap[salesId]
		data[i]["employee"] = employeeMap[cast.ToString(row["employee_fkid"])]["name"]
		data[i]["shift_name"] = shiftMap[cast.ToString(row["shift_fkid"])]["name"]
		data[i]["pembayaran"] = map[string]interface{}{
			cast.ToString(payment["method"]): cast.ToInt(payment["pay"]),
		}

		data[i]["keterangan_discount"] = array.Join(", ", row["keterangan_discountS"], row["keterangan_discountSd"])
		data[i]["total_discount"] = cast.ToInt(row["discount_sales"]) + cast.ToInt(row["discountSd"]) + cast.ToInt(row["discountTax"])
		data[i]["total_voucher"] = cast.ToInt(row["voucherTax"]) + cast.ToInt(row["voucherSales"])
		data[i]["tanggal"] = time.Unix(cast.ToInt64(row["tanggal"])/1000+int64(request.TimeZone), 0).Format("01-02-2006 15:04")

		promotionsInfo := make([]string, 0)
		promotionsInfo = append(promotionsInfo, cast.ToString(promoMap[cast.ToString(row["promotion_fkid"])]["name"]))
		if ids := strings.Split(cast.ToString(row["promotion_fkids"]), ","); len(ids) > 0 {
			idsInts := transform.ToIntArray(cast.ToString(row["promotion_fkids"]))
			for _, id := range idsInts {
				promotionsInfo = append(promotionsInfo, cast.ToString(promoMap[cast.ToString(id)]["name"]))
			}
		}

		if cast.ToString(row["voucher_codes"]) != "" {
			promotionsInfo = append(promotionsInfo, fmt.Sprintf("#VOUCHER: %s", row["voucher_codes"]))
		}

		data[i]["keterangan_promo"] = strings.Join(array.RemoveEmpty(promotionsInfo), ", ")
		data[i]["voucher_code_promo"] = row["voucher_codes"]

		//sales tag / order type
		if salesTagId := cast.ToInt(row["sales_tag_fkid"]); salesTagId > 0 {
			data[i]["order_type"] = salesTagMap[salesTagId]
		}

		//if not set, get from order type (if exist)
		if orderType, ok := data[i]["order_type"]; !ok || orderType == nil || orderType == "" {
			log.Info(">>> order_type: '%v'", data[i]["order_type"])
			data[i]["order_type"] = strings.Replace(salesOrderTypeMap[salesId], "_", " ", -1)
		}
	}

	result["data"] = data
	result["offsetId"] = offsetId

	return result, nil
}

func (s *salesUseCase) FetchSalesHistoryTransactionV2(user domain.User, request domain.SalesReportRequest) (map[string]interface{}, error) {
	repo := s.repoPrimary // s.pickRepo(request.EndDate)
	result := map[string]interface{}{
		"data":     []interface{}{},
		"offsetId": 0,
	}

	request.EndDate = take.Smaller(utils.CurrentMillis(), request.EndDate)
	sales, err := repo.FetchSalesHistory(user, request)
	if log.IfError(err) || len(sales) == 0 {
		return result, err
	}

	salesIds := make([]interface{}, 0)
	employeeIdMap := make(map[interface{}]bool)
	openShiftIdMap := make(map[interface{}]bool)
	salesTagIdMap := make(map[int]bool)
	for _, sale := range sales {
		salesIds = append(salesIds, sale["sales_id"])
		employeeIdMap[sale["employee_fkid"]] = true
		openShiftIdMap[sale["open_shift_fkid"]] = true
		salesTagIdMap[cast.ToInt(sale["sales_tag_fkid"])] = true
	}

	openShiftIds := array.GetKeys(openShiftIdMap)
	employeeIds := array.GetKeys(employeeIdMap)
	salesTagIds := array.MapKey(salesTagIdMap)

	salesDetailPromoChan := make(chan []map[string]interface{})
	salesDetailDiscChan := make(chan []map[string]interface{})
	salesPromotionChan := make(chan []map[string]interface{})
	salesTaxChan := make(chan []map[string]interface{})
	salesVoidChan := make(chan []map[string]interface{})
	shiftChan := make(chan []map[string]interface{})
	employeeChan := make(chan []map[string]interface{})
	salesTagChan := make(chan []models.SalesTagEntity)
	paymentChan := make(chan []map[string]interface{})
	salesDetailChan := make(chan []map[string]interface{})
	salesRefundChan := make(chan []map[string]interface{})

	//fetch sales refunds
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSalesRefunds(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesRefundChan)

	//fetch  sales_detail_promotion by salesIds - unique: promotion_fkid, sum: promotion_value
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetailPromotion(domain.SalesId, ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesDetailPromoChan)

	//fetch sales_detail_discount by salesIds, and type is discount - sum total
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetailDiscount(domain.SalesId, []string{"discount"}, ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesDetailDiscChan)

	//fetch sales_promotion by salesIds - unique: voucher_code, sum: promotion_value
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesPromotion(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesPromotionChan)

	//fetch sales_tax by salesIds (per category) - sum total
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesTaxPerCategory(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesTaxChan)

	//fetch shift by openShiftId
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchShiftByOpenShift(ids...)
		log.IfError(err)
		resultChan <- result
	}(openShiftIds, shiftChan)

	//fetch salesVoid by salesIds
	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesVoid(domain.SalesId, ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesVoidChan)

	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := s.repoPrimary.FetchEmployee(ids...)
		log.IfError(err)
		resultChan <- result
	}(employeeIds, employeeChan)

	go func(ids []int, resultChan chan []models.SalesTagEntity) {
		result, err := s.repoPrimary.FetchSalesTagByIds(ids)
		log.IfError(err)
		resultChan <- *result
	}(salesTagIds, salesTagChan)

	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := s.repoPrimary.FetchSalesPayment(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, paymentChan)

	go func(ids []interface{}, resultChan chan []map[string]interface{}) {
		result, err := repo.FetchSumSalesDetail(ids...)
		log.IfError(err)
		resultChan <- result
	}(salesIds, salesDetailChan)

	salesDetailPromo := <-salesDetailPromoChan
	salesDetailDisc := <-salesDetailDiscChan
	salesPromotion := <-salesPromotionChan
	salesTax := <-salesTaxChan
	salesVoid := <-salesVoidChan
	shifts := <-shiftChan
	employees := <-employeeChan
	salesTags := <-salesTagChan
	payments := <-paymentChan
	salesDetail := <-salesDetailChan
	salesRefunds := <-salesRefundChan

	promoIds := make([]interface{}, 0)
	voucherCodesMap := make(map[string]string)
	for _, row := range salesPromotion {
		if ids := (cast.ToString(row["promotion_fkids"])); len(ids) > 0 {
			idsInts := transform.ToIntArray(ids)
			promoIds = append(promoIds, array.TransformToInterface(idsInts)...)
		}
		voucherCodesMap[cast.ToString(row["sales_fkid"])] = cast.ToString(row["voucher_codes"])
	}
	for _, row := range salesDetailPromo {
		if ids := (cast.ToString(row["promotion_fkids"])); len(ids) > 0 {
			idsInts := transform.ToIntArray(ids)
			promoIds = append(promoIds, array.TransformToInterface(idsInts)...)
		}
	}

	var promotions []map[string]interface{}
	if len(promoIds) > 0 {
		promotions, err = s.repoPrimary.FetchPromotion(promoIds...)
		if log.IfError(err) {
			return result, err
		}
	}

	salesTagMap := make(map[int]string)
	for _, salesTag := range salesTags {
		salesTagMap[salesTag.SalesTagID] = salesTag.Name
	}

	employeeMap := array.FlatMapArray(employees, "employee_id")
	salesPaymentMap := array.GroupBy(payments, "sales_fkid")
	shiftMap := array.FlatMapArray(shifts, "open_shift_id")
	promoMap := array.FlatMapArray(promotions, "promotion_id")
	salesTaxMap := array.FlatMapArray(salesTax, "sales_fkid")
	salesVoidMap := array.FlatMapArray(salesVoid, "sales_fkid")
	salesDetailDiscMap := array.FlatMapArray(salesDetailDisc, "sales_fkid")
	salesPromotionMap := array.FlatMapArray(salesPromotion, "sales_fkid")
	salesDetailPromotionMap := array.FlatMapArray(salesDetailPromo, "sales_fkid")
	salesDetailMap := array.FlatMapArray(salesDetail, "sales_fkid")
	salesRefundMap := array.FlatMapArray(salesRefunds, "sales_fkid")

	log.Info("total employee: %v, ids: %v", len(employeeMap), len(employeeIds))

	offsetId := int64(0)
	for i, row := range sales {
		if timeCreated := cast.ToInt64(row["time_created"]); timeCreated > offsetId {
			offsetId = timeCreated
		}

		salesId := cast.ToString(row["sales_id"])
		payment := salesPaymentMap[salesId]
		tax := salesTaxMap[salesId]
		salesPromo := salesPromotionMap[salesId]
		detail := salesDetailMap[salesId]
		employeeId := cast.ToString(cast.ToInt(row["employee_fkid"]))

		if _, ok := employeeMap[employeeId]; !ok {
			log.IfError(fmt.Errorf("can not get employee, id: %v (%v) | row: %v\n", row["employee_fkid"], employeeId, utils.SimplyToJson(row)))
		}

		sales[i]["employee"] = employeeMap[employeeId]["name"]
		sales[i]["shift_name"] = shiftMap[cast.ToString(row["open_shift_fkid"])]["name"]

		paymentPerMethod := make(map[string]int)
		for _, payment := range payment {
			paymentPerMethod[cast.ToString(payment["method"])] = cast.ToInt(payment["total"])
		}
		sales[i]["pembayaran"] = paymentPerMethod
		sales[i]["keterangan_discount"] = array.Join(", ", row["keterangan_discountS"], detail["discount_info"])
		sales[i]["total_discount"] = cast.ToInt(salesDetailDiscMap[salesId]["total"]) + cast.ToInt(detail["discount"]) + cast.ToInt(tax["discount"])
		sales[i]["total_voucher"] = cast.ToInt(tax["voucher"]) + cast.ToInt(row["voucherSales"])
		sales[i]["tanggal"] = time.Unix(cast.ToInt64(row["time_created"])/1000+int64(request.TimeZone), 0).Format("01-02-2006 15:04")
		sales[i]["service"] = tax["service"]
		sales[i]["tax"] = tax["tax"]
		sales[i]["sub_void"] = salesVoidMap[salesId]
		sales[i]["promo"] = cast.ToInt(salesPromo["total"]) + cast.ToInt(salesDetailPromotionMap[salesId]["total"])
		sales[i]["sub_total"] = detail["sub_total"]

		if refund := salesRefundMap[salesId]; refund != nil {
			refundEmployeeId := cast.ToString(refund["employee_fkid"])
			refund["employee"] = employeeMap[refundEmployeeId]["name"]
			refund["date"] = time.Unix(cast.ToInt64(refund["time_created"])/1000+int64(request.TimeZone), 0).Format("01-02-2006 15:04")
			sales[i]["refund"] = refund
		}

		promotionsInfo := make([]string, 0)
		promotionsInfo = append(promotionsInfo, cast.ToString(promoMap[cast.ToString(salesPromo["promotion_fkids"])]["name"]))
		if ids := strings.Split(cast.ToString(salesPromo["promotion_fkids"]), ","); len(ids) > 0 {
			idsInts := transform.ToIntArray(cast.ToString(salesPromo["promotion_fkids"]))
			for _, id := range idsInts {
				promotionsInfo = append(promotionsInfo, cast.ToString(promoMap[cast.ToString(id)]["name"]))
			}
		}

		if voucher := voucherCodesMap[salesId]; len(voucher) > 0 {
			promotionsInfo = append(promotionsInfo, fmt.Sprintf("#VOUCHER: %s", voucher))
		}

		sales[i]["keterangan_promo"] = strings.Join(array.RemoveEmpty(promotionsInfo), ", ")
		sales[i]["voucher_code_promo"] = row["voucher_codes"]

		//sales tag / order type
		if salesTagId := cast.ToInt(row["sales_tag_fkid"]); salesTagId > 0 {
			sales[i]["order_type"] = salesTagMap[salesTagId]
		}
	}

	result["data"] = sales
	result["offsetId"] = offsetId
	return result, nil
}
