package routes

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-report/controller"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
)

func SetWelcomeRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/auth/welcome", auth.ValidateToken(controller.WelcomePage))
	router.GET("/welcome", controller.WelcomePage)
	router.POST("/product", auth.<PERSON>idate<PERSON>oken(controller.AddProduct))
	router.PUT("/product", auth.<PERSON>idate<PERSON>oken(controller.UpdateProduct))
	return router
}
