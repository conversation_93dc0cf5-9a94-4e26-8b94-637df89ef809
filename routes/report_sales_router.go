package routes

import (
	"github.com/buaazp/fasthttprouter"
	"gitlab.com/uniqdev/backend/api-report/controller"
	v1 "gitlab.com/uniqdev/backend/api-report/controller/sales_commission"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
)

func SetReportSalesRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	router.GET("/sales_analysis/nominal/:outletId/:shiftId/:date/:dateAdd/:length/:start", controller.GetSalesAnalysisByNominal)
	router.GET("/sales_analysis/nominal/:outletId/:shiftId/:date/:dateAdd", controller.GetSalesAnalysisByNominalV2)

	router.GET("/v1/sales_commission/datatables", v1.SalesCommissionDatatable)
	router.POST("/v1/sales_commission/datatables", auth.ValidateToken(v1.SalesCommissionDatatable))
	router.GET("/v1/sales-commission", auth.ValidateToken(v1.SalesCommission))
	return router
}
