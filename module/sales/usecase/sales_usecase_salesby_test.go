package usecase

import (
	"reflect"
	"testing"

	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

func TestTransformData(t *testing.T) {

	// Sample input data
	inputData := []models.SalesTagByOutlet{
		{

			Total:        4000,
			OutletName:   "Best UNIQ Resto",
			SalesTagFkid: 2,
			OutletFkid:   29,
		},
		{

			Total:        1000,
			OutletName:   "Branch Solo",
			SalesTagFkid: 2,
			OutletFkid:   31,
		},
		{
			Total:        6000,
			OutletName:   "Best UNIQ Resto",
			SalesTagFkid: 3,
			OutletFkid:   29,
		},
		{
			Total:        3000,
			OutletName:   "Branch Timoho",
			SalesTagFkid: 2,
			OutletFkid:   30,
		},
		{
			Total:        4000,
			OutletName:   "Branch Timoho",
			SalesTagFkid: 3,
			OutletFkid:   30,
		},
	}

	// Expected output data
	expectedOutput := []models.SalesByOrderTypeResponse{
		{
			Name:  "Dine In",
			Total: 8000,
			// Outlet: []struct {
			// 	Name  string `json:"name,omitempty"`
			// 	Total int    `json:"total,omitempty"`
			// }{
			// 	{
			// 		Name:  "Best UNIQ Resto",
			// 		Total: 4000,
			// 	},
			// 	{
			// 		Name:  "Branch Solo",
			// 		Total: 1000,
			// 	},
			// 	{
			// 		Name:  "Branch Timoho",
			// 		Total: 3000,
			// 	},
			// },
		},
		{
			Name:  "Take Away",
			Total: 10000,
			// Outlet: []struct {
			// 	Name  string `json:"name,omitempty"`
			// 	Total int    `json:"total,omitempty"`
			// }{
			// 	{
			// 		Name:  "Best UNIQ Resto",
			// 		Total: 6000,
			// 	},
			// 	{
			// 		Name:  "Branch Timoho",
			// 		Total: 4000,
			// 	},
			// },
		},
	}

	// Sample sales tag data
	salesTagData := []models.SalesTagEntity{
		{
			SalesTagID: 2,
			Name:       "Dine In",
			AdminFKID:  1,
		},
		{
			SalesTagID: 3,
			Name:       "Take Away",
			AdminFKID:  1,
		},
	}

	// Call the function
	output := transformData(&inputData, &salesTagData)

	// Compare the output with the expected output
	if !reflect.DeepEqual(output, expectedOutput) {
		t.Errorf("TransformData: Expected %v, \nbut got %v", utils.SimplyToJson(expectedOutput), utils.SimplyToJson(output))
	}
}
