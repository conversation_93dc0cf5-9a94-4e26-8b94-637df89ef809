package domain

import "gitlab.com/uniqdev/backend/api-report/models"

type CloseShift struct{}

type CloseShiftUseCase interface {
	FetchCloseShiftRecap(request SalesReportRequest, user User) (models.CloseShiftResponse, error)
}

type CloseShiftRepository interface {
	FetchSalesRecapShift(request SalesReportRequest, user User) (models.SalesRecapShift, error)
	FetchSalesDetailShift(request SalesReportRequest, user User) (models.SalesDetailShift, error)
	FetchPayShift(request SalesReportRequest, user User) (models.PayShift, error)
	FetchTaxShift(request SalesReportRequest, user User) (models.TaxShift, error)
	FetchVoidShiftRecap(request SalesReportRequest, user User) (models.VoidShiftRecap, error)
	FetchRefundShiftRecap(request SalesReportRequest, user User) (models.RefundShiftRecap, error)
	FetchPromoRecapProduk(request SalesReportRequest, user User) (models.PromoRecapTotal, error)
	FetchPromoRecapShift(request SalesReportRequest, user User) (models.PromoRecapTotal, error)
}

type CloseShiftRepositoryReplica interface {
	CloseShiftRepository
}
