package repository

import (
	"database/sql"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
	bqCrm "gitlab.com/uniqdev/backend/api-report/module/crm/repository/bigquery"
	"gitlab.com/uniqdev/backend/api-report/module/crm/repository/mysql"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type crmRepository struct {
	repoPrimary crm.Repository
	repoReplica crm.Repository
}

func NewMiddlewareCrmRepository(db *sql.DB, client *bigquery.Client) crm.Repository {
	return &crmRepository{repoPrimary: mysql.NewMysqlCrmRepository(db), repoReplica: bqCrm.NewBigQueryCrmRepository(client)}
}

func (c crmRepository) QueryParam(param domain.RequestParam) crm.Repository {
	if repository.GetMaxTimeSync() > 0 {
		return c.repoReplica.QueryParam(param)
	}
	return c.repoPrimary.QueryParam(param)
}

func (c crmRepository) FetchCustomerList(user domain.User) ([]map[string]interface{}, error) {
	if repository.GetMaxTimeSync() > 0 {
		return c.repoReplica.FetchCustomerList(user)
	}
	return c.repoPrimary.FetchCustomerList(user)
}

func (c crmRepository) FetchCustomerTransactionList(user domain.User) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmRepository) FetchSalesMember(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmRepository) FetchMemberRegisterRange(adminId int, param domain.RequestParam) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}
