package usecase

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	"github.com/stretchr/testify/assert"
)

func setupTestData() (ProductionData, error) {
	var err error
	var testData ProductionData

	jsonProduction := `{"production_id":132,"itembreakdown_fkid":85,"product_detail_fkid":37182,"outlet_fkid":628,"qty_recipe":0,"qty_primary":2,"employee_fkid":null,"admin_fkid":7,"date_input":"2023-07-04","data_created":1688476659956,"data_modified":1688476659956,"data_delete_at":null,"transferred":0}`
	err = json.Unmarshal([]byte(jsonProduction), &testData.Production)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProduction: %v", err)
	}

	jsonProductionCost := `[{"id":19,"production_fkid":132,"purchase_report_category_fkid":238,"nominal":5000}]`
	err = json.Unmarshal([]byte(jsonProductionCost), &testData.ProductionCost)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionCost: %v", err)
	}

	jsonProductionDetail := `[{"productiondetail_id":579,"production_fkid":132,"product_fkid":9762,"product_detail_fkid":37180,"qty":4,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":580,"production_fkid":132,"product_fkid":9765,"product_detail_fkid":37186,"qty":20,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":581,"production_fkid":132,"product_fkid":9766,"product_detail_fkid":37188,"qty":20,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":582,"production_fkid":132,"product_fkid":9767,"product_detail_fkid":37190,"qty":16,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":583,"production_fkid":132,"product_fkid":9769,"product_detail_fkid":37194,"qty":300,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":584,"production_fkid":132,"product_fkid":9770,"product_detail_fkid":37196,"qty":500,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":585,"production_fkid":132,"product_fkid":9768,"product_detail_fkid":37192,"qty":300,"detail_type":"ingredient","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":586,"production_fkid":132,"product_fkid":9761,"product_detail_fkid":37179,"qty":3,"detail_type":"endproduct","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":587,"production_fkid":132,"product_fkid":9762,"product_detail_fkid":37180,"qty":0.06,"detail_type":"residual","data_created":1688476659969,"data_modified":1688476659969},{"productiondetail_id":588,"production_fkid":132,"product_fkid":9763,"product_detail_fkid":37182,"qty":0.06,"detail_type":"residual","data_created":1688476659969,"data_modified":1688476659969}]`
	err = json.Unmarshal([]byte(jsonProductionDetail), &testData.ProductionDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductionDetail: %v", err)
	}

	jsonProductDetail := `[{"product_detail_id":37180,"product_fkid":9762,"outlet_fkid":628,"price_buy_start":13000,"price_buy":13000,"price_sell":15000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":2,"data_status":"on"},{"product_detail_id":37186,"product_fkid":9765,"outlet_fkid":628,"price_buy_start":150,"price_buy":150,"price_sell":200,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":10,"data_status":"on"},{"product_detail_id":37188,"product_fkid":9766,"outlet_fkid":628,"price_buy_start":250,"price_buy":250,"price_sell":300,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":10,"data_status":"on"},{"product_detail_id":37190,"product_fkid":9767,"outlet_fkid":628,"price_buy_start":350,"price_buy":350,"price_sell":450,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":18,"data_status":"on"},{"product_detail_id":37194,"product_fkid":9769,"outlet_fkid":628,"price_buy_start":87000,"price_buy":87,"price_sell":90000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":4200,"data_status":"on"},{"product_detail_id":37196,"product_fkid":9770,"outlet_fkid":628,"price_buy_start":35000,"price_buy":35,"price_sell":40000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":4000,"data_status":"on"},{"product_detail_id":37192,"product_fkid":9768,"outlet_fkid":628,"price_buy_start":85000,"price_buy":85,"price_sell":90000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":4400,"data_status":"on"},{"product_detail_id":37179,"product_fkid":9761,"outlet_fkid":628,"price_buy_start":0,"price_buy":0,"price_sell":650000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":7,"data_status":"on"},{"product_detail_id":37182,"product_fkid":9763,"outlet_fkid":628,"price_buy_start":25000,"price_buy":25000,"price_sell":28000,"voucher":"off","discount":"off","active":"on_all","transfer_markup_type":"percent","transfer_markup":0,"commission_staff_type":"percent","commission_staff":0,"commission_customer_type":"percent","commission_customer":0,"data_modified":1688482259000,"variant_fkid":null,"stock":"available","stock_qty":6,"data_status":"on"}]`
	err = json.Unmarshal([]byte(jsonProductDetail), &testData.ProductDetail)
	if err != nil {
		return testData, fmt.Errorf("failed to unmarshal jsonProductDetail: %v", err)
	}

	return testData, err
}

func Test_calculatePriceBuy(t *testing.T) {
	data, err := setupTestData()
	assert.NoError(t, err)

	jsonResultOneEndProduct := `[{"ProductDetailId":37179,"PriceBuy":187420}]`
	var resultOneEndProduct []ProductionPriceBuy
	json.Unmarshal([]byte(jsonResultOneEndProduct), &resultOneEndProduct)

	type args struct {
		data ProductionData
	}
	tests := []struct {
		name string
		args args
		want []ProductionPriceBuy
	}{
		{"test1", args{data}, resultOneEndProduct},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := calculatePriceBuy(tt.args.data); !reflect.DeepEqual(got, tt.want) {
				priceJson, _ := json.Marshal(got)
				fmt.Println(">>>>", string(priceJson))
				t.Errorf("calculatePriceBuy() = %v, want %v", got, tt.want)
			}
		})
	}
}
