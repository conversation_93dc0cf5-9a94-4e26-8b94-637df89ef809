package cast

import (
	"bufio"
	"encoding/base64"
	"io/ioutil"
	"os"
)

func ToBase64(filePath string) (string, error) {
	f, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	// Read entire JPG into byte slice.
	reader := bufio.NewReader(f)
	content, err := ioutil.ReadAll(reader)
	if err != nil {
		return "", err
	}

	// Encode as base64.
	return base64.StdEncoding.EncodeToString(content), nil
}
