package domain

import "fmt"

const (
	RequestFormatDatatable = "datatable"
	RequestFormatJson      = "json"
)

type RequestParamFilter struct {
	OutletId  []int
	DateStart int64
	DateEnd   int64
	Period    string //daily, monthly, weekly,
	GroupBy   string
}

type RequestParam struct {
	RequestParamFilter `json:"request_param_filter,omitempty"`
	Format             string       `json:"format,omitempty"` //json, datatable
	Search             string       `json:"search,omitempty"`
	Start              int          `json:"start,omitempty"`
	Length             int          `json:"length,omitempty"`
	Order              RequestOrder `json:"order,omitempty"`
	Offset             int          `json:"offset,omitempty"`
	IsCount            bool         `json:"is_count,omitempty"`
}

type RequestOrder struct {
	Column    string `json:"column,omitempty"`
	Direction string `json:"direction,omitempty"` //ASC or DESC
}

func (r RequestParam) HasFilter() bool {
	//return len(r.OutletId) > 0 || (r.DateEnd > 0 && r.DateStart > 0) || r.Search != ""
	return r.Search != ""
}

func (r RequestParam) ResetSearch() RequestParam {
	r.Search = ""
	return r
}

func (r RequestParam) LimitQuery() string {
	if r.Length == 0 {
		return ""
	}
	return fmt.Sprintf(" LIMIT %d OFFSET %v ", r.Length, r.Start)
}
