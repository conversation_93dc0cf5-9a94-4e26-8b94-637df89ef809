package http

import (
	"encoding/json"
	"os"

	"github.com/buaazp/fasthttprouter"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	"gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
)

type hppHandler struct {
	uc hpp.UseCase
}

var fakeAdminId = setFakeAdmin()

func setFakeAdmin() int {
	if os.Getenv("ENV") == "localhost" {
		return cast.ToInt(os.Getenv("FAKE_ADMIN"))
	}
	return 7
}

func NewHttpHppHandler(app *fasthttprouter.Router, useCase hpp.UseCase) {
	handler := &hppHandler{useCase}
	app.POST("/v1/simulation/fifo", ensureTesting(handler.SimulateFifoAdjusment))
	app.GET("/v1/simulation/fifo", ensureTesting(handler.FetchFifo))

	app.PUT("/v1/simulation/fifo", ensureTesting(handler.UpdateFifo))
}

func (h *hppHandler) SimulateFifoAdjusment(ctx *fasthttp.RequestCtx) {
	var hppInput []domain.Hpp
	err := json.Unmarshal(ctx.PostBody(), &hppInput)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusBadRequest)
		return
	}

	ctx.SetContentType("application/json")
	hppResult, err := h.uc.SimulateFifoAdjusment(hppInput)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}

	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"data":    hppResult.UpdateHpp,
		"changes": hppResult.Changes,
	})
}

func (h *hppHandler) FetchFifo(ctx *fasthttp.RequestCtx) {
	param := domain.StockPriceRequest{
		ProductName: string(ctx.QueryArgs().Peek("product")),
		OutletName:  string(ctx.QueryArgs().Peek("outlet")),
		AdminId:     fakeAdminId,
		StartDate:   cast.ToInt64(ctx.QueryArgs().Peek("start_date")),
	}
	hppResult, err := h.uc.FetchStockPrice(&param)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}
	ctx.SetContentType("application/json")

	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"data": hppResult,
	})
}

func (h *hppHandler) UpdateFifo(ctx *fasthttp.RequestCtx) {
	param := domain.StockPriceUpdateRequest{
		StockInSrouce: string(ctx.FormValue("stock_in_source")),
		StockInId:     string(ctx.FormValue("stock_in_id")),
		Qty:           cast.ToFloat32(ctx.FormValue("qty")),
		AdminId:       fakeAdminId,
	}
	log.Info("update fifo: %v", utils.SimplyToJson(param))
	err := h.uc.UpdateStockPrice(&param)
	if log.IfError(err) {
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return
	}
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(map[string]interface{}{
		"status": "success",
	})
}

func ensureTesting(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		if os.Getenv("ENV") == "localhost" || os.Getenv("ENV") == "development" {
			next(ctx)
			return
		}
		ctx.SetStatusCode(fasthttp.StatusNotFound)
	}
}
