package domain

type Dashboard struct{}

type DashboardRequest struct {
	TimeStart int64    `json:"time_start,omitempty"`
	TimeEnd   int64    `json:"time_end" validate:"required,gte=0" label:"Time End"`
	TimeDiff  int64    `json:"time_diff" validate:"required,gte=0" label:"Time Diff"`
	OutletIds []string `json:"outlet_ids" validate:"" label:"Outlets"`
}

type TopSalesResponse struct {
	ProductFkid   int `json:"product_fkid"`
	TotalQty      int `json:"total_qty"`
	TotalSubtotal int `json:"total_subtotal"`
}

type DashboardUseCase interface {
	FetchCashFlow(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error)
	FetchEntertainIncome(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error)
	FetchSalesTransaction(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error)
	FetchSalesTransactionV2(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) ([]map[string]interface{}, error)

	FetchItemSalesGroup(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, dateOption string) (map[string]interface{}, error)
	FetchSalesByMedia(param DashboardRequest, user User) ([]map[string]interface{}, error)
	FetchSalesAnalysis(param DashboardRequest, user User) ([]map[string]interface{}, error)
	FetchTopSales(param DashboardRequest, limit int, user User) (map[string]interface{}, error)
}

type DashboardRepository interface {
	FetchNetSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchNetSalesByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchPaidPurchaseBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchUnPaidPurchase(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDebtPayment(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDebt(adminId string, timeStart int, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchComplimentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDutyMealsBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchFreeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDiscSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDiscItemBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchDiscGratuityBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchVoucherBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchVoucherSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchTotalSalesPerPaymentBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, paymentMethods ...string) ([]map[string]interface{}, error)
	FetchPendingIncomeBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchTotalSalesBySales(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchItemSalesGroupSubCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchItemSalesGroupCategory(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchItemSalesGroupType(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}) ([]map[string]interface{}, error)
	FetchSalesByMedia(param DashboardRequest, user User) ([]map[string]interface{}, error)
	FetchBankById(id ...interface{}) ([]map[string]interface{}, error)
	FetchTopSales(param DashboardRequest, user User) ([]map[string]interface{}, error)
}
