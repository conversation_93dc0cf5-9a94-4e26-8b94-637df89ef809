package mysql

import (
	"database/sql"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gitlab.com/uniqdev/backend/api-report/module/purchase"
)

type purchaseRepository struct {
	mysql.Repository
}

func NewMysqlPurchaseRepository(db *sql.DB) purchase.Repository {
	return &purchaseRepository{mysql.Repository{Conn: db}}
}

func (p purchaseRepository) FetchProduction(productionId int) (models.ProductionEntity, error) {
	sql := `SELECT * from production where production_id=?`
	var result models.ProductionEntity
	err := p.Query(sql, productionId).Model(&result)
	return result, err
}

func (p purchaseRepository) FetchProductionDetail(productionId int) ([]models.ProductionDetailEntity, error) {
	sql := `SELECT * from production_detail where production_fkid=?`
	var result []models.ProductionDetailEntity
	err := p.Query(sql, productionId).Model(&result)
	return result, err
}

func (p purchaseRepository) FetchProductionCost(productionId int) ([]models.ProductionCostEntity, error) {
	sql := `SELECT * from production_detail_cost where production_fkid=?`
	var result []models.ProductionCostEntity
	err := p.Query(sql, productionId).Model(&result)
	return result, err
}

func (p purchaseRepository) UpdateProductionDetail(data models.ProductionDetailEntity) error {
	whereSql := "production_fkid = ? and product_detail_fkid = ? "
	params := make([]interface{}, 0)

	params = append(params, data.ProductionFKID, data.ProductDetailFKID)
	if data.DetailType != "" {
		whereSql += " and detail_type = ? "
		params = append(params, data.DetailType)
	}

	_, err := db.Update("production_detail", map[string]interface{}{
		"price_buy": data.PriceBuy,
	}, whereSql, params...)
	return err
}
