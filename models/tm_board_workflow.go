package models

type BoardWorkflowEntity struct {
	ID                         int   `json:"id,omitempty"`
	TmBoardsID                 int   `json:"tm_boards_id,omitempty"`
	TmMasterCategoryWorkflowID int   `json:"tm_master_category_workflow_id,omitempty"`
	CreatedAt                  int64 `json:"created_at,omitempty"`
}

type MasterWorkflowEntity struct {
	ID                 int    `json:"id,omitempty"`
	TmMasterCategoryID int    `json:"tm_master_category_id,omitempty"`
	Name               string `json:"name,omitempty"`
	Type               string `json:"type,omitempty"`
	Position           int    `json:"position,omitempty"`
	UpdatedAt          int64  `json:"updated_at,omitempty"`
}
