package models

type StockOutProduct struct {
	ProductDetailId int
	Qty             float32
	MetaData        map[string]interface{}
	CreatedAt       int64
}

type StockOutProductPrice struct {
	StockOutProduct
	Price float32
}

type StockInProduct struct {
	ProductDetailId int
	Qty             float32
	Price           float32
	MetaData        map[string]interface{}
	CreatedAt       int64
}

type PriceBuyUpdate struct {
	Id            int     //can be sales_detail_id or sales_breakdown_id
	PriceBuy      float32 //price for each item
	PriceBuyTotal float32 //total price for one row of data
}
