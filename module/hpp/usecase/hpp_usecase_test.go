package usecase

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"
	"time"

	"github.com/joho/godotenv"
	"github.com/stretchr/testify/mock"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
	"gitlab.com/uniqdev/backend/api-report/module/hpp/mocks"
	"gitlab.com/uniqdev/backend/api-report/module/hpp/repository/mysql"
	"gitlab.com/uniqdev/backend/api-report/module/hpp/usecase/testdata"
	"gitlab.com/uniqdev/backend/api-report/module/purchase"
)

func Test_basic(t *testing.T) {
	hppMap := make(map[string][]int)
	hppMap["one"] = make([]int, 0)
	hppMap["one"] = append(hppMap["one"], 4, 5, 6)

	x := hppMap["one"]
	for i, _ := range x {
		x[i] = x[i] * 10
	}
	fmt.Println("data: ", utils.SimplyToJson(hppMap))
}

func Test_getHpp(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-report/.env")
	envPath, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(filepath.Join(envPath, ".env"))
	dbConn := db.GetConn()
	if dbConn == nil {
		t.Fatal("database not initialized...")
	}
	hppRepo := mysql.NewMysqlHppRepository(dbConn)
	result, err := hppRepo.FetchStockOutHpp(39409)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println(">> result :", utils.SimplyToJson(result))
}

func jsonToHpp(jsonStr string) []domain.Hpp {
	var result []domain.Hpp
	json.Unmarshal([]byte(jsonStr), &result)
	return result
}

func jsonToStockPriceDetail(jsonStr string, t *testing.T) []models.StockPriceDetailEntity {
	var result []models.StockPriceDetailEntity
	err := json.Unmarshal([]byte(jsonStr), &result)
	if err != nil {
		t.Error(err)
	}
	return result
}

func jsonToChanges(jsonStr string) map[int][]models.HppUpdate {
	var changes map[int][]models.HppUpdate
	err := json.Unmarshal([]byte(jsonStr), &changes)
	if err != nil {
		fmt.Println("--- jsonToChanges err", err)
		return nil
	}
	return changes
}

//updates:
// {
// 	"1": [
// 	  {
// 		"StockPriceId": 2,
// 		"Qty": 6,
// 		"Price": 80000
// 	  }
// 	 ]
//  }
//   this translate:
//   - ambil stock_price_detail dengan id 2, sejumlah 6 item (order desc), update stock_price_fkid ke 1

func Test_recalculateHpp(t *testing.T) {
	os.Setenv("ENV", "localhost")
	log.SetCurrentLevel()
	type args struct {
		hpps []domain.Hpp
	}
	type Test struct {
		name        string
		args        args
		want        []domain.Hpp
		wantChanges map[int][]models.HppUpdate
	}

	testCases := make([]Test, 0)
	for id, data := range testdata.DataRecalculateHpp() {
		testCases = append(testCases, Test{
			name: id,
			args: args{
				hpps: jsonToHpp(data["original"]),
			},
			want:        jsonToHpp(data["expected"]),
			wantChanges: jsonToChanges(data["changes"]),
		})
	}

	// Validate the test cases
	for _, testCase := range testCases {
		originStockOutTotal := sumStockOut(testCase.args.hpps)
		expectedStockOutTotal := sumStockOut(testCase.want)
		if originStockOutTotal != expectedStockOutTotal {
			t.Errorf("Total sum of stockOut origin vs total sum stockOut expected are not the same for test case %s", testCase.name)
		}
	}

	for _, tt := range testCases {
		t.Run(tt.name, func(t *testing.T) {
			fmt.Println("start recalculateHpp....")
			gotHpp, gotChanges := recalculateHpp(tt.args.hpps)
			if !reflect.DeepEqual(gotHpp, tt.want) {
				// for i, h := range gotHpp {
				// 	fmt.Printf("%v \t| %v  <> %v | %v\n", h.StockIn, h.StockOut, tt.want[i].StockIn, tt.want[i].StockOut)
				// }

				t.Errorf("recalculateHpp() gotHpp = %v, \nwant %v", toJson(gotHpp), toJson(tt.want))
			}
			if !reflect.DeepEqual(gotChanges, tt.wantChanges) {
				t.Errorf("recalculateHpp() gotChanges = %v, \nwant %v", toJson(gotChanges), toJson(tt.wantChanges))
			}
		})
	}
}

func toJson(data interface{}) string {
	res, _ := json.Marshal(data)
	return string(res)
}

func Test_hppUseCase_logStockIn_noPendingStock(t *testing.T) {
	repoMock := mocks.NewRepository(t)
	newStockIn := []models.StockInProduct{{ProductDetailId: 1, Qty: 10, Price: 500}}

	type args struct {
		dataDetail *[]models.StockInProduct
		dataType   string
		dataId     string
	}

	arg := args{dataDetail: &newStockIn, dataType: domain.PurchaseConfirm.String(), dataId: "1"}

	repoMock.On("Filter", mock.Anything).Return(repoMock)
	repoMock.On("FetchStockPrice").Return(nil, nil)
	repoMock.EXPECT().InsertStockPrice(domain.Hpp{
		StockInId:         arg.dataId,
		StockInSource:     arg.dataType,
		StockIn:           newStockIn[0].Qty,
		Price:             newStockIn[0].Price,
		ProductDetailFkid: newStockIn[0].ProductDetailId,
	}).Return(0, nil).Once()

	tests := []struct {
		name string
		repo hpp.Repository
		args args
	}{
		{"no-pending-stock", repoMock, arg},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo: tt.repo,
			}
			h.logStockIn(tt.args.dataDetail, tt.args.dataType, tt.args.dataId)
		})
	}
}

func Test_logStockIn_inOutEqual(t *testing.T) {
	repoMock := mocks.NewRepository(t)
	newStockIn := []models.StockInProduct{{ProductDetailId: 1, Qty: 10, Price: 500}}

	type args struct {
		dataDetail *[]models.StockInProduct
		dataType   string
		dataId     string
	}

	arg := args{dataDetail: &newStockIn, dataType: domain.PurchaseConfirm.String(), dataId: "1"}
	stockPrice := []domain.Hpp{
		{
			StockPriceId:      99,
			StockOut:          newStockIn[0].Qty,
			ProductDetailFkid: newStockIn[0].ProductDetailId,
		},
	}

	repoMock.On("Filter", mock.Anything).Return(repoMock)
	repoMock.On("FetchStockPrice").Return(&stockPrice, nil)
	repoMock.EXPECT().UpdateStockPrice((stockPrice)[0].StockPriceId, map[string]interface{}{
		"stock_in":        newStockIn[0].Qty,
		"price":           newStockIn[0].Price,
		"stock_in_id":     arg.dataId,
		"stock_in_source": arg.dataType,
	}).Return(nil).Once()

	tests := []struct {
		name string
		repo hpp.Repository
		args args
	}{
		{"in-out-equal", repoMock, arg},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo: tt.repo,
			}
			h.logStockIn(tt.args.dataDetail, tt.args.dataType, tt.args.dataId)
		})
	}
}

func Test_logStockIn_inSmallerThanOut(t *testing.T) {
	os.Setenv("ENV", "localhost")
	repoMock := mocks.NewRepository(t)
	newStockIn := []models.StockInProduct{{ProductDetailId: 1, Qty: 10, Price: 500}}

	type args struct {
		dataDetail *[]models.StockInProduct
		dataType   string
		dataId     string
	}

	arg := args{dataDetail: &newStockIn, dataType: domain.PurchaseConfirm.String(), dataId: "1"}
	stockPrice := []domain.Hpp{
		{
			StockPriceId:      99,
			StockOut:          newStockIn[0].Qty + 10,
			ProductDetailFkid: newStockIn[0].ProductDetailId,
		},
	}
	stockPriceDetail := []models.StockPriceDetailEntity{
		{
			StockPriceDetailID: time.Now().Unix(),
			StockPriceFKID:     int64(stockPrice[0].StockPriceId),
			StockOut:           stockPrice[0].StockOut - newStockIn[0].Qty,
			StockOutID:         utils.RandStringBytes(7),
			StockOutSource:     domain.Sale.String(),
		},
	}

	newStockPriceDetail := make([]map[string]interface{}, 0)
	removeStockPriceDetail := make([]map[string]interface{}, 0)
	updateStockPriceDetail := make(map[string]interface{}, 0)

	newStockPriceDetail = append(newStockPriceDetail, stockPriceDetail[0].ToMap())
	removeStockPriceDetail = append(removeStockPriceDetail, stockPriceDetail[0].ToMap())

	repoMock.On("Filter", mock.Anything).Return(repoMock)
	repoMock.On("FetchStockPrice").Return(&stockPrice, nil)
	repoMock.EXPECT().UpdateStockPrice((stockPrice)[0].StockPriceId, map[string]interface{}{
		"stock_in":        newStockIn[0].Qty,
		"stock_out":       newStockIn[0].Qty,
		"price":           newStockIn[0].Price,
		"stock_in_id":     arg.dataId,
		"stock_in_source": arg.dataType,
	}).Return(nil).Once()
	repoMock.EXPECT().FetchStockPriceDetail(stockPrice[0].StockPriceId).Return(&stockPriceDetail, nil)
	repoMock.EXPECT().AdjustStockPriceDetail(updateStockPriceDetail, removeStockPriceDetail, newStockPriceDetail).Return(0, nil).Once()

	tests := []struct {
		name string
		repo hpp.Repository
		args args
	}{
		{"inSmallerThanOut", repoMock, arg},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo: tt.repo,
			}
			h.logStockIn(tt.args.dataDetail, tt.args.dataType, tt.args.dataId)
		})
	}
}

func Test_logStockIn_inSmallerThanOutWithAdjustment(t *testing.T) {
	os.Setenv("ENV", "localhost")
	dataType := domain.PurchaseConfirm.String()
	dataId := "1"
	// repoMock := mocks.NewRepository(t)
	// repoMock := new(mocks.Repository)
	newStockIn := []models.StockInProduct{{ProductDetailId: 1, Qty: 10, Price: 500}}

	t.Run("inSmallerThanOut", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)
		stockPrice := []domain.Hpp{
			{
				StockPriceId:      99,
				StockOut:          newStockIn[0].Qty + 10,
				ProductDetailFkid: newStockIn[0].ProductDetailId,
			},
		}
		stockPriceDetail := []models.StockPriceDetailEntity{
			{
				StockPriceDetailID: time.Now().Unix(),
				StockPriceFKID:     int64(stockPrice[0].StockPriceId),
				StockOut:           stockPrice[0].StockOut - newStockIn[0].Qty,
				StockOutID:         utils.RandStringBytes(7),
				StockOutSource:     domain.Sale.String(),
			},
		}

		newStockPriceDetail := make([]map[string]interface{}, 0)
		removeStockPriceDetail := make([]map[string]interface{}, 0)
		updateStockPriceDetail := make(map[string]interface{}, 0)

		newStockPriceDetail = append(newStockPriceDetail, stockPriceDetail[0].ToMap())
		removeStockPriceDetail = append(removeStockPriceDetail, stockPriceDetail[0].ToMap())

		repoMock.On("Filter", mock.Anything).Return(repoMock)
		repoMock.On("FetchStockPrice").Return(&stockPrice, nil)
		repoMock.EXPECT().UpdateStockPrice((stockPrice)[0].StockPriceId, map[string]interface{}{
			"stock_in":        newStockIn[0].Qty,
			"stock_out":       newStockIn[0].Qty,
			"price":           newStockIn[0].Price,
			"stock_in_id":     dataId,
			"stock_in_source": dataType,
		}).Return(nil).Once()
		repoMock.EXPECT().FetchStockPriceDetail(stockPrice[0].StockPriceId).Return(&stockPriceDetail, nil)
		repoMock.EXPECT().AdjustStockPriceDetail(updateStockPriceDetail, removeStockPriceDetail, newStockPriceDetail).Return(0, nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.logStockIn(&newStockIn, dataType, dataId)
	})

	t.Run("inSmallerThanOutWithAdjustment", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)
		extraStockOut := float32(10)
		stockPrice := []domain.Hpp{
			{
				StockPriceId:      99,
				StockOut:          newStockIn[0].Qty + extraStockOut,
				ProductDetailFkid: newStockIn[0].ProductDetailId,
			},
		}
		stockPriceDetail := []models.StockPriceDetailEntity{
			{
				StockPriceDetailID: 1,
				StockPriceFKID:     int64(stockPrice[0].StockPriceId),
				StockOut:           5,
				StockOutID:         utils.RandStringBytes(7),
				StockOutSource:     domain.Sale.String(),
			},
			{
				StockPriceDetailID: 2,
				StockPriceFKID:     int64(stockPrice[0].StockPriceId),
				StockOut:           17,
				StockOutID:         utils.RandStringBytes(7),
				StockOutSource:     domain.Sale.String(),
			},
			{
				StockPriceDetailID: 3,
				StockPriceFKID:     int64(stockPrice[0].StockPriceId),
				StockOut:           2,
				StockOutID:         utils.RandStringBytes(7),
				StockOutSource:     domain.Sale.String(),
			},
		}

		newStockPriceDetail := make([]map[string]interface{}, 0)
		removeStockPriceDetail := make([]map[string]interface{}, 0)
		updateStockPriceDetail := stockPriceDetail[1].ToMap()
		updateStockPriceDetail["stock_out"] = stockPriceDetail[1].StockOut - (extraStockOut - stockPriceDetail[2].StockOut)

		newStockPriceDetail = append(newStockPriceDetail, stockPriceDetail[2].ToMap(), stockPriceDetail[1].ToMap())
		newStockPriceDetail[1]["stock_out"] = extraStockOut - stockPriceDetail[2].StockOut

		removeStockPriceDetail = append(removeStockPriceDetail, stockPriceDetail[2].ToMap())

		repoMock.On("Filter", mock.Anything).Return(repoMock)
		repoMock.On("FetchStockPrice").Return(&stockPrice, nil)
		repoMock.EXPECT().UpdateStockPrice((stockPrice)[0].StockPriceId, map[string]interface{}{
			"stock_in":        newStockIn[0].Qty,
			"stock_out":       newStockIn[0].Qty,
			"price":           newStockIn[0].Price,
			"stock_in_id":     dataId,
			"stock_in_source": dataType,
		}).Return(nil).Once()
		repoMock.EXPECT().FetchStockPriceDetail(stockPrice[0].StockPriceId).Return(&stockPriceDetail, nil)
		repoMock.EXPECT().AdjustStockPriceDetail(updateStockPriceDetail, removeStockPriceDetail, newStockPriceDetail).Return(0, nil).Once()

		h := &hppUseCase{
			repo: repoMock,
		}
		h.logStockIn(&newStockIn, dataType, dataId)
	})
}

func Test_hppUseCase_AdjustStockPrice(t *testing.T) {
	t.Run("adjust-up-finish-second", func(t *testing.T) {
		repoMock := mocks.NewRepository(t)
		//up-finish-second
		testData := testdata.DataRecalculateHpp()["up-finish-second"]
		testStockPrices := jsonToHpp(testData["original"])
		s := testStockPrices[0]

		repoMock.On("FetchStockPriceByIdAndSource", s.StockInId, s.StockInSource).Return(&testStockPrices, nil)
		repoMock.On("UpdateStockPriceQtyIn", mock.Anything, mock.Anything).Return(nil)

		h := &hppUseCase{
			repo: repoMock,
		}
		h.UpdateStockPriceQtyIn(s.StockInId, s.StockInSource, s.StockIn)
	})

	type fields struct {
		repo         hpp.Repository
		repoPurchase purchase.Repository
		repoProduct  domain.ProductRepository
	}
	type args struct {
		stockInId     string
		stockInSource string
		stockInNew    float32
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo:         tt.fields.repo,
				repoPurchase: tt.fields.repoPurchase,
				repoProduct:  tt.fields.repoProduct,
			}
			if err := h.UpdateStockPriceQtyIn(tt.args.stockInId, tt.args.stockInSource, tt.args.stockInNew); (err != nil) != tt.wantErr {
				t.Errorf("hppUseCase.AdjustStockPrice() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func buildChangesTestCases() map[string]map[string]string {
	testFiles := testdata.ReadFiles("testdata/buildchanges")
	fmt.Printf("-------- data: %v\n", testFiles)
	return map[string]map[string]string{
		"up-multi-stages": {
			"changes": `{"134":[{"StockPriceId":135,"Qty":10,"Price":16666.67,"ProductDetailId":39389},{"StockPriceId":136,"Qty":5,"Price":16666.67,"ProductDetailId":39389}],"135":[{"StockPriceId":137,"Qty":5,"Price":16666.67,"ProductDetailId":39389},{"StockPriceId":140,"Qty":5,"Price":16666.67,"ProductDetailId":39389}],"136":[{"StockPriceId":140,"Qty":5,"Price":16666.67,"ProductDetailId":39389}],"137":[{"StockPriceId":140,"Qty":5,"Price":16666.67,"ProductDetailId":39389}]}`,
			"details": `{"135":[{"stock_price_detail_id":1195,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1196,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1197,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178899},{"stock_price_detail_id":1198,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178920},{"stock_price_detail_id":1199,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1205,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178920},{"stock_price_detail_id":1206,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1207,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178920},{"stock_price_detail_id":1208,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1213,"stock_price_fkid":135,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615}],"136":[{"stock_price_detail_id":1200,"stock_price_fkid":136,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1201,"stock_price_fkid":136,"stock_out":1,"stock_out_id":"1I0H270S79MO6","stock_out_source":"sales","created_at":1704270583352},{"stock_price_detail_id":1202,"stock_price_fkid":136,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1203,"stock_price_fkid":136,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178899},{"stock_price_detail_id":1204,"stock_price_fkid":136,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172}],"137":[{"stock_price_detail_id":1209,"stock_price_fkid":137,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1214,"stock_price_fkid":137,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1215,"stock_price_fkid":137,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1216,"stock_price_fkid":137,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1223,"stock_price_fkid":137,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172}],"140":[{"stock_price_detail_id":1210,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1211,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1212,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1217,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1IM4K71N75865","stock_out_source":"sales","created_at":1704271178920},{"stock_price_detail_id":1218,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1219,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1220,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1221,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1I0H270S79MO6","stock_out_source":"sales","created_at":1704270583352},{"stock_price_detail_id":1222,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1224,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"17042I119KKK9","stock_out_source":"sales","created_at":1704271195172},{"stock_price_detail_id":1225,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1I0H270S79MO6","stock_out_source":"sales","created_at":1704270583352},{"stock_price_detail_id":1226,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1227,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1228,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1229,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1704271151N1A","stock_out_source":"sales","created_at":1704271154615},{"stock_price_detail_id":1230,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1231,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"170HK7N1641L9","stock_out_source":"sales","created_at":1704271167254},{"stock_price_detail_id":1232,"stock_price_fkid":140,"stock_out":1,"stock_out_id":"1I0H270S79MO6","stock_out_source":"sales","created_at":1704270583352}]}`,
			"updates": `{"134":15,"135":0,"136":0,"137":0,"140":-15}`,
		},
		"down-multi-stages": {
			"changes": `{"3":[{"StockPriceId":2,"Qty":10,"Price":16666.67,"ProductDetailId":39389}],"4":[{"StockPriceId":2,"Qty":5,"Price":16666.67,"ProductDetailId":39389}],"6":[{"StockPriceId":2,"Qty":3,"Price":16666.67,"ProductDetailId":39389},{"StockPriceId":3,"Qty":10,"Price":16666.67,"ProductDetailId":39389},{"StockPriceId":4,"Qty":5,"Price":16666.67,"ProductDetailId":39389}]}`,
			"details": `{"2":[{"stock_price_detail_id":2,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"17MH36O13IMS1","stock_out_source":"sales","created_at":1704363141095},{"stock_price_detail_id":4,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"1I04364I93OH3","stock_out_source":"sales","created_at":1704364797885},{"stock_price_detail_id":5,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"1I04O6H8N0M0I","stock_out_source":"sales","created_at":1704364813161},{"stock_price_detail_id":8,"stock_price_fkid":2,"stock_out":1,"stock_out_id":"170HO65K5055I","stock_out_source":"sales","created_at":1704365254849},{"stock_price_detail_id":9,"stock_price_fkid":2,"stock_out":1,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491},{"stock_price_detail_id":10,"stock_price_fkid":2,"stock_out":3,"stock_out_id":"1I04O6H8N0M0I","stock_out_source":"sales","created_at":1704364813161},{"stock_price_detail_id":11,"stock_price_fkid":2,"stock_out":1,"stock_out_id":"17M4O63117AK7","stock_out_source":"sales","created_at":1704363122759},{"stock_price_detail_id":12,"stock_price_fkid":2,"stock_out":1,"stock_out_id":"1I04364I93OH3","stock_out_source":"sales","created_at":1704364797885},{"stock_price_detail_id":13,"stock_price_fkid":2,"stock_out":1,"stock_out_id":"1IM4364NASA78","stock_out_source":"sales","created_at":1704364690419},{"stock_price_detail_id":14,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491},{"stock_price_detail_id":18,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"1I04364I93OH3","stock_out_source":"sales","created_at":1704364797885},{"stock_price_detail_id":26,"stock_price_fkid":2,"stock_out":2,"stock_out_id":"1IM4364NASA78","stock_out_source":"sales","created_at":1704364690419}],"3":[{"stock_price_detail_id":3,"stock_price_fkid":3,"stock_out":2,"stock_out_id":"1IM4364NASA78","stock_out_source":"sales","created_at":1704364690419},{"stock_price_detail_id":6,"stock_price_fkid":3,"stock_out":2,"stock_out_id":"1I0436H86K5KI","stock_out_source":"sales","created_at":1704364865944},{"stock_price_detail_id":7,"stock_price_fkid":3,"stock_out":1,"stock_out_id":"1704O6HAI82N0","stock_out_source":"sales","created_at":1704364881414},{"stock_price_detail_id":15,"stock_price_fkid":3,"stock_out":1,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491},{"stock_price_detail_id":19,"stock_price_fkid":3,"stock_out":1,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491},{"stock_price_detail_id":24,"stock_price_fkid":3,"stock_out":2,"stock_out_id":"1I0436H86K5KI","stock_out_source":"sales","created_at":1704364865944},{"stock_price_detail_id":25,"stock_price_fkid":3,"stock_out":1,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491}],"4":[{"stock_price_detail_id":22,"stock_price_fkid":4,"stock_out":1,"stock_out_id":"1IM4O652705IN","stock_out_source":"sales","created_at":1704365273491},{"stock_price_detail_id":23,"stock_price_fkid":4,"stock_out":3,"stock_out_id":"1704O6HAI82N0","stock_out_source":"sales","created_at":1704364881414},{"stock_price_detail_id":27,"stock_price_fkid":4,"stock_out":1,"stock_out_id":"1704O6HAI82N0","stock_out_source":"sales","created_at":1704364881414}]}`,
			"updates": `{"2":-18,"3":0,"4":0,"6":18}`,
		},
		"real-data": {
			"changes": testFiles["2105_changes"],
			"details": testFiles["2105_details"],
			"updates": ``,
		},
	}
}

func Test_buildChanges(t *testing.T) {
	os.Setenv("ENV", "localhost")
	log.SetCurrentLevel()

	type args struct {
		changes              map[int][]models.HppUpdate
		stockPriceDetailsMap map[int64][]models.StockPriceDetailEntity
	}
	tests := []struct {
		name  string
		args  args
		want  []map[string]interface{}
		want1 []models.StockPriceDetailEntity
		want2 map[int64]float32
	}{}

	testCases := buildChangesTestCases()
	for name, data := range testCases {
		var changes map[int][]models.HppUpdate
		var stockPriceDetailsMap map[int64][]models.StockPriceDetailEntity
		var stockOutUpdates map[int64]float32

		if err := json.Unmarshal([]byte(data["changes"]), &changes); err != nil {
			t.Error(err)
		}
		if err := json.Unmarshal([]byte(data["details"]), &stockPriceDetailsMap); err != nil {
			t.Error(err)
		}
		if err := json.Unmarshal([]byte(data["updates"]), &stockOutUpdates); err != nil {
			t.Error(err)
		}

		tests = append(tests, struct {
			name  string
			args  args
			want  []map[string]interface{}
			want1 []models.StockPriceDetailEntity
			want2 map[int64]float32
		}{
			name:  name,
			args:  args{changes: changes, stockPriceDetailsMap: stockPriceDetailsMap},
			want:  nil,
			want1: nil,
			want2: stockOutUpdates,
		})
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, got2, got3 := buildChanges(tt.args.changes, tt.args.stockPriceDetailsMap)
			fmt.Println("stockPriceDetailNew: ", utils.SimplyToJson(got))
			fmt.Println("-------------------------------")
			fmt.Println("stockPriceDetailAdjust: ", utils.SimplyToJson(len(got1)))
			// os.WriteFile("/Users/<USER>/Documents/WORK/api-report/adjus.json", []byte(utils.SimplyToJson((got1))), os.ModePerm)
			fmt.Println("-------------------------------")
			fmt.Println("stockPriceDetailRemove: ", utils.SimplyToJson(got2))
			fmt.Println("-------------------------------")
			fmt.Println("stockOutUpdates: ", utils.SimplyToJson(got3))
			// if !reflect.DeepEqual(got, tt.want) {
			// 	t.Errorf("buildChanges() got = %v, want %v", got, tt.want)
			// }
			// if !reflect.DeepEqual(got1, tt.want1) {
			// 	t.Errorf("buildChanges() got1 = %v, want %v", got1, tt.want1)
			// }
			if !reflect.DeepEqual(got2, tt.want2) {
				t.Errorf("buildChanges() stockOutUpdates = %v, want %v", got2, tt.want2)
			}
		})
	}
}

func Test_uniqueStocks(t *testing.T) {
	t.Run("duplicate", func(t *testing.T) {
		dataDetailSales := []models.StockInProduct{
			{ProductDetailId: 1, Qty: 10},
			{ProductDetailId: 2, Qty: 5},
			{ProductDetailId: 1, Qty: 5},
		}
		got := uniqueStocks(dataDetailSales)
		gotJs, _ := json.Marshal(got)
		fmt.Println("got>> ", string(gotJs))
	})
}
