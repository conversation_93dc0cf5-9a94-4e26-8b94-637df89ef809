package crm

import "gitlab.com/uniqdev/backend/api-report/domain"

type UseCase interface {
	FetchCustomerList(domain.User, domain.RequestParam) (interface{}, error)
	FetchCustomerTransactionList(user domain.User, request domain.RequestParam) (interface{}, error)
	FetchMemberAcquisition(user domain.User, request domain.RequestParam) (interface{}, error)
	FetchCohortAnalysis(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error)
}
