package v1

import (
	"encoding/json"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"strings"
)

func getTotalSales(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	//get sales information v1
	/*
		querySalesInformation := db.GetSQLRaw(`SELECT
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			IFNULL(ROUND(sum(sd.qty) / count(sv.sales_fkid)), sum(sd.qty)) - IFNULL(sum(sv.qty),0) AS qty_clean
			FROM sales s
			LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id
			LEFT JOIN sales_detail sd ON sd.sales_fkid = s.sales_id
			LEFT JOIN sales_void sv ON sv.sales_fkid = s.sales_id
			WHERE o.admin_fkid=?
			AND s.time_created >=?
			AND s.time_created <=?
			AND s.status = 'success'
			AND s.data_status='on'
			AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
			`+whereInOutlet+`
			GROUP BY sales_id
			HAVING qty_clean != 0
		`, adminId, timeStart, timeEnd)
	*/

	//get sales information v2
	querySalesInformation := db.GetSQLRaw(`
		SELECT 
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			sum(sd.qty)-IFNULL(sum(sv.qty),0) AS qty_clean
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id=sd.sales_fkid
		LEFT JOIN sales_void sv ON sv.sales_fkid=s.sales_id AND sd.product_detail_fkid=sv.product_detail_fkid
		LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid
		WHERE o.admin_fkid = ?
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP by sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	query, err := db.QueryArray(`
		SELECT
			outlet_id,
			outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (` + querySalesInformation + `) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)
	utils.CheckErr(err)
	return query
}

func getTotalSales2(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('" + outletId + "') "
	}

	querySalesInformation := db.GetSQLRaw(`
		SELECT
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			(sum(sd.qty) - IFNULL(sum(sv.qty),0)) AS qty_clean
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id 
		LEFT JOIN sales_detail sd ON sd.sales_fkid = s.sales_id 
		LEFT JOIN (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                            ON sd.sales_detail_id = sv.sales_detail_fkid
		WHERE o.admin_fkid= ?
		AND s.time_created >= ?
		AND s.time_created <= ?
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP BY sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	query, err := db.QueryArray(`
		SELECT
			outlet_id,
			outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (` + querySalesInformation + `) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)
	utils.CheckErr(err)
	return query
}

func getPurchaseLunas(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(p.grand_total) AS sum_grandtotal
		FROM purchase p
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.bayar >= p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid
	`, timeStart, timeEnd, adminId)
	utils.CheckErr(err)
	return query
}

func getPurchaseHutang(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(p.bayar) AS sum_uangmuka
		FROM purchase p
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.bayar < p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid
	`, timeStart, timeEnd, adminId)
	utils.CheckErr(err)
	return query
}

func getDebtPayment(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			sum(dp.nominal) AS sum_debtpayment,
			p.grand_total,
			p.bayar AS purchase_um,
			if(p.data_created<?, 0, p.bayar) AS purchase_um_range,
			dp.purchase_fkid AS purchase_id,
			IFNULL(
			sum(
				(SELECT sum(sdp.nominal) FROM debt_payment sdp WHERE sdp.time_created<? AND sdp.purchase_fkid=dp.purchase_fkid)
			), 0) AS debtpaid_under_range,
			sum(dp.nominal) AS debtpaid_on_range
		FROM debt_payment dp
		LEFT JOIN purchase p ON dp.purchase_fkid = p.purchase_id
		LEFT JOIN outlets o ON p.outlet_fkid = o.outlet_id
		WHERE p.admin_fkid = ?
		AND dp.time_created >= ?
		AND dp.time_created <= ?
		AND dp.data_status = 'on'
		`+whereInOutlet+`
		GROUP BY p.outlet_fkid, p.bayar, p.grand_total, purchase_id
	`, timeStart, timeStart, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	return query
}

func getDebt(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND p.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT
			p.outlet_fkid AS outlet_id,
			o.name AS outlet_name,
			p.purchase_id,
			p.grand_total,
			p.bayar AS uang_muka,
			IF(p.bayar < p.grand_total, (p.grand_total-p.bayar), 0) AS hutang,
			
			(SELECT IFNULL(sum(sdp.nominal),0)
				FROM debt_payment sdp
				WHERE sdp.time_created<=?
				AND sdp.purchase_fkid=p.purchase_id
				AND sdp.data_status="on"
			) AS hutang_dibayar
		FROM purchase p
		LEFT JOIN outlets o ON o.outlet_id = p.outlet_fkid
		LEFT JOIN debt_payment dp ON dp.purchase_fkid = p.purchase_id
		WHERE p.bayar < p.grand_total
		AND p.data_created >= ?
		AND p.data_created <= ?
		AND p.admin_fkid = ?	
		`+whereInOutlet+`
		GROUP BY purchase_id
	`, timeEnd, timeStart, timeEnd, adminId)
	utils.CheckErr(err)
	return query
}

func getPendingIncome(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND ts.outlet_fkid IN ('" + outletId + "') "
	}

	query, err := db.QueryArray(`
		SELECT 
			o.outlet_id,
			o.name AS outlet_name,
			sum(ts.grand_total) AS pending_income
		FROM tmp_sales ts
		LEFT JOIN outlets o ON ts.outlet_fkid = o.outlet_id
		WHERE ts.time_created >= ?
		AND ts.time_created <= ?
		AND o.admin_fkid = ?
		AND ts.status = 'pending'
		`+whereInOutlet+`
		GROUP BY ts.outlet_fkid
		ORDER BY pending_income DESC
	`, timeStart, timeEnd, adminId)
	utils.CheckErr(err)
	return query
}

func SalesTransaction(ctx *fasthttp.RequestCtx) {
	//data post
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get post value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	//timeDiff := cast.ToInt64(post["time_diff"]) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])

	//render outletlist
	outletIdMap := map[string]interface{}{}

	//get data
	chanSales := make(chan map[string]interface{})
	go func() {
		query := getTotalSales(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			average_income := cast.ToFloat64(data["total_income"]) / cast.ToFloat64(data["total_transaction"])
			average_income_customer := cast.ToFloat64(data["total_income"]) / cast.ToFloat64(data["total_customer"])

			//render map
			dataRender[id] = map[string]interface{}{
				"total_customer":          data["total_customer"],
				"total_income":            data["total_income"],
				"total_transaction":       data["total_transaction"],
				"average_income":          average_income,
				"average_income_customer": average_income_customer,
			}

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanSales <- dataRender
	}()
	chanPurchaseLunas := make(chan map[string]interface{})
	go func() {
		query := getPurchaseLunas(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_grandtotal"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseLunas <- dataRender
	}()
	chanPurchaseHutang := make(chan map[string]interface{})
	go func() {
		query := getPurchaseHutang(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_uangmuka"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseHutang <- dataRender
	}()
	chanDebtPayment := make(chan map[string]interface{})
	go func() {
		query := getDebtPayment(adminId, timeStart, timeEnd, outletIds)

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = cast.ToInt64(dataRender[id]) + cast.ToInt64(data["debt"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanDebtPayment <- dataRender
	}()
	chanDebt := make(chan map[string]interface{})
	go func() {
		query := getDebt(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			debt := cast.ToInt64(0)
			if cast.ToInt64(data["hutang"]) > cast.ToInt64(data["hutang_dibayar"]) {
				debt = cast.ToInt64(data["hutang"]) - cast.ToInt64(data["hutang_dibayar"])
			}

			if dataRender[id] != nil {
				q1 := cast.ToStringMap(dataRender[id])
				debt += cast.ToInt64(q1["debt"])
			}

			//render map
			dataRender[id] = debt

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}

		chanDebt <- dataRender
	}()
	chanPendingIncome := make(chan map[string]interface{})
	go func() {
		query := getPendingIncome(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = data["pending_income"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPendingIncome <- dataRender
	}()

	//hasil merge id
	sales := <-chanSales
	purchase_lunas := <-chanPurchaseLunas
	purchase_hutang := <-chanPurchaseHutang
	debt_payment := <-chanDebtPayment
	debt := <-chanDebt
	pending_income := <-chanPendingIncome

	//render outletlist
	dataRender := []map[string]interface{}{}
	for index, data := range outletIdMap {
		dataSales := sales[index]
		if dataSales == nil {
			dataSales = map[string]interface{}{
				"average_income":          0,
				"average_income_customer": 0,
				"total_customer":          0,
				"total_income":            0,
				"total_transaction":       0,
			}
		}
		dataPurchaseLunas := purchase_lunas[index]
		if dataPurchaseLunas == nil {
			dataPurchaseLunas = 0
		}
		dataPurchaseHutang := purchase_hutang[index]
		if dataPurchaseHutang == nil {
			dataPurchaseHutang = 0
		}
		dataDebtPayment := debt_payment[index]
		if dataDebtPayment == nil {
			dataDebtPayment = 0
		}
		dataDebt := debt[index]
		if dataDebt == nil {
			dataDebt = 0
		}
		dataPendingIncome := pending_income[index]
		if dataPendingIncome == nil {
			dataPendingIncome = 0
		}

		//render
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail": map[string]interface{}{
				"sales":           dataSales,
				"purchase_lunas":  dataPurchaseLunas,
				"purchase_hutang": dataPurchaseHutang,
				"debt_payment":    dataDebtPayment,
				"debt":            dataDebt,
				"pending_income":  dataPendingIncome,
			},
		}
		dataRender = append(dataRender, dataFormat)
	}

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(dataRender)
}

func SalesTransaction_map(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//render outletlist
	outletIdMap := map[string]interface{}{}

	//get data
	chanSales := make(chan map[string]interface{})
	go func() {
		query := getTotalSales(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			average_income := cast.ToFloat64(data["total_income"]) / cast.ToFloat64(data["total_transaction"])
			average_income_customer := cast.ToFloat64(data["total_income"]) / cast.ToFloat64(data["total_customer"])

			//render map
			dataRender[id] = map[string]interface{}{
				"total_customer":          data["total_customer"],
				"total_income":            data["total_income"],
				"total_transaction":       data["total_transaction"],
				"average_income":          average_income,
				"average_income_customer": average_income_customer,
			}

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanSales <- dataRender
	}()
	chanPurchaseLunas := make(chan map[string]interface{})
	go func() {
		query := getPurchaseLunas(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_grandtotal"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseLunas <- dataRender
	}()
	chanPurchaseHutang := make(chan map[string]interface{})
	go func() {
		query := getPurchaseHutang(adminId, timeStart, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			dataRender[id] = data["sum_uangmuka"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPurchaseHutang <- dataRender
	}()
	chanDebtPayment := make(chan map[string]interface{})
	go func() {
		query := getDebtPayment(adminId, timeStart, timeEnd, outletIds)

		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = cast.ToInt64(dataRender[id]) + cast.ToInt64(data["debt"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanDebtPayment <- dataRender
	}()
	chanDebt := make(chan map[string]interface{})
	go func() {
		query := getDebt(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			debt := cast.ToInt64(0)
			if cast.ToInt64(data["hutang"]) > cast.ToInt64(data["hutang_dibayar"]) {
				debt = cast.ToInt64(data["hutang"]) - cast.ToInt64(data["hutang_dibayar"])
			}

			if dataRender[id] != nil {
				q1 := cast.ToStringMap(dataRender[id])
				debt += cast.ToInt64(q1["debt"])
			}

			//render map
			dataRender[id] = debt

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}

		chanDebt <- dataRender
	}()
	chanPendingIncome := make(chan map[string]interface{})
	go func() {
		query := getPendingIncome(adminId, 0, timeEnd, outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = data["pending_income"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id":   data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPendingIncome <- dataRender
	}()

	//hasil merge id
	sales := <-chanSales
	purchase_lunas := <-chanPurchaseLunas
	purchase_hutang := <-chanPurchaseHutang
	debt_payment := <-chanDebtPayment
	debt := <-chanDebt
	pending_income := <-chanPendingIncome

	//render outletlist
	dataRender := []map[string]interface{}{}
	for index, data := range outletIdMap {
		dataSales := sales[index]
		if dataSales == nil {
			dataSales = map[string]interface{}{
				"average_income":          0,
				"average_income_customer": 0,
				"total_customer":          0,
				"total_income":            0,
				"total_transaction":       0,
			}
		}
		dataPurchaseLunas := purchase_lunas[index]
		if dataPurchaseLunas == nil {
			dataPurchaseLunas = 0
		}
		dataPurchaseHutang := purchase_hutang[index]
		if dataPurchaseHutang == nil {
			dataPurchaseHutang = 0
		}
		dataDebtPayment := debt_payment[index]
		if dataDebtPayment == nil {
			dataDebtPayment = 0
		}
		dataDebt := debt[index]
		if dataDebt == nil {
			dataDebt = 0
		}
		dataPendingIncome := pending_income[index]
		if dataPendingIncome == nil {
			dataPendingIncome = 0
		}

		//render
		data := data.(map[string]interface{})
		dataFormat := map[string]interface{}{
			"outlet_id":   data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail": map[string]interface{}{
				"sales":           dataSales,
				"purchase_lunas":  dataPurchaseLunas,
				"purchase_hutang": dataPurchaseHutang,
				"debt_payment":    dataDebtPayment,
				"debt":            dataDebt,
				"pending_income":  dataPendingIncome,
			},
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender
}

func SalesTransaction_HTTP(ctx *fasthttp.RequestCtx) {
	//data post
	jsonPost := ctx.PostBody()
	post, err := djson.DecodeObject(jsonPost)
	utils.CheckErr(err)

	//get post value
	adminId := cast.ToString(post["admin_id"])
	timeStart := cast.ToInt64(post["time_start"])
	timeEnd := cast.ToInt64(post["time_end"])
	//timeDiff := cast.ToInt64(post["time_diff"]) //timezone
	outletIds := cast.ToSlice(post["outlet_ids"])

	//result data
	dataRender := SalesTransaction_map(adminId, timeStart, timeEnd, outletIds)

	//output
	ctx.SetContentType("application/json")
	_ = json.NewEncoder(ctx).Encode(dataRender)
}
