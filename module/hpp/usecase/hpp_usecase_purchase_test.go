package usecase

import (
	"fmt"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/module/hpp"
	"gitlab.com/uniqdev/backend/api-report/module/hpp/repository/mysql"
)

func Test_hppUseCase_recordPurchaseConfirm(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-report/.env")
	// os.Setenv("ENV", "localhost")
	log.SetCurrentLevel()
	envPath, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(filepath.Join(envPath, ".env"))
	dbConn := db.GetConn()
	if dbConn == nil {
		t.Fatal("database not initialized...")
	}
	hppRepo := mysql.NewMysqlHppRepository(dbConn)

	type fields struct {
		repo hpp.Repository
	}
	type args struct {
		purchaseConfirmId int
		qtyConfirm        float32
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []interface{}
	}{
		{"test", fields{repo: hppRepo}, args{purchaseConfirmId: 133, qtyConfirm: 30}, []interface{}{}},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			h := &hppUseCase{
				repo: tt.fields.repo,
			}
			if got := h.recordPurchaseConfirm(tt.args.purchaseConfirmId, tt.args.qtyConfirm); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("hppUseCase.recordPurchaseConfirm() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_hppUseCase_handleStockOpname(t *testing.T) {
	godotenv.Load("/Users/<USER>/Documents/WORK/api-report/.env")
	// os.Setenv("ENV", "localhost")
	log.SetCurrentLevel()
	envPath, err := os.Getwd()
	if err != nil {
		t.Fatal(err)
	}
	fmt.Println(filepath.Join(envPath, ".env"))
	dbConn := db.GetConn()
	if dbConn == nil {
		t.Fatal("database not initialized...")
	}
	hppRepo := mysql.NewMysqlHppRepository(dbConn)

	h := &hppUseCase{
		repo: hppRepo,
	}
	h.HandleStockOpname([]int{800})
}
