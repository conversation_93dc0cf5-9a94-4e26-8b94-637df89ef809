package log

import (
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/joho/godotenv"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
)

const (
	PREFIX_DEBUG = "\033[1;37m[DEBUG]\033[0m"
	PREFIX_INFO  = "\033[1;32m[INFO]\033[0m"
	PREFIX_WARN  = "\033[1;33m[WARN]\033[0m"
	PREFIX_ERROR = "\033[1;31m[ERROR]\033[0m"

	LEVEL_TRACE = iota
	LEVEL_DEBUG
	LEVEL_INFO
	LEVEL_WARN
)

var currentLogLevel = getLogLevel()

func getLogLevel() int {
	if os.Getenv("ENV") == "" {
		godotenv.Load(".env")
	}
	env := os.Getenv("ENV")
	switch env {
	case "localhost", "development":
		return LEVEL_TRACE
	case "staging":
		return LEVEL_INFO
	case "production":
		return LEVEL_WARN
	default:
		return LEVEL_WARN
	}
}

func SetCurrentLevel() {
	currentLogLevel = getLogLevel()
}

func Debug(msg string, v ...interface{}) {
	if currentLogLevel > LEVEL_TRACE {
		return
	}
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf(" %s %s:%d  >> ", getDate(), utils.GetFileName(fn, true), line)
	// fmt.Printf(PREFIX_DEBUG+prefix+msg+"\n", v...)
	fmt.Printf(prefix+msg+"\n", v...)
}

func Info(msg string, v ...interface{}) {
	if currentLogLevel > LEVEL_INFO {
		return
	}
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("%s:%d  >> ", utils.GetFileName(fn, true), line)
	fmt.Printf(prefix+msg+"\n", v...)
}

func Warn(msg string, v ...interface{}) {
	if currentLogLevel > LEVEL_WARN {
		return
	}
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf("  %s %s:%d  >> ", getDate(), utils.GetFileName(fn, true), line)
	fmt.Printf(PREFIX_WARN+prefix+msg+"\n", v...)
}

func Error(msg string, v ...interface{}) {
	msg = fmt.Sprintf(msg, v...)
	_, fn, line, _ := runtime.Caller(1)
	prefix := fmt.Sprintf(" %s %s:%d  >> ", getDate(), utils.GetFileName(fn, true), line)
	fmt.Println(PREFIX_ERROR + prefix + msg)

	go func() {
		utils.SendMessageToSlack(fmt.Sprintf("#%s  [ERROR] %s  :: %s", strings.ToUpper(os.Getenv("server")), prefix, msg))
	}()
}

func IfError(err error) bool {
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		pref := ""
		if os.Getenv("ENV") == "localhost" {
			pref = PREFIX_ERROR
		}
		msg := fmt.Sprintf("%s%s %s:%d  >> %v ", pref, getDate(), utils.GetFileName(fn, true), line, err)
		fmt.Println(msg)

		go func(msg string) {
			fmt.Println("hook: ", sdt.hook)
			if sdt.hook != nil {
				err = sdt.hook.send(logOutput{
					Message: msg,
				})
				if err != nil {
					fmt.Println("sending error message error: ", err)
					utils.SendMessageToSlack(fmt.Sprintf("#%s  [ERROR] >> %s", strings.ToUpper(os.Getenv("server")), msg))
				}
			} else {
				// utils.SendMessageToSlack(fmt.Sprintf("#%s  [ERROR] >> %s", strings.ToUpper(os.Getenv("server")), msg))
			}
		}(msg)

		return true
	}
	return false
}

func IfErrorSetStatus(ctx *fasthttp.RequestCtx, err error) bool {
	if IfError(err) {
		ctx.SetContentType("text/plain; charset=utf-8")
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.SetStatusCode(fasthttp.StatusInternalServerError)
		return true
	}
	return false
}

func getDate() string {
	_, offset := time.Now().Zone()
	diff := int64(25200 - offset) //25200 is developer offset (WIB)
	return time.Unix(time.Now().Unix()+diff, 0).Format("02/01/2006 15:04:05")
}
