package auth

import (
	"bufio"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"fmt"
	"os"
	"runtime/debug"
	"time"

	"github.com/dgrijalva/jwt-go"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type JWTAuthBackend struct {
	privateKey *rsa.PrivateKey
	PublicKey  *rsa.PublicKey
}

var jwtAuthInstance *JWTAuthBackend = nil

func InitJWTAuth() *JWTAuthBackend {
	if jwtAuthInstance == nil {
		privateKeyPath := os.Getenv("JWT_PRIVATE_KEY")
		pubKeyPath := os.Getenv("JWT_PUBLIC_KEY")

		if privateKeyPath == "" {
			privateKeyPath = "config/auth/app.rsa"
		}
		if pubKeyPath == "" {
			pubKeyPath = "config/auth/app.rsa.pub"
		}
		
		checkOrCreateKeys(pubKeyPath, privateKeyPath)

		jwtAuthInstance = &JWTAuthBackend{
			privateKey: getPrivateKey(privateKeyPath),
			PublicKey:  getPublicKey(pubKeyPath),
		}
	}
	return jwtAuthInstance
}

func (auth *JWTAuthBackend) GenerateToken(dataArr map[string]interface{}) *models.AuthToken {
	expired := time.Now().Add(time.Minute * 60).Unix()
	token := jwt.New(jwt.SigningMethodRS512)

	claims := jwt.MapClaims{}
	claims["exp"] = expired
	claims["iat"] = time.Now().Unix()
	for key, data := range dataArr {
		claims[key] = data
	}

	token.Claims = claims

	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if err != nil {
		panic(err)
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired
	return authToken
}

func (auth *JWTAuthBackend) GenerateTokenWithRefresh(jwtId string, dataArr map[string]interface{}) *models.AuthToken {
	expired := time.Now().Add(time.Minute * 60).Unix()
	token := jwt.New(jwt.SigningMethodRS512)
	id := jwtId

	claims := jwt.MapClaims{}
	claims["jti"] = id
	claims["exp"] = expired
	claims["iat"] = time.Now().Unix()
	//claims["auth"] = os.Getenv("auth_token")
	for key, data := range dataArr {
		claims[key] = data
	}
	token.Claims = claims
	//token.Claims = jwt.MapClaims{
	//	"exp":            expired,
	//	"iat":            time.Now().Unix(),
	//	"sub":            id,
	//	"access_allowed": data,
	//	"context":        "report-" + os.Getenv("server"),
	//	"authorization":  os.Getenv("token"),
	//}
	authToken := new(models.AuthToken)
	tokenString, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}
	authToken.Token = tokenString
	authToken.Type = "Bearer"
	authToken.Expired = expired

	//create refresh token
	token = jwt.New(jwt.SigningMethodRS512)
	refreshTokenExpired := time.Now().Add(time.Hour * 24 * 30).Unix() //expired in 30 days

	//claims := jwt.MapClaims{}
	claims["jti"] = id
	claims["exp"] = refreshTokenExpired
	claims["iat"] = time.Now().Unix()
	for key, data := range dataArr {
		claims[key] = data
	}
	token.Claims = claims
	//token.Claims = jwt.MapClaims{
	//	"exp":     refreshTokenExpired,
	//	"iat":     time.Now().Unix(),
	//	"sub":     id,
	//	"context": "report-" + os.Getenv("server"),
	//	"insurer": insurerId,
	//	"role":    role,
	//}

	refreshToken, err := token.SignedString(auth.privateKey)
	if log.IfError(err) {
		return authToken
	}

	// authToken.RefreshToken = refreshToken
	//prevent refresh token to be more than 72 bytes, as it will be hashed (bcrypted)
	//bcrypt in go has limit (max byte is 72)
	refreshToken = refreshToken[:72]
	authToken.RefreshToken = refreshToken

	go saveUserSession(id, refreshToken, refreshTokenExpired)

	return authToken
}

/*
save refresh token in db, will be used for validate request new token
*/
func saveUserSession(id, refreshToken string, tokenExpired int64) {
	tokenHash, err := utils.HashPassword(refreshToken)
	if log.IfError(err) {
		log.Info("failed hash: %v | length: %v", refreshToken, len(refreshToken))
		debug.PrintStack()
	}

	//insert refresh token to db,
	//for new logged in user, the refresh token is not exist, so we insert it, otherwise we just have to update the data
	//_, err = db.GetDb().Exec("insert into users_session (id, timestamp, token, expired_at) values (?,?,?,?) on duplicate key update timestamp=?, token=?, expired_at=?",
	//	id,  time.Now().Unix(), tokenHash, tokenExpired,  time.Now().Unix(), tokenHash, tokenExpired)
	_, err = db.Query(`INSERT INTO users_session (id, timestamp, token, expired_at) VALUES (?,?,?,?)
								on duplicate key update timestamp=?, token=?, expired_at=?`,
		id, time.Now().Unix(), tokenHash, tokenExpired, time.Now().Unix(), tokenHash, tokenExpired)
	log.IfError(err)

	//tokenHash, err := utils.HashPassword(refreshToken)
	//log.IfError(err)
	//localDb := db.GetDbJson()
	//err = localDb.Write("refresh_token", id, models.RefreshToken{RefreshToken: tokenHash, Expired: refreshTokenExpired})
	//log.IfError(err)
}

func getPrivateKey(filePath string) *rsa.PrivateKey {
	privateKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := privateKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(privateKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	privateKeyFile.Close()

	privateKeyImported, err := x509.ParsePKCS1PrivateKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	return privateKeyImported
}

func getPublicKey(filePath string) *rsa.PublicKey {
	publicKeyFile, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}

	pemFileInfo, _ := publicKeyFile.Stat()
	var size = pemFileInfo.Size()
	pemBytes := make([]byte, size)

	buffer := bufio.NewReader(publicKeyFile)
	_, err = buffer.Read(pemBytes)

	data, _ := pem.Decode([]byte(pemBytes))

	publicKeyFile.Close()

	publicKeyImported, err := x509.ParsePKIXPublicKey(data.Bytes)

	if err != nil {
		panic(err)
	}

	rsaPub, ok := publicKeyImported.(*rsa.PublicKey)

	if !ok {
		panic(err)
	}

	return rsaPub
}

func checkOrCreateKeys(pubKeyPath, privKeyPath string) {
	if os.Getenv("ENV") != "localhost" {
		return
	}
	// Check if the file exists
	if _, err := os.Stat(privKeyPath); os.IsNotExist(err) {
		fmt.Println("File does not exist:", privKeyPath)
	} else {
		fmt.Println("File exists:", privKeyPath)
		return
	}

	// Generate a new RSA key pair
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		fmt.Println("Error generating key:", err)
		return
	}

	// Encode the private key in PEM format
	privateKeyBytes := x509.MarshalPKCS1PrivateKey(privateKey)
	privateKeyBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privateKeyBytes,
	}
	privateKeyFile, err := os.Create(privKeyPath)
	if err != nil {
		fmt.Println("Error creating private key file:", err)
		return
	}
	defer privateKeyFile.Close()
	if err := pem.Encode(privateKeyFile, privateKeyBlock); err != nil {
		fmt.Println("Error encoding private key:", err)
		return
	}

	// Encode the public key in PEM format
	publicKeyBytes, err := x509.MarshalPKIXPublicKey(&privateKey.PublicKey)
	if err != nil {
		fmt.Println("Error marshalling public key:", err)
		return
	}
	publicKeyBlock := &pem.Block{
		Type:  "RSA PUBLIC KEY",
		Bytes: publicKeyBytes,
	}
	publicKeyFile, err := os.Create(pubKeyPath)
	if err != nil {
		fmt.Println("Error creating public key file:", err)
		return
	}
	defer publicKeyFile.Close()
	if err := pem.Encode(publicKeyFile, publicKeyBlock); err != nil {
		fmt.Println("Error encoding public key:", err)
		return
	}

	fmt.Println("RSA key pair generated successfully!")
}
