ENV=development

db_host = ************
db_name = admin_uniq_v2
db_user =
db_password =
db_comm =     #tcp/unix

BQ_DB_NAME = xxx
BQ_DB_LOCATION=xxx

ssl_cert = config/ssl/server.pem
ssl_key = config/ssl/server.key

JWT_PRIVATE_KEY=/config/auth/app.rsa
JWT_PUBLIC_KEY=/config/auth/app.rsa.pub

auth_token = Bearer ycEadPhdwPe7353vgSHwbLqNeCUQ7R5M
app_port = 2893

GOOGLE_APPLICATION_CREDENTIALS=uniq-credential.json
BIGQUERY_CREDENTIAL_PATH=

#Enabel/Disable Feature 
ENABLE_FIFO=true 

#debug/warning/info: by default will be based on env
LOG_LEVEL=