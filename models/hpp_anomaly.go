package models

type AnomalyOverStockOut struct {
	Count   int     `json:"count"`
	Sources string  `json:"sources"`
	AvgDiff float64 `json:"avg_diff"`
}

type AnomalyMismatchStockOut struct {
	StockPriceID  int     `json:"stock_price_id"`
	StockInSource string  `json:"stock_in_source"`
	AllOut        float64 `json:"all_out"`
	StockOut      float64 `json:"stock_out"`
	StockIn       float64 `json:"stock_in"`
	Sources       string  `json:"sources"`
	Diff          float64 `json:"diff"`
}
