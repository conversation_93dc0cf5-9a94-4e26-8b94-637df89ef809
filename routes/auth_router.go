package routes

import (
	"github.com/buaazp/fasthttprouter"
	v1 "gitlab.com/uniqdev/backend/api-report/controller/auth/v1"
	"gitlab.com/uniqdev/backend/api-report/core/auth"
)

func SetAuthRoutes(router *fasthttprouter.Router) *fasthttprouter.Router {
	//router.GET("/login", controller.Login)
	router.POST("/v1/auth/login", v1.Login)
	router.POST("/v1/auth/token", auth.ValidateRefreshToken(v1.Token))
	router.POST("/v1/auth/token_by_phpsession", auth.AllowAccess(v1.TokenByPHPSession))
	router.POST("/v1/auth/token_by_rememberme", auth.AllowAccess(v1.TokenByRememberMe))
	router.POST("/v1/auth/token_refresh_revoke", auth.ValidateRefreshToken(v1.TokenRefreshRevoke))
	return router
}
