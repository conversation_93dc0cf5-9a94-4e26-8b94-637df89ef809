package usecase

import (
	"math"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type closeShiftUseCase struct {
	repo domain.CloseShiftRepository
}

func NewCloseShiftUseCase(repository domain.CloseShiftRepository) domain.CloseShiftUseCase {
	return &closeShiftUseCase{repository}
}

// FetchCloseShiftRecap implements domain.CloseShiftUseCase.
func (c *closeShiftUseCase) FetchCloseShiftRecap(request domain.SalesReportRequest, user domain.User) (models.CloseShiftResponse, error) {
	// Create channels to receive return values from goroutines
	salesRecapShiftCh := make(chan models.SalesRecapShift)
	salesDetailShiftCh := make(chan models.SalesDetailShift)
	payShiftCh := make(chan models.PayShift)
	taxShiftCh := make(chan models.TaxShift)
	voidShiftRecapCh := make(chan models.VoidShiftRecap)
	// refundShiftRecapCh := make(chan models.RefundShiftRecap)
	promoRecapProdukCh := make(chan models.PromoRecapTotal)
	promoRecapShiftCh := make(chan models.PromoRecapTotal)

	defer close(salesRecapShiftCh)
	defer close(salesDetailShiftCh)
	defer close(payShiftCh)
	defer close(taxShiftCh)
	defer close(voidShiftRecapCh)
	// defer close(refundShiftRecapCh)
	defer close(promoRecapProdukCh)
	defer close(promoRecapShiftCh)

	// Start goroutines for each repository function call
	go func() {
		startTime := time.Now()
		salesRecapShift, _ := c.repo.FetchSalesRecapShift(request, user)
		salesRecapShiftCh <- salesRecapShift
		log.Info("Total time elapsed for FetchSalesRecapShift: %v", time.Since(startTime))
	}()

	go func() {
		startTime := time.Now()
		salesDetailShift, _ := c.repo.FetchSalesDetailShift(request, user)
		salesDetailShiftCh <- salesDetailShift
		log.Info("Total time elapsed for FetchSalesDetailShift: %v", time.Since(startTime))
	}()

	go func() {
		payShift, _ := c.repo.FetchPayShift(request, user)
		payShiftCh <- payShift
	}()

	go func() {
		startTime := time.Now()
		taxShift, _ := c.repo.FetchTaxShift(request, user)
		taxShiftCh <- taxShift
		log.Info("Total time elapsed for FetchTaxShift: %v", time.Since(startTime))
	}()

	go func() {
		startTime := time.Now()
		voidShiftRecap, _ := c.repo.FetchVoidShiftRecap(request, user)
		voidShiftRecapCh <- voidShiftRecap
		log.Info("Total time elapsed for FetchVoidShiftRecap: %v", time.Since(startTime))
	}()

	// go func() {
	// 	startTime := time.Now()
	// 	refundShiftRecap, _ := c.repo.FetchRefundShiftRecap(request, user)
	// 	refundShiftRecapCh <- refundShiftRecap
	// 	log.Info("Total time elapsed for FetchRefundShiftRecap: %v", time.Since(startTime))
	// }()

	go func() {
		startTime := time.Now()
		promoRecapProduk, _ := c.repo.FetchPromoRecapProduk(request, user)
		promoRecapProdukCh <- promoRecapProduk
		log.Info("Total time elapsed for FetchPromoRecapProduk: %v", time.Since(startTime))
	}()

	go func() {
		startTime := time.Now()
		promoRecapShift, _ := c.repo.FetchPromoRecapShift(request, user)
		promoRecapShiftCh <- promoRecapShift
		log.Info("Total time elapsed for FetchPromoRecapShift: %v", time.Since(startTime))
	}()

	// log.Info(" Wait for the completion signal...")
	// // Wait for the completion signal
	// <-done

	// Receive return values from channels
	salesRecapShift := <-salesRecapShiftCh
	salesDetailShift := <-salesDetailShiftCh
	payShift := <-payShiftCh
	taxShift := <-taxShiftCh
	voidShiftRecap := <-voidShiftRecapCh
	// refundShiftRecap := <-refundShiftRecapCh
	promoRecapProduk := <-promoRecapProdukCh
	promoRecapShift := <-promoRecapShiftCh

	totalPromo := promoRecapProduk.Promo + promoRecapShift.Promo
	totalVoucher := salesRecapShift.VoucherSales + taxShift.Voucher
	totDis := salesDetailShift.DiscountDetail + taxShift.Discount + salesRecapShift.DiscountSales - float64(voidShiftRecap.Dicount)
	sales := salesDetailShift.SubTotal - math.Abs(voidShiftRecap.SubVoid) - totalVoucher
	stlDis := sales - totDis - totalPromo
	totalMedia := payShift.Cash + payShift.Card + payShift.Piutang

	response := models.CloseShiftResponse{
		SubSalesDetail: salesDetailShift.SubTotal,
		Bill:           salesRecapShift.Bill,
		Pax:            salesRecapShift.Pax,
		DiscountSales:  salesRecapShift.DiscountSales,
		Cash:           payShift.Cash,
		Card:           payShift.Card,
		DutyMeals:      payShift.DutyMeals,
		Compliment:     payShift.Compliment,
		Free:           payShift.Free,
		Piutang:        payShift.Piutang,
		Service:        taxShift.Service,
		Tax:            taxShift.Tax,
		GrandRefund:    salesRecapShift.GrandTotalRefund, //refundShiftRecap.GrandTotal,
		Voucher:        totalVoucher,
		SubVoid:        math.Abs(voidShiftRecap.SubVoid),
		StlDis:         stlDis,
		Total:          stlDis + taxShift.Service + taxShift.Tax,
		TotalMedia:     totalMedia,
		TotalEntertain: totDis + totalVoucher + payShift.Compliment + payShift.Free + payShift.DutyMeals,
		Average:        totalMedia / float64(utils.TakeGreater(salesRecapShift.Pax, 1)),
		AverageBill:    totalMedia / float64(utils.TakeGreater(salesRecapShift.Bill, 1)),
		Sales:          sales,
		TotDis:         totDis,
		QtyVoid:        voidShiftRecap.QtyVoid,
		BillRefund:     salesRecapShift.BillRefund, //refundShiftRecap.BillRefund,
		Promo:          totalPromo,
	}

	log.Info("========== voidShiftRecap.Dicount: %v", voidShiftRecap.Dicount)
	log.Info("subtotal : %d, void: %d, totalVoucher: %d", salesDetailShift.SubTotal, math.Abs(voidShiftRecap.SubVoid), totalVoucher)

	return response, nil
}
