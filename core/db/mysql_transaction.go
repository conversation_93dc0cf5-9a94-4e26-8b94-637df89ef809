package db

import (
	"database/sql"
	"fmt"
	"runtime"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/log"
)

type Transaction interface {
	Insert(table string, data map[string]interface{}) sql.Result
	InsertBatch(table string, data []map[string]interface{}) sql.Result
	Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) sql.Result
	Delete(table string, whereCond string, whereParams ...interface{}) sql.Result
	Exec(query string, args ...interface{}) sql.Result
}

type TxFn func(tx Transaction) error

type SqlTx struct {
	Tx *sql.Tx
}

func (s SqlTx) Exec(query string, args ...interface{}) sql.Result {
	resp, err := s.Tx.Exec(query, args...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.<PERSON>rrorf("%s:%d query err: %v", fn, line, err))
		fmt.Printf("[transaction] %v \nquery : %s", err, getSQLRaw(query, args...))
		panic(err)
	}
	return resp
}

func (s SqlTx) Insert(table string, data map[string]interface{}) sql.Result {
	query, values := InsertQuery(table, data)
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Printf("[transaction] %v \nquery : %s \n", err, getSQLRaw(query, values...))
		panic(err)
	}
	return resp
}

func (s SqlTx) InsertBatch(table string, data []map[string]interface{}) sql.Result {
	// Ensure that there is data to insert
	if len(data) == 0 {
		return nil
	}

	// Create placeholders for each column in the query
	columns := make([]string, 0)
	// for col := range data[0] {
	// 	columns = append(columns, col)
	// }
	//get all unique column names
	columnCheck := make(map[string]bool)
	for _, row := range data {
		for col := range row {
			if !columnCheck[col] {
				columns = append(columns, col)
			}
			columnCheck[col] = true
		}
	}

	// Build the base query with placeholders
	query := "INSERT INTO " + table + " (" + strings.Join(columns, ",") + ") VALUES "
	placeholder := "(" + strings.Repeat("?,", len(columns)-1) + "?)"

	// Build the full query with multiple value sets
	var values []interface{}
	for _, row := range data {
		// values = append(values, getRowValues(row)...)
		//append value based on key/column order
		for _, key := range columns {
			values = append(values, row[key])
		}
		query += placeholder + ","
	}
	query = query[:len(query)-1] // Remove the trailing comma
	query += " "

	// Execute the batch insert query
	resp, err := s.Tx.Exec(query, values...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Printf("[transaction] %v \nquery : %s \n", err, getSQLRaw(query, values...))
		panic(err)
	}

	return resp
}

func (s SqlTx) Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) sql.Result {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		query += col + " = ?,"
		values = append(values, val)
	}
	query = query[:len(query)-1] + " WHERE " + whereCond

	for _, param := range whereParams {
		values = append(values, param)
	}

	res, err := s.Tx.Exec(query, values...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Printf("[transaction] %v \nquery : %s", err, getSQLRaw(query, values...))
		panic(fmt.Sprintf("%v \nquery : %s", err, getSQLRaw(query, values...)))
	}

	return res
}

func (s SqlTx) Delete(table string, whereCond string, whereParams ...interface{}) sql.Result {
	if whereCond == "" || len(whereParams) == 0 {
		fmt.Println("delete with no params skipped...")
		return nil
	}

	query := "DELETE FROM " + table + " WHERE " + whereCond

	res, err := s.Tx.Exec(query, whereParams...)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%s:%d query err: %v", fn, line, err))
		fmt.Println("Executing Query Error : ", getSQLRaw(query, whereParams...))
	}

	return res
}

func WithTransaction(fn TxFn) (err error) {
	tx, err := GetConn().Begin()
	if err != nil {
		fmt.Println("failed to start transaction...")
		return err
	}
	sqlTx := SqlTx{tx}

	defer func() {
		if p := recover(); p != nil {
			sqlTx.Tx.Rollback()
			err = fmt.Errorf("%v", p)
		} else if err != nil {
			// something went wrong, rollback
			sqlTx.Tx.Rollback()
		} else {
			// all good, commit
			sqlTx.Tx.Commit()
		}
	}()
	err = fn(sqlTx)
	return err
}

// getRowValues extracts values from a map and returns them as a slice
func getRowValues(row map[string]interface{}) []interface{} {
	values := make([]interface{}, 0, len(row))
	for _, val := range row {
		values = append(values, val)
	}
	return values
}
