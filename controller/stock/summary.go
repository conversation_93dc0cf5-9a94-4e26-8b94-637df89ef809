package stock

import (
	"bytes"
	"encoding/json"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"html/template"
	"io/ioutil"
	"math"
	"os"
	"strings"
	"sync"
	"time"
)

var reportOnProgress bool

func TestStockReport(ctx *fasthttp.RequestCtx) {
	adminId := ctx.UserValue("adminId")
	email := ctx.UserValue("email")

	if reportOnProgress {
		_ = json.NewEncoder(ctx).Encode("harap antri, kami sedang memproses report...")
		return
	}

	reportOnProgress = true

	if email == nil {
		if adminId == "10" {
			email = "<EMAIL>"
		} else {
			admin, err := db.Query("select email from admin where admin_id = ?", adminId)
			if err != nil {
				_ = json.NewEncoder(ctx).Encode(err)
				return
			}
			email = admin["email"]
		}
	}

	go SendStockReport(map[string]interface{}{
		"admin_id": adminId,
		"email":    email,
	})

	_ = json.NewEncoder(ctx).Encode(fmt.Sprintf("report akan dikirim ke email '%s', tunggu maksimal 5 menit", email))
}

func SendDailyStockReport() {
	admin, err := db.Query("select email from admin where admin_id = 7")
	log.IfError(err)

	if admin["email"] == "<EMAIL>" {
		go SendStockReport(map[string]interface{}{
			"admin_id": 7,
			"email":    "<EMAIL>",
		})
	}

	admin, err = db.Query("select email from admin where admin_id = 10")
	log.IfError(err)
	if admin["email"] == "<EMAIL>" {
		go SendStockReport(map[string]interface{}{
			"admin_id": 10,
			"email":    "<EMAIL>",
		})
	}
}

func SendStockReport(admin map[string]interface{}) {
	log.Info("calculating stock...")

	adminData, err := db.Query("select business_name, name from admin where admin_id = ? ", admin["admin_id"])
	log.IfError(err)

	if len(adminData) == 0 {
		log.Info("no admin found with id: %v", admin["admin_id"])
		reportOnProgress = false
		return
	}

	bigbang := time.Now()

	requestTimeReport := ""
	start := time.Now()
	//result := CalculateStock(admin)
	result := GetStock(admin)
	fmt.Println(len(result), "products")
	requestTimeReport += fmt.Sprintf(" - calculation took : %v\n", time.Since(start))

	if _, err := os.Stat("temp"); os.IsNotExist(err) {
		log.IfError(os.Mkdir("temp", os.ModePerm))
	}

	log.Info("generating report...")
	start = time.Now()
	summary, summaryFile := generateReportSummary(result)
	requestTimeReport += fmt.Sprintf(" - generating reports (summary) took : %v\n", time.Since(start))

	start = time.Now()
	reportDetailFiles := generateReportAllOutlet(result)
	requestTimeReport += fmt.Sprintf(" - generating reports (all) took : %v\n", time.Since(start))

	allFiles := make([]string, 0)
	allFiles = append(allFiles, reportDetailFiles...)
	allFiles = append(allFiles, summaryFile)

	log.Info("sending report...")
	start = time.Now()
	utils.SendEmailZoho(models.ZohoMail{
		To:          utils.ToString(admin["email"]),
		Subject:     fmt.Sprintf("STOCK ALERT REPORT of %s", adminData["business_name"]),
		Content:     summary,
		Attachments: allFiles,
	})

	requestTimeReport += fmt.Sprintf(" - sending email took : %v\n", time.Since(start))

	for _, file := range allFiles {
		utils.DeleteFile(file)
	}

	reportOnProgress = false
	log.Info("stock calculation took : %v  \ndetail : \n%s", time.Since(bigbang), requestTimeReport)
}

func CreateStockReport(admin map[string]interface{}) {
	result := CalculateStock(admin)

	stockAlert, fileName := generateReportSummary(result)

	utils.SendEmailZoho(models.ZohoMail{
		To:          utils.ToString(admin["email"]),
		Subject:     "Stock Report",
		Content:     stockAlert,
		Attachments: []string{fileName},
	})
}

func CreateStockReportDetail(admin map[string]interface{}) {
	result := CalculateStock(admin)
	fileNames := generateReportAllOutlet(result)
	log.Info("files : %v", fileNames)
	utils.SendEmailZoho(models.ZohoMail{
		To:          utils.ToString(admin["email"]),
		Subject:     "Stock Report",
		Content:     "kami kirimkan datam bentuk attachment",
		Attachments: fileNames,
	})

	reportOnProgress = false
}

func generateReportSummary(result []map[string]interface{}) (string, string) {
	resultJson, _ := json.Marshal(result)
	fmt.Println("result : ", string(resultJson))

	products := make([]models.Product, 0)
	outlets := make([]string, 0)
	outletIdx := make(map[interface{}]int)

	//find unused/used outlet
	for _, data := range result {
		stocks := data["stock"].([]map[string]interface{})
		for _, stock := range stocks {
			found := false
			for _, name := range outlets {
				if name == stock["outlet_name"] {
					found = true
					break
				}
			}
			if !found {
				outlets = append(outlets, utils.ToString(stock["outlet_name"]))
				outletIdx[stock["outlet_id"]] = len(outlets) - 1
			}
		}
	}

	for _, data := range result {
		stocks := data["stock"].([]map[string]interface{})
		stockStr := make([]models.Stock, len(outlets))
		for _, stock := range stocks {
			color := "black"
			if stock["status"] == "need" {
				color = "red"
			} else if stock["status"] == "exceeded" {
				color = "yellowgreen"
			}

			//stockStr = append(stockStr, models.Stock{
			//	Qty:   utils.FloatFormat(stock["closing"]),
			//	Color: color,
			//})

			//add data base on index
			stockStr[outletIdx[stock["outlet_id"]]] = models.Stock{
				Qty:   utils.FloatFormat(stock["closing"]),
				Color: color,
			}
		}
		products = append(products, models.Product{
			ProductName: utils.ToString(data["product_name"]),
			Stocks:      stockStr,
		})
	}

	fmt.Println("outlets ", outlets)
	fmt.Println("products to report : ", len(products))

	htmlTemplate := "config/template/stock_alert_template.html"
	tmpl, err := utils.ReadFile(htmlTemplate)
	if utils.CheckErr(err) {
		return "", ""
	}

	template, err := template.New("stock_alert").Parse(tmpl)
	if utils.CheckErr(err) {
		fmt.Println("error - ", err)
	}

	data := models.StockAlert{
		Outlets:  outlets,
		Products: products,
	}

	var stockAlert bytes.Buffer
	err = template.Execute(&stockAlert, data)
	utils.CheckErr(err)

	fileName := fmt.Sprintf("temp/stock_alert_summary_%d.html", time.Now().UnixNano())
	err = ioutil.WriteFile(fileName, stockAlert.Bytes(), 0644)
	utils.CheckErr(err)

	////convert html to pdf
	//client := pdfcrowd.NewHtmlToPdfClient("annasdev", "95dcd36c38e275ee1ed6dee7829da5f7")
	//client.SetOrientation("landscape")
	//// run the conversion and write the result to a file
	//pdfFile := fmt.Sprintf("temp/stock_alert_%d.pdf", time.Now().UnixNano())
	//err = client.ConvertFileToFile(fileName, pdfFile)
	//utils.CheckErr(err)

	return stockAlert.String(), fileName
}

func generateReportAllOutlet(result []map[string]interface{}) []string {
	stockPerOutlets := make(map[string][]models.Product)
	stockTypes := []string{"opname", "purchase", "retur", "sales", "refund", "spoil", "transfer", "production", "closing"}
	infoHeaders := []string{"outlet", "category", "sub_category", "unit"}

	//opname + purchase + transferIn - sales - breakDown - retur - spoil - transferOut
	for _, data := range result {
		stocks := data["stock"].([]map[string]interface{})
		for _, stock := range stocks {
			if stock["closing"] == "-" {
				continue
			}

			if stockPerOutlets[utils.ToString(stock["outlet_name"])] == nil {
				stockPerOutlets[utils.ToString(stock["outlet_name"])] = make([]models.Product, 0)
			}

			stockQtys := make([]models.Stock, 0)
			for _, key := range stockTypes {
				stockQtys = append(stockQtys, models.Stock{
					//Qty:   fmt.Sprintf("%.0f", stock[key]),
					Qty:   utils.FloatFormat(stock[key]),
					Color: "black",
				})
			}

			stockPerOutlets[utils.ToString(stock["outlet_name"])] = append(stockPerOutlets[utils.ToString(stock["outlet_name"])], models.Product{
				ProductName: utils.ToString(data["product_name"]),
				Unit:        utils.ToString(data["unit"]),
				Category:    utils.ToString(data["category"]),
				SubCategory: utils.ToString(data["sub_category"]),
				Stocks:      stockQtys,
			})
		}
	}

	//resultJson, _ := json.Marshal(stockPerOutlets)
	//fmt.Println(string(resultJson))

	htmlTemplate := "config/template/stock_alert_template.html"
	tmpl, err := utils.ReadFile(htmlTemplate)
	if utils.CheckErr(err) {
		return nil
	}

	templateReport, err := template.New("stock_alert").Parse(tmpl)
	if log.IfError(err) {
		fmt.Println("error - ", err)
	}

	f := excelize.NewFile()
	fileNames := make([]string, 0)
	for key, value := range stockPerOutlets {
		data := models.StockAlert{
			Outlets:  stockTypes,
			Products: value,
			Title:    key,
		}

		var stockAlert bytes.Buffer
		err = templateReport.Execute(&stockAlert, data)
		utils.CheckErr(err)

		//r := strings.NewReplacer(" ", "_",
		//	"&", "_")

		//fileName := fmt.Sprintf("temp/stock_alert_%s_%d.html", r.Replace(key), time.Now().UnixNano())

		//err = ioutil.WriteFile(fileName, stockAlert.Bytes(), 0644)
		//log.IfError(err)
		//fileNames = append(fileNames, fileName)

		//pdfg, err := wkhtmltopdf.NewPDFGenerator()
		//if err == nil {
		//	pdfg.AddPage(wkhtmltopdf.NewPageReader(strings.NewReader(stockAlert.String())))
		//	// Create PDF document in internal buffer
		//	err = pdfg.Create()
		//	log.IfError(err)
		//
		//	// Write buffer contents to file on disk
		//	fileName = fmt.Sprintf("temp/stock_alert_%s_%d.pdf", r.Replace(key), time.Now().UnixNano())
		//	err = pdfg.WriteFile(fileName)
		//	log.IfError(err)
		//	//fileNames = append(fileNames, fileName)
		//}

		//create excel file
		index := f.NewSheet(key)
		//write header
		f.SetCellValue(key, "A2", "NO")
		f.SetCellValue(key, "B2", "PRODUCT")

		headers := make([]string, 0)
		headers = append(headers, infoHeaders...)
		headers = append(headers, stockTypes...)

		for i, header := range headers {
			f.SetCellValue(key, fmt.Sprintf("%s2", utils.Alphabet[i+2]), strings.ToUpper(strings.Replace(header, "_", " ", 1)))
		}

		writeExcel(f, key, value)
		f.SetActiveSheet(index)
	}

	f.DeleteSheet("Sheet1")
	fileName := fmt.Sprintf("temp/stock_alert_all_outlet-%s.xlsx", time.Now().Format("200601021504052006"))
	if err := f.SaveAs(fileName); err != nil {
		fmt.Println(err)
	}

	fileNames = append(fileNames, fileName)

	log.Info("files : %v", fileNames)
	return fileNames
}

func writeExcel(f *excelize.File, sheetName string, produts []models.Product) {
	for i, product := range produts {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", i+3), i+1) //NUMBER
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", i+3), product.ProductName)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", i+3), sheetName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", i+3), product.Category)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", i+3), product.SubCategory)
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", i+3), product.Unit)

		for j, stock := range product.Stocks {
			f.SetCellValue(sheetName, fmt.Sprintf("%s%d", utils.Alphabet[j+6], i+3), stock.Qty)
		}
	}
}

func GetStock(admin map[string]interface{}) []map[string]interface{} {
	result := make([]map[string]interface{}, 0)

	sql := `select ss.*,
       p.product_id,
       concat(trim(p.name), coalesce(concat(' (', pdv.variant_name, ')'), '')) as name,
       o.name                                                                  as outlet_name,
       u.name                                                                  as unit,
       ps.name                                                                 as sub_category,
       pc.name                                                                 as category,
       o.outlet_id,
       pdv.variant_id,
       concat(p.product_id, coalesce(concat('-', pdv.variant_id), ''))         as unique_id
from stock_summary ss
         join products_detail pd on ss.product_detail_fkid = pd.product_detail_id
         left join products_detail_variant pdv on pd.variant_fkid = pdv.variant_id
         join outlets o on pd.outlet_fkid = o.outlet_id
         join products p on pd.product_fkid = p.product_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
         join products_category pc on p.product_category_fkid = pc.product_category_id
         join unit u on p.unit_fkid = u.unit_id
where p.admin_fkid = ?
  and p.stock_management = 1
  and p.data_status = 'on'
  and pd.data_status = 'on'
order by name, p.product_id, o.name  `

	data, err := db.QueryArray(sql, admin["admin_id"])
	log.IfError(err)

	stocks := make([]map[string]interface{}, 0)
	for i, sales := range data {
		stocks = append(stocks, map[string]interface{}{
			"outlet_id":    sales["outlet_id"],
			"outlet_name":  sales["outlet_name"],
			"closing":      utils.ToFloat(sales["closing"]),
			"status":       "",
			"need":         "",
			"opname":       utils.ToFloat(sales["open"]),
			"purchase":     utils.ToFloat(sales["purchase"]),
			"transfer_in":  utils.ToFloat(sales["transfer_in"]),
			"sales":        utils.ToFloat(sales["sales"]),
			"refund":       utils.ToFloat(sales["refund"]),
			"retur":        utils.ToFloat(sales["retur"]),
			"spoil":        utils.ToFloat(sales["spoil"]),
			"transfer_out": utils.ToFloat(sales["transfer_out"]),
			"transfer":     utils.ToFloat(sales["transfer_in"]) - utils.ToFloat(sales["transfer_out"]),
			"production":   float32(0),
		})

		if i == len(data)-1 || sales["unique_id"] != data[i+1]["unique_id"] {
			result = append(result, map[string]interface{}{
				"product_id":   sales["product_id"],
				"product_name": sales["name"],
				"unit":         sales["unit"],
				"category":     sales["category"],
				"sub_category": sales["sub_category"],
				"stock":        stocks,
			})
			stocks = make([]map[string]interface{}, 0)
		}
	}

	return result
}

func CalculateStock(admin map[string]interface{}) []map[string]interface{} {
	outletChan := make(chan []map[string]interface{}, 1)
	productsChan := make(chan []map[string]interface{}, 1)
	stockSalesChan := make(chan []map[string]interface{}, 1)
	stockBreakdownChan := make(chan []map[string]interface{}, 1)
	stockReturChan := make(chan []map[string]interface{}, 1)
	stockSpoilChan := make(chan []map[string]interface{}, 1)
	stockTransferInChan := make(chan []map[string]interface{}, 1)
	stockTransferOutChan := make(chan []map[string]interface{}, 1)
	stockOpnameChan := make(chan []map[string]interface{}, 1)
	stockPurchaseChan := make(chan []map[string]interface{}, 1)
	stockEstimationChan := make(chan []map[string]interface{}, 1)

	day := strings.ToLower(time.Now().Weekday().String())
	timeStart := time.Now()

	//var wg sync.WaitGroup

	//wg.Add(1)
	//go db.QueryArrayGo(&wg, outletChan, "select outlet_id, name from outlets where admin_fkid = ?", admin["admin_id"])
	go db.QueryArrayChannel(outletChan, "select outlet_id, name from outlets where admin_fkid = ?", admin["admin_id"])

	//wg.Add(1)
	sql := `
select product_id, p.name, u.name as unit, pc.name as category, ps.name as sub_category
from products p
         join unit u on p.unit_fkid = u.unit_id
         join products_category pc on p.product_category_fkid = pc.product_category_id
         join products_subcategory ps on p.product_subcategory_fkid = ps.product_subcategory_id
where stock_management = 1
  and p.data_status = 'on'
  and p.admin_fkid = ?
order by p.name `
	//sql := "select product_id, name  from products where data_status = 'on' and admin_fkid = ? order by name"
	//go db.QueryArrayGo(&wg, productsChan, sql, admin["admin_id"])
	go db.QueryArrayChannel(productsChan, sql, admin["admin_id"])

	sql = `
select pd.product_fkid as product_id, se.product_detail_fkid,min_stock, max_stock, pd.outlet_fkid
from stock_estimation se
join day_category dc on se.day_category_fkid = dc.day_category_id
join products_detail pd on se.product_detail_fkid = pd.product_detail_id
where ? is not null and dc.admin_fkid = ? `
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockEstimationChan, sql, day, admin["admin_id"])
	go db.QueryArrayChannel(stockEstimationChan, sql, day, admin["admin_id"])

	/// ----- STOCK IN  -------
	//#opanem
	sql = `
select pd.product_fkid as product_id,so.product_detail_fkid, opname as stock_in, pd.outlet_fkid
from stock_opname so
join products_detail pd on so.product_detail_fkid = pd.product_detail_id
where so.admin_fkid = ? and so.opname_id in (select max(opname_id) from stock_opname group by product_detail_fkid);`
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockOpnameChan, sql, admin["admin_id"])
	go db.QueryArrayChannel(stockOpnameChan, sql, admin["admin_id"])

	//#purchase
	sql = `
select pd.product_fkid as product_id, pp.products_fkid as product_detail_fkid, sum(pc.qty_arive) as stock_in, pd.outlet_fkid
from purchase_confrim pc
  join purchase_products pp on pc.purchase_product_fkid = pp.purchase_products_id
  left join
     (select max(time_created) last_opname, product_detail_fkid from stock_opname where admin_fkid = ? group by product_detail_fkid) op
  on op.product_detail_fkid=pp.products_fkid
join purchase p on pp.purchase_fkid = p.purchase_id
join products_detail pd on pd.product_detail_id=pp.products_fkid
where pc.date_created > coalesce(op.last_opname, 0) and p.admin_fkid = ?
group by pp.products_fkid`
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockPurchaseChan, sql, admin["admin_id"], admin["admin_id"])
	go db.QueryArrayChannel(stockPurchaseChan, sql, admin["admin_id"], admin["admin_id"])

	//#transfer - in
	sql = `
select pd.product_fkid as product_id, tp.product_detail_des_fkid, sum(qty_confirm) as stock_in, pd.outlet_fkid
from transfer_confirm tc
         join transfer_products tp on tc.transfer_product_fkid = tp.transfer_product_id
         join products_detail pd on tp.product_detail_des_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = tp.product_detail_des_fkid
where tc.data_created > coalesce(op.last_opname, 0)
  and p.admin_fkid = ?
  #and p.stock_management = 1
  and p.data_status = 'on'
  and pd.data_status = 'on'
group by tp.product_detail_des_fkid `
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockTransferInChan, sql, admin["admin_id"], admin["admin_id"])
	go db.QueryArrayChannel(stockTransferInChan, sql, admin["admin_id"], admin["admin_id"])

	/// ----- STOCK OUT ------
	//#sales
	sql = `
select p.product_id,
       sd.product_detail_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as stock_out,
       pd.outlet_fkid
from sales_detail sd
         join sales s on s.sales_id = sd.sales_fkid
         join products_detail pd on sd.product_detail_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         left join sales_void sv on sd.sales_detail_id = sv.sales_detail_fkid
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = sd.product_detail_fkid
where sd.time_created > coalesce(op.last_opname, 0)
  and p.data_status = 'on'
  #and p.stock_management = 1
  and pd.data_status = 'on' 
  and p.admin_fkid = ?
  and s.status = 'Success'
group by sd.product_detail_fkid `
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockSalesChan, sql, admin["admin_id"], admin["admin_id"])
	//go db.QueryArrayChannel(stockSalesChan, sql, admin["admin_id"], admin["admin_id"])

	//#breakdown
	sql = `
select min(p.product_id)   as product_id,
       sb.product_detail_fkid,
       sum(qty_total)      as stock_out,
       min(pd.outlet_fkid) as outlet_fkid
from sales_breakdown sb
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = sb.product_detail_fkid
         join sales_detail sd on sb.sales_detail_fkid = sd.sales_detail_id
         join sales s on s.sales_id = sd.sales_fkid
         join products p on sd.product_fkid = p.product_id
         join products_detail pd on sd.product_detail_fkid = pd.product_detail_id
where sd.time_created > coalesce(op.last_opname, 0)
  and p.data_status = 'on'
  #and p.stock_management = 1
  and pd.data_status = 'on'
  and s.status = 'Success'
  and p.admin_fkid = ?
group by sb.product_detail_fkid `
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockBreakdownChan, sql, admin["admin_id"], admin["admin_id"])
	//go db.QueryArrayChannel(stockBreakdownChan, sql, admin["admin_id"], admin["admin_id"])

	//#retur
	sql = `
select pd.product_fkid as product_id, pd.product_detail_id, sum(rp.qty_retur) as stock_out, pd.outlet_fkid
from retur_products rp
         join purchase_products pp on pp.purchase_products_id = rp.purchase_product_fkid
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = pp.products_fkid
         join products_detail pd on pd.product_detail_id = pp.products_fkid
where rp.data_created > coalesce(op.last_opname, 0)
  and rp.admin_fkid = ?
group by pp.products_fkid `
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockReturChan, sql, admin["admin_id"], admin["admin_id"])
	go db.QueryArrayChannel(stockReturChan, sql, admin["admin_id"], admin["admin_id"])

	//#spoil
	sql = `
select pd.product_fkid as product_id, pd.product_detail_id, sum(qty) as stock_out, outlet_fkid
from spoils s
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = s.product_detail_fkid
         join products_detail pd on s.product_detail_fkid = pd.product_detail_id
where time_created > coalesce(op.last_opname, 0)
  and admin_fkid = ?
group by s.product_detail_fkid`
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockSpoilChan, sql, admin["admin_id"], admin["admin_id"])
	go db.QueryArrayChannel(stockSpoilChan, sql, admin["admin_id"], admin["admin_id"])

	//#transfer - out
	sql = `
select pd.product_fkid as product_id, tp.product_detail_fkid, sum(qty_confirm) as stock_out, pd.outlet_fkid
from transfer_confirm tc
         join transfer_products tp on tc.transfer_product_fkid = tp.transfer_product_id
         join products_detail pd on tp.product_detail_fkid = pd.product_detail_id
         join products p on pd.product_fkid = p.product_id
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname
      where admin_fkid = ?
      group by product_detail_fkid) op
     on op.product_detail_fkid = tp.product_detail_fkid
where tc.data_created > coalesce(op.last_opname, 0)
  and p.admin_fkid = ?
  #and p.stock_management = 1
  and p.data_status = 'on'
  and pd.data_status = 'on'
group by tp.product_detail_fkid`
	//wg.Add(1)
	//go db.QueryArrayGo(&wg, stockTransferOutChan, sql, admin["admin_id"], admin["admin_id"])
	go db.QueryArrayChannel(stockTransferOutChan, sql, admin["admin_id"], admin["admin_id"])

	//wg.Wait()

	//sales - per product
	sqlSales := `
select min(sd.product_fkid)              as product_id,
       sd.product_detail_fkid,
       sum(sd.qty - coalesce(sv.qty, 0)) as stock_out,
       min(s.outlet_fkid)                as outlet_fkid,
       s.status                          as status
from sales_detail sd
         join sales s on s.sales_id = sd.sales_fkid
         left join (select sum(qty) as qty, sales_detail_fkid from sales_void group by sales_detail_fkid) sv
                   on sv.sales_detail_fkid = sd.sales_detail_id
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname so
               join products_detail pd on so.product_detail_fkid = pd.product_detail_id
      where pd.product_fkid in [IN]
      group by product_detail_fkid) op
     on op.product_detail_fkid = sd.product_detail_fkid
where sd.time_created > coalesce(op.last_opname, 0)
  and sd.product_fkid in [IN]
group by sd.product_detail_fkid, s.status `

	//get breakdown - per product
	sqlBreakDown := `
select min(pd.product_fkid)         as product_id,
       sb.product_detail_fkid,
       sum(if(sb.sales_status = 'success', if(sb.status = 'success', abs(qty_total), abs(qty_total) * -1),
              abs(qty_total) * -1)) as stock_out,
       min(pd.outlet_fkid)          as outlet_fkid,
       sb.sales_status              as status
from sales_breakdown sb
         left join
     (select max(time_created) last_opname, product_detail_fkid
      from stock_opname so
               join products_detail pd on so.product_detail_fkid = pd.product_detail_id
      where pd.product_fkid in [IN]
      group by product_detail_fkid) op
     on op.product_detail_fkid = sb.product_detail_fkid
         join products_detail pd on sb.product_detail_fkid = pd.product_detail_id
where sb.time_created > coalesce(op.last_opname, 0)
  and pd.product_fkid in [IN]
group by sb.product_detail_fkid, sb.sales_status `

	salesDone := make(chan bool)
	salesArray := make([]map[string]interface{}, 0)
	salesRefundArray := make([]map[string]interface{}, 0)
	go func() {
		for {
			data, more := <-stockSalesChan
			if more {
				for _, sale := range data {
					if sale["status"] == "Refund" {
						salesRefundArray = append(salesRefundArray, sale)
					} else if sale["status"] == "Success" {
						salesArray = append(salesArray, sale)
					}
				}
			} else {
				fmt.Println("got all sales.. TOTAL ", len(salesArray))
				salesDone <- true
				return
			}
		}
	}()

	breakDownDone := make(chan bool)
	breakDownArray := make([]map[string]interface{}, 0)
	breakDownRefundArray := make([]map[string]interface{}, 0)
	go func() {
		for {
			data, more := <-stockBreakdownChan
			if more {
				breakDownArray = append(breakDownArray, data...)
				for _, breakDown := range breakDownArray {
					if breakDown["status"] == "Success" {
						breakDownArray = append(breakDownArray, breakDown)
					} else if breakDown["status"] == "Refund" {
						breakDownRefundArray = append(breakDownRefundArray, breakDown)
					}
				}
			} else {
				fmt.Println("got all breakdown.. TOTAL ", len(breakDownArray))
				breakDownDone <- true
				return
			}
		}
	}()

	//process data
	products := <-productsChan
	fmt.Printf("got product data in : %s | total %d \n", time.Since(timeStart), len(products))

	wg := sync.WaitGroup{}
	max := 50
	for i := 0; i < len(products); i += max {
		params := make([]interface{}, 0)
		//params = append(params, admin["admin_id"])
		totalIds := 0
		for j := i; j < i+max && j < len(products); j++ {
			params = append(params, utils.ToInt(products[j]["product_id"]))
			totalIds++
		}

		//make it double
		params = append(params, params...)

		query := strings.Replace(sqlSales, "[IN]", "("+strings.Repeat("?, ", totalIds-1)+"?)", -1)

		fmt.Println("query sales...", i)
		wg.Add(1)
		go db.QueryArrayGo(&wg, stockSalesChan, query, params...)

		query = strings.Replace(sqlBreakDown, "[IN]", "("+strings.Repeat("?, ", totalIds-1)+"?)", -1)
		wg.Add(1)
		go db.QueryArrayGo(&wg, stockBreakdownChan, query, params...)
	}

	wg.Wait()
	fmt.Println("wait finish...")
	close(stockSalesChan)
	close(stockBreakdownChan)
	<-salesDone
	<-breakDownDone

	outlets := <-outletChan
	stockOpnameArray := <-stockOpnameChan
	returArray := <-stockReturChan
	spoilArray := <-stockSpoilChan
	transferInArray := <-stockTransferInChan
	transferOutArray := <-stockTransferOutChan
	purchaseArray := <-stockPurchaseChan
	estimationArray := <-stockEstimationChan
	//salesArray := <-stockSalesChan
	//fmt.Printf("got sales data in : %s \n", time.Since(timeStart))
	//breakDownArray := <-stockBreakdownChan
	//fmt.Printf("got breakdown data in : %s \n", time.Since(timeStart))

	fmt.Printf("got all data in : %s \n", time.Since(timeStart))

	//stockOpnameMap := formatDatStock(stockOpname)

	result := make([]map[string]interface{}, 0)

	unReportedProducts := ""
	for _, product := range products {
		stocks := make([]map[string]interface{}, 0)
		used := 0
		for _, outlet := range outlets {
			estimation := FindItem(estimationArray, "product_id", product["product_id"], "outlet_fkid", outlet["outlet_id"])
			opname := findStock(stockOpnameArray, "stock_in", outlet["outlet_id"], product["product_id"])
			purchase := findStock(purchaseArray, "stock_in", outlet["outlet_id"], product["product_id"])
			sales := findStock(salesArray, "stock_out", outlet["outlet_id"], product["product_id"])
			salesRefund := findStock(salesRefundArray, "stock_out", outlet["outlet_id"], product["product_id"])
			breakDown := findStock(breakDownArray, "stock_out", outlet["outlet_id"], product["product_id"])
			breakDownRefund := findStock(breakDownRefundArray, "stock_out", outlet["outlet_id"], product["product_id"])
			retur := findStock(returArray, "stock_out", outlet["outlet_id"], product["product_id"])
			spoil := findStock(spoilArray, "stock_out", outlet["outlet_id"], product["product_id"])
			transferIn := findStock(transferInArray, "stock_in", outlet["outlet_id"], product["product_id"])
			transferOut := findStock(transferOutArray, "stock_out", outlet["outlet_id"], product["product_id"])

			count := opname + purchase + sales + salesRefund + breakDown + breakDownRefund + retur + spoil + transferIn + transferOut
			closing := opname + purchase + transferIn - sales - breakDown - retur - spoil - transferOut

			//fmt.Println("\n=====> ", product["name"], "| Outlet ===> ", outlet["name"], "--", product["product_id"])
			//fmt.Printf("opname : %f | sales : %f | breakdown : %f | retur : %f | spoil : %f | transfer in : %f | transfer out : %f | estimation : %v | ",
			//	opname, sales, breakDown, retur, spoil, transferIn, transferOut, estimation)

			if count > 0 {
				status := "safe"
				need := float32(0)
				if estimation != nil {
					minStock := utils.ToFloat32(estimation["min_stock"])
					maxStock := utils.ToFloat32(estimation["max_stock"])
					if closing < minStock {
						status = "need"
						need = minStock - closing
					} else if closing > maxStock {
						status = "exceeded"
						used++
					}
				} else {
					if closing <= 0 {
						status = "need"
						need = 1 - closing //1 : is default minimum stock estimation
					}
				}

				//only send report if need is more than 1
				//if need > 0 {
				//	used++
				//}

				//only send report only if the product ever been sold, or transfered, etc
				if count > 0 {
					used++
				}

				//fmt.Printf("status : %s | need : %f \n", status, need)

				stocks = append(stocks, map[string]interface{}{
					"outlet_id":    outlet["outlet_id"],
					"outlet_name":  outlet["name"],
					"closing":      closing,
					"status":       status,
					"need":         need,
					"opname":       opname,
					"purchase":     purchase,
					"transfer_in":  transferIn,
					"sales":        sales + breakDown + (utils.ToFloat32(math.Abs(float64(salesRefund))) + utils.ToFloat32(math.Abs(float64(breakDownRefund)))),
					"refund":       math.Abs(float64(salesRefund)) + math.Abs(float64(breakDownRefund)),
					"retur":        retur,
					"spoil":        spoil,
					"transfer_out": transferOut,
					"transfer":     transferIn - transferOut,
					"production":   float32(0),
				})
			} else {
				stocks = append(stocks, map[string]interface{}{
					"outlet_id":   outlet["outlet_id"],
					"outlet_name": outlet["name"],
					"closing":     "-",
				})
			}
		}

		if used > 0 {
			result = append(result, map[string]interface{}{
				"product_id":   product["product_id"],
				"product_name": product["name"],
				"unit":         product["unit"],
				"category":     product["category"],
				"sub_category": product["sub_category"],
				"stock":        stocks,
			})
		} else {
			unReportedProducts += utils.ToString(product["name"]) + ", "
		}
	}

	fmt.Println("unreported : ", unReportedProducts)
	fmt.Println("Done... total to return ", len(result))
	//resultJson, _ := json.Marshal(result)
	//fmt.Println("result : ", string(resultJson))
	return result
}

func findStock(dataArray []map[string]interface{}, key string, outletId interface{}, productId interface{}) float32 {
	var result float32 = 0
	for _, data := range dataArray {
		if data["outlet_fkid"] == outletId && data["product_id"] == productId {
			result = utils.ToFloat32(data[key])
			break
		}
	}
	return result
}

func FindItem(dataArray []map[string]interface{}, conditions ...interface{}) map[string]interface{} {
	for _, data := range dataArray {
		found := 0
		for i := 0; i <= (len(conditions) / 2); i += 2 {
			if data[utils.ToString(conditions[i])] == conditions[i+1] {
				found++
			}
		}
		if found == (len(conditions) / 2) {
			return data
		}
	}
	return nil
}

func ManualStockOpname(adminId, outletId int) {
	sql := `
select pd.product_detail_id
from products_detail pd
         join products p on p.product_id = pd.product_fkid
where p.data_status = 'on'
  and pd.data_status = 'on'
  and p.stock_management = 1
  and pd.outlet_fkid = ?
`
	products, err := db.QueryArray(sql, outletId)
	if log.IfError(err) {
		return
	}

	log.Info("total products : %d", len(products))

	lastPercentage := 0
	ids := make([]string, 0)
	for i, product := range products {
		resp, err := db.Insert("stock_opname", map[string]interface{}{
			"product_detail_fkid": product["product_detail_id"],
			"opname":              0,
			"open":                0,
			"purchase":            0,
			"retur":               0,
			"refund":              0,
			"sales":               0,
			"spoil":               0,
			"transfer":            0,
			"closing":             0,
			"production":          0,
			"need":                0,
			"alert":               "[NO ALERT]",
			"data_created":        "2020-10-30 03:00:00",
			"time_created":        1604001600000,
			"time_modified":       1604001600000,
			"data_status":         "on",
			"admin_fkid":          adminId,
			"employee_fkid":       365,
		})
		if log.IfError(err) {
			break
		}

		id, _ := resp.LastInsertId()
		ids = append(ids, utils.ToString(id))

		percentage := float64(i) / float64(len(products)) * float64(100)
		if int(percentage) > lastPercentage {
			fmt.Print(int(percentage), "%, ")
		}
		lastPercentage = int(percentage)
	}

	fmt.Println("")
	log.Info("ids %d (%d) : %v", adminId, outletId, strings.Join(ids, ","))
}

func CheckDiffStock(ctx *fasthttp.RequestCtx) {
	fmt.Printf("CheckDiffStock - receive body : %s", string(ctx.PostBody()))
	var report []map[string]interface{}
	log.IfError(json.Unmarshal(ctx.PostBody(), &report))

	stocks := make(map[int]map[string]interface{})
	prodIds := make([]interface{}, 0)
	for _, row := range report {
		prodIds = append(prodIds, row["pd_id"])
		stocks[utils.ToInt(row["pd_id"])] = map[string]interface{}{
			"breakdown":    int(utils.ToFloat(row["breakdown"])),
			"open":         int(utils.ToFloat(row["open"])),
			"sales":        int(utils.ToFloat(row["sales"])),
			"spoil":        int(utils.ToFloat(row["spoil"])),
			"closing":      int(utils.ToFloat(row["closing"])),
			"retur":        int(utils.ToFloat(row["retur"])),
			"refund":       int(utils.ToFloat(row["refund"])),
			"purchase":     int(utils.ToFloat(row["purchase"])),
			"transfer":     int(utils.ToFloat(row["transfer"])),
			"product_name": row["ingridient_name"],
		}
	}

	sql := "select * from stock_summary where product_detail_fkid in " + "(" + strings.Repeat("?, ", len(prodIds)-1) + "?)"
	data, err := db.QueryArray(sql, prodIds...)
	log.IfError(err)

	fmt.Printf("total ids: %d, total data %d\n\n", len(prodIds), len(data))
	msg := ""
	for _, dbStock := range data {
		webStock := stocks[utils.ToInt(dbStock["product_detail_fkid"])]

		reportErr := ""
		//transfer_in
		//transfer_out
		stockTypes := []string{"open", "purchase", "sales", "refund", "spoil", "retur", "closing"}
		for _, tp := range stockTypes {
			if int(utils.ToFloat(dbStock[tp])) != utils.ToInt(webStock[tp]) {
				reportErr += fmt.Sprintf("<b>%s</b> -> db: %s | web: %d<br/>", strings.ToUpper(tp), dbStock[tp], webStock[tp])
			}
		}

		if reportErr != "" {
			msg += fmt.Sprintf("<b>PRODUCT</b> %s<br/>%s<br/>", dbStock["product_detail_fkid"], reportErr)
		}
	}

	if len(data) != len(prodIds) {
		msg += fmt.Sprintf("<br/><b>TOTAL NOT SAME</b> <br/>db: %d | web: %d", len(data), len(prodIds))
	}

	if msg != "" {
		msg = fmt.Sprintf("<b>OUTLET</b> : %s<br/>", report[0]["outlet_name"]) + msg + "<br/><br/>"
	}

	fmt.Println("report diff: " + msg)

	title := fmt.Sprintf("REPORT CHECK STOCK - %s", time.Now().Add(7*time.Hour).Format("02-01-2006"))

	scheduleMsg, err := db.Query("select id, message from scheduled_message where title = ? and status = 'pending' limit 1", title)
	log.IfError(err)

	if len(scheduleMsg) == 0 {
		_, err = db.Insert("scheduled_message", map[string]interface{}{
			"message":      msg,
			"title":        title,
			"media":        "email",
			"receiver":     "<EMAIL>",
			"time_deliver": time.Now().Add(15*time.Minute).Unix() * 1000,
			"data_created": time.Now().Unix() * 1000,
		})
		log.IfError(err)
	} else {
		_, err = db.Update("scheduled_message", map[string]interface{}{
			"message": fmt.Sprintf("%s %s", scheduleMsg["message"], msg),
		}, "id = ?", scheduleMsg["id"])
		log.IfError(err)
	}

	//utils.SendEmailZoho(models.ZohoMail{
	//	To:      "<EMAIL>",
	//	Subject: title,
	//	Content: strings.Replace(msg, "\n", "<br/>", -1),
	//})
}
