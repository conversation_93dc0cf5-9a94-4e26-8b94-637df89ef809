package v1

import (
	"encoding/json"
	"fmt"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"net/http"
	"strings"
	"time"
)

// query untuk mengambil data sales dengan void yang belum berelasi
func queryGetTotalSalesByShift_v1(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('"+outletId+"') "
	}

	//get sales information v2
	querySalesInformation := db.GetSQLRaw(`
		SELECT 
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			sum(sd.qty)-IFNULL(sum(sv.qty),0) AS qty_clean
		FROM sales_detail sd
		LEFT JOIN sales s ON s.sales_id=sd.sales_fkid
		LEFT JOIN sales_void sv ON sv.sales_fkid=s.sales_id AND sd.product_detail_fkid=sv.product_detail_fkid
		LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP by sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	query, err := db.QueryArray(`
		SELECT
			outlet_id,
			outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (`+querySalesInformation+`) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)
	utils.CheckErr(err)
	return query
}

// query untuk mengambil data sales dengan void yang SUDAH berelasi
func queryGetTotalSalesByShift_v2(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('"+outletId+"') "
	}

	querySalesInformation := db.GetSQLRaw(`
		SELECT
			s.sales_id,
			o.outlet_id,
			o.name AS outlet_name,
			s.qty_customers,
			s.grand_total,
			(sum(sd.qty) - IFNULL(sum(sv.qty),0)) AS qty_clean
		FROM sales s
		LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id 
		LEFT JOIN sales_detail sd ON sd.sales_fkid = s.sales_id 
		LEFT JOIN sales_void sv ON sd.sales_detail_id = sv.sales_detail_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE o.admin_fkid=? 
		AND os.time_open >=? 
		AND os.time_open <=?
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		`+whereInOutlet+`
		GROUP BY sales_id
		HAVING qty_clean > 0
	`, adminId, timeStart, timeEnd)

	//finishing query
	query, err := db.QueryArray(`
		SELECT
			outlet_id,
			outlet_name, 
			sum(qty_customers) AS total_customer,
			sum(grand_total) AS total_income,
			count(sales_id) AS total_transaction
		FROM (`+querySalesInformation+`) tb
		GROUP BY outlet_id
		ORDER BY total_income DESC
	`)
	utils.CheckErr(err)
	return query
}

func getPendingIncomeByShift(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND ts.outlet_fkid IN ('"+outletId+"') "
	}

	query, err := db.QueryArray(`
		SELECT 
			o.outlet_id,
			o.name AS outlet_name,
			sum(ts.grand_total) AS pending_income
		FROM tmp_sales ts
		LEFT JOIN outlets o ON ts.outlet_fkid = o.outlet_id
		WHERE ts.time_created >= ?
		AND ts.time_created <= ?
		AND o.admin_fkid = ?
		AND ts.status = 'pending'
		`+whereInOutlet+`
		GROUP BY ts.outlet_fkid
		ORDER BY pending_income DESC
	`, timeStart, timeEnd, adminId)
	utils.CheckErr(err)
	return query
}

func SalesTransactionByShift_map(adminId string, timeStart, timeEnd int64, outletIds []interface{}) []map[string]interface{} {
	//render outletlist
	outletIdMap := map[string]interface{}{}

	//get time when void not related with sales_detail
	//where IN outlet_fkid
	whereInOutlet := ""
	if outletIds != nil {
		outletId := strings.Join(cast.ToStringSlice(outletIds), "','")
		whereInOutlet = "AND s.outlet_fkid IN ('"+outletId+"') "
	}
	qCheckSalesVoid, err := db.QueryArray(`
		SELECT max(os.time_open) AS time
		FROM sales_void sv
		LEFT JOIN sales s ON sv.sales_fkid=s.sales_id
		LEFT JOIN outlets o ON o.outlet_id=s.outlet_fkid
		LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid
		WHERE sv.sales_detail_fkid is null
		AND s.status = 'success'
		AND s.data_status='on'
		AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS')
		AND o.admin_fkid = ?
		`+whereInOutlet+`
		HAVING time >= ?
		AND time <= ?
	`, adminId, timeStart, timeEnd)
	utils.CheckErr(err)
	if len(qCheckSalesVoid) > 0 {
		fmt.Println("last unrelated void: ", qCheckSalesVoid[0]["time"])
	}

	//get data dengan void belum berelasi dengan sales_detail
	chanSales1 := make(chan map[string]interface{})
	go func() {
		dataRender := map[string]interface{}{}
		if len(qCheckSalesVoid) > 0 {
			timeEnd2 := cast.ToInt64(qCheckSalesVoid[0]["time"])
			query := queryGetTotalSalesByShift_v1(adminId, timeStart, timeEnd2, outletIds)
			for _, data := range query {
				id := utils.ToString(data["outlet_id"])

				//append outletid
				if outletIdMap[id] == nil {
					outletIdMap[id] = map[string]interface{}{
						"outlet_id": data["outlet_id"],
						"outlet_name": data["outlet_name"],
					}
				}

				//render map
				dataRender[id] = map[string]interface{}{
					"total_customer": data["total_customer"],
					"total_income": data["total_income"],
					"total_transaction": data["total_transaction"],
				}
			}
		}
		chanSales1 <- dataRender
	}()
	//get data dengan void sudah berelasi dengan sales_detail
	chanSales2 := make(chan map[string]interface{})
	go func() {
		dataRender := map[string]interface{}{}

		waktuMulai := timeStart
		if len(qCheckSalesVoid) > 0 {
			waktuMulai = cast.ToInt64(qCheckSalesVoid[0]["time"]) + 1
		}
		query := queryGetTotalSalesByShift_v2(adminId, waktuMulai, timeEnd, outletIds)
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id": data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}

			//render map
			dataRender[id] = map[string]interface{}{
				"total_customer": data["total_customer"],
				"total_income": data["total_income"],
				"total_transaction": data["total_transaction"],
			}
		}
		chanSales2 <- dataRender
	}()
	chanPendingIncome := make(chan map[string]interface{})
	go func() {
		query := getPendingIncomeByShift(adminId,0,timeEnd,outletIds)
		dataRender := map[string]interface{}{}
		for _, data := range query {
			id := utils.ToString(data["outlet_id"])

			//render map
			if dataRender[id] == nil {
				dataRender[id] = 0
			}

			dataRender[id] = data["pending_income"]

			//append outletid
			if outletIdMap[id] == nil {
				outletIdMap[id] = map[string]interface{}{
					"outlet_id": data["outlet_id"],
					"outlet_name": data["outlet_name"],
				}
			}
		}
		chanPendingIncome <- dataRender
	}()

	//hasil merge id
	sales1 := <-chanSales1 //data sales ada void (belum berelasi dengan sales_detail)
	sales2 := <-chanSales2 //data sales ada void (sudah berelasi dengan sales_detail)
	pending_income := <-chanPendingIncome

	//render outletlist
	dataRender := []map[string]interface{}{}
	for index, data := range outletIdMap {
		dataSales1 := sales1[index]
		if dataSales1 == nil {
			dataSales1 = map[string]interface{}{
				"average_income": 0,
				"average_income_customer": 0,
				"total_customer": 0,
				"total_income": 0,
				"total_transaction": 0,
			}
		}
		datas1 := cast.ToStringMap(dataSales1)

		dataSales2 := sales2[index]
		if dataSales2 == nil {
			dataSales2 = map[string]interface{}{
				"average_income": 0,
				"average_income_customer": 0,
				"total_customer": 0,
				"total_income": 0,
				"total_transaction": 0,
			}
		}
		datas2 := cast.ToStringMap(dataSales2)

		dataPendingIncome := pending_income[index]
		if dataPendingIncome == nil {
			dataPendingIncome = 0
		}

		//perhitungan akhir
		allTotalIncome := cast.ToInt64(datas1["total_income"]) + cast.ToInt64(datas2["total_income"])
		allTotalTransaction := cast.ToInt64(datas1["total_transaction"]) + cast.ToInt64(datas2["total_transaction"])
		allTotalCustomer := cast.ToInt64(datas1["total_customer"]) + cast.ToInt64(datas2["total_customer"])
		avgIncome := cast.ToInt64(0)
		if allTotalTransaction != 0 {
			avgIncome = allTotalIncome / allTotalTransaction
		}
		avgIncomeCustomer := cast.ToInt64(0)
		if allTotalCustomer != 0 {
			avgIncomeCustomer = allTotalIncome / allTotalCustomer
		}


		//render format data
		mergeMap := make(map[string]interface{})
		mergeMap["average_income"] = avgIncome
		mergeMap["average_income_customer"] = avgIncomeCustomer
		mergeMap["total_customer"] = allTotalCustomer
		mergeMap["total_income"] = allTotalIncome
		mergeMap["total_transaction"] = allTotalTransaction
		mergeMap["pending_income"] = dataPendingIncome

		//render data per-outlet
		data := data.(map[string] interface{})
		dataFormat := map[string]interface{}{
			"outlet_id": data["outlet_id"],
			"outlet_name": data["outlet_name"],
			"detail": mergeMap,
		}
		dataRender = append(dataRender, dataFormat)
	}

	return dataRender
}

func SalesTransactionByShift_v1(ctx *fasthttp.RequestCtx)  {
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  false,
			Code:    400,
			Data:    err,
		})
	}else{
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess,",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		timeStartBenchmark := time.Now()
		data := SalesTransactionByShift_map(adminId, timeStart, timeEnd, outletIds)
		fmt.Printf("BENCHMARK sales_transaction_info : %v\n", time.Since(timeStartBenchmark))

		//output
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  true,
			Code:    200,
			Data:    data,
		})
	}
}
