package cast

import (
	"encoding/json"
	"fmt"
	"reflect"
	"runtime"
	"strconv"
	"strings"
)

func ToString(data interface{}) string {
	switch v := data.(type) {
	case int:
		return strconv.Itoa(v)
	case int64:
		return strconv.FormatInt(v, 10)
	case float32:
		return fmt.Sprintf("%f", v)
	case float64:
		return strconv.FormatFloat(v, 'f', 2, 64)
	case string:
		return v
	case []uint8:
		return string(v)
	case []interface{}:
		return fmt.Sprintf("%s", v[0])
	case error:
		return v.Error()
	case nil:
		return ""
	default:
		if reflect.TypeOf(data).Kind() == reflect.Slice {
			return ToStringJson(data)
		}
		//if map or struct, convert to json
		if reflect.TypeOf(data).Kind() == reflect.Map || reflect.TypeOf(data).Kind() == reflect.Struct {
			return ToStringJson(data)
		}
		fmt.Printf("[String] - invalid convertion. type: '%s' | kind: %s || >> %v\n", reflect.TypeOf(data), reflect.TypeOf(data).Kind(), data)
		return fmt.Sprintf("%s", data)
	}
}

func ToInt(data interface{}) int {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	r := strings.NewReplacer(`"`, "", `'`, "")
	dataStr = r.Replace(dataStr)

	if strings.Contains(dataStr, ".") {
		i := strings.Index(dataStr, ".")
		dataStr = dataStr[:i]
	}

	result, err := strconv.Atoi(dataStr)
	if err != nil {
		switch i := data.(type) {
		case float32:
			return int(i)
		case float64:
			return int(i)
		default:
			_, fn, line, _ := runtime.Caller(1)
			fmt.Printf("%s:%d failed converting '%v' to int, type is %v | %v\n", fn, line, data, reflect.TypeOf(data), err)
			return 0
		}
	} else {
		return result
	}
}

func ToInt64(data interface{}) int64 {
	if data == nil {
		return 0
	}
	if reflect.TypeOf(data).Kind() == reflect.Float64 {
		return int64(data.(float64))
	}

	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}

	result, err := strconv.ParseInt(dataStr, 10, 64)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d parsing to int64 err: %v", fn, line, err)
		return 0
	} else {
		return result
	}
}

func ToFloat32(data interface{}) float32 {
	dataStr := ToString(data)
	if dataStr == "" {
		return 0
	}
	result, err := strconv.ParseFloat(dataStr, 32)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d parsing to float32 err: %v", fn, line, err)
		return 0
	} else {
		return float32(result)
	}
}

func ToFloat64(value interface{}) float64 {
	if value == nil {
		return 0
	}
	switch v := value.(type) {
	case float64:
		return v
	case int:
		return float64(v)
	case int64:
		return float64(v)
	case string:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return 0
		}
		return f
	default:
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%v unable to convert %v of type %T to float64\n", fn, line, value, value)
		return 0
	}
}

func ToStringJson(data interface{}) string {
	dataJson, err := json.Marshal(data)
	if err != nil {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%s:%d parsing to json err: %v", fn, line, err)
	}
	return string(dataJson)
}

func ToInterfaceArray(data interface{}) []interface{} {
	result := make([]interface{}, 0)
	if reflect.TypeOf(data).Kind() == reflect.Slice {
		s := reflect.ValueOf(data)
		for i := 0; i < s.Len(); i++ {
			result = append(result, s.Index(i).Interface())
		}
	}
	return result
}

// ToSlice converts an interface to a slice of a specific type
func ToSlice[T any](input interface{}) ([]T, error) {
	if input == nil {
		return nil, nil
	}

	v := reflect.ValueOf(input)
	if v.Kind() != reflect.Slice {
		return nil, fmt.Errorf("input is not a slice")
	}

	result := make([]T, v.Len())
	for i := 0; i < v.Len(); i++ {
		elem, ok := v.Index(i).Interface().(T)
		if !ok {
			return nil, fmt.Errorf("element %d is not of type %T", i, result)
		}
		result[i] = elem
	}

	return result, nil
}
