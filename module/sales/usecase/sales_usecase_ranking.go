package usecase

import (
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-report/core/array"
	"gitlab.com/uniqdev/backend/api-report/core/log"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	repoSales "gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

func (s *salesUseCase) FetchSalesRankingV2(request domain.SalesRankingRequest, user domain.User) (map[string]any, error) {
	//validate

	//if filter category/subcategory active, no paging required
	if request.Offset > 0 && (len(request.Category) > 0 || len(request.SubCategory) > 0) {
		log.Info("return empty.... request: %v", utils.SimplyToJson(request))
		return map[string]any{"data": []map[string]any{}}, nil
	}

	//adjust request
	request.Limit = 100
	request.Offset = request.Page * request.Limit

	if request.StartDate == 0 {
		request.StartDate = time.Now().AddDate(0, 0, -1).Unix() * 1000
		log.Info("set start date to --> %v", request.StartDate)
	}
	if request.EndDate == 0 {
		request.EndDate = time.Now().Unix() * 1000
		log.Info("set end date to --> %v", request.EndDate)
	}

	//adjust request
	if millisNow := time.Now().Unix() * 1000; request.EndDate > millisNow {
		request.EndDate = millisNow
	}

	timeStart := time.Now()
	sales, err := s.repoPrimary.FetchSalesRankingPlain(request, user)
	log.Info("FetchSalesRankingV2, size: %v | param %v ", len(sales), utils.SimplyToJson(request))
	if log.IfError(err) || len(sales) == 0 {
		return map[string]any{"data": []any{}}, nil
	}

	log.Info(">>> FetchSalesRankingPlain tooks %v", time.Since(timeStart))

	outletIds := make(map[int]bool)
	shiftIds := make(map[int]bool)
	productIds := make([]any, 0)
	for _, sale := range sales {
		if _, ok := sale["product_fkid"]; !ok {
			log.IfError(fmt.Errorf(">>> data sales doesnt have product_fkid: %v", utils.SimplyToJson(sale)))
		}
		productIds = append(productIds, sale["product_fkid"])
		for _, outletId := range strings.Split(cast.ToString(sale["outlet_ids"]), ",") {
			outletIds[cast.ToInt(outletId)] = true
		}
		for _, id := range strings.Split(cast.ToString(sale["shift_ids"]), ",") {
			shiftIds[cast.ToInt(id)] = true
		}
	}

	productChan := make(chan map[string]map[string]any)
	outletChan := make(chan map[string]map[string]any)
	shiftChan := make(chan map[string]map[string]any)

	go func(id []any, dataChan chan map[string]map[string]any) {
		data, _ := s.repoPrimary.FetchProductByIds(id...)
		dataChan <- array.FlatMapArray(data, "product_id")
	}(productIds, productChan)

	go func(id []any, dataChan chan map[string]map[string]any) {
		data, _ := s.repoPrimary.FetchOutletByIds(id...)
		dataChan <- array.FlatMapArray(data, "outlet_id")
	}(array.GetKeys(outletIds), outletChan)

	go func(id []any, dataChan chan map[string]map[string]any) {
		data, _ := s.repoPrimary.FetchShiftByIds(id...)
		dataChan <- array.FlatMapArray(data, "shift_id")
	}(array.GetKeys(shiftIds), shiftChan)

	products := <-productChan
	outlets := <-outletChan
	shifts := <-shiftChan

	result := make([]map[string]any, 0)
	var logs strings.Builder
	for i, sale := range sales {
		if _, ok := sale["product_fkid"]; !ok {
			log.IfError(fmt.Errorf("sale doesn have product_fkid: %v", utils.SimplyToJson(sale)))
		}

		productId := fmt.Sprintf("%v", sale["product_fkid"])
		product := products[productId]

		if _, ok := products[productId]; !ok {
			log.IfError(fmt.Errorf("products not found: %v, sale: %v | %v", productId, utils.SimplyToJson(sale), utils.SimplyToJson(products)))
		}

		//filter
		if cast.ToInt(product["product_subcategory_id"]) > 0 && len(request.SubCategory) > 0 && !array.Contain(request.SubCategory, cast.ToInt(product["product_subcategory_id"])) {
			logs.WriteString(fmt.Sprintf("skip SubCategory '%v', ", product["product_subcategory_id"]))
			continue
		}

		if cast.ToInt(product["product_category_id"]) > 0 && len(request.Category) > 0 && !array.Contain(request.Category, cast.ToInt(product["product_category_id"])) {
			logs.WriteString(fmt.Sprintf("skip Category '%v', ", product["product_category_id"]))
			continue
		}

		sales[i]["product_name"] = utils.FormatVariantName(cast.ToString(product["name"]), sale["variant_name"])
		sales[i]["sku"] = product["sku"]
		sales[i]["category"] = []any{product["category_name"]}
		sales[i]["sub_category"] = []any{product["subcategory_name"]}

		//set outlet
		outletNames := make([]string, 0)
		for _, outletId := range strings.Split(cast.ToString(sale["outlet_ids"]), ",") {
			outletNames = append(outletNames, cast.ToString(outlets[outletId]["name"]))
		}
		sales[i]["outlet_name"] = outletNames
		sales[i]["outlet"] = strings.Join(outletNames, ", ")

		// /set shift
		shiftNames := make([]string, 0)
		for _, id := range strings.Split(cast.ToString(sale["shift_ids"]), ",") {
			shiftNames = append(shiftNames, cast.ToString(shifts[id]["name"]))
		}
		sales[i]["shift_name"] = shiftNames
		sales[i]["shift"] = strings.Join(shiftNames, ", ")

		// set average
		sales[i]["avg"] = 0
		if qty := cast.ToInt(sale["qty"]); qty > 0 {
			sales[i]["avg"] = cast.ToInt(sale["sub_total"]) / qty
		}

		//removing unecessary fields
		delete(sale, "outlet_ids") //
		delete(sale, "shift_ids")

		result = append(result, sale)
	}

	log.Info("FetchSalesRanking returning %v data, total sales: %v | productIds: %v, products: %v", len(result), len(sales), len(productIds), len(products))
	//in case filter causing return no data, fetch next page directly
	if len(result) == 0 && len(sales) > 0 {
		log.Info(">> logs: %v", logs.String())
		// request.Offset += request.Limit
		// log.Info("fetch next page, adjust offset to %v", request.Offset)
		// return s.FetchSalesRankingV2(request, user)
		return map[string]any{"data": result, "has_next_data": len(result) == 0 && len(sales) > 0}, nil
	}

	return map[string]any{"data": result}, nil
}

func (s *salesUseCase) FetchSalesRanking(request domain.SalesRankingRequest, user domain.User) (map[string]any, error) {
	//validate
	if request.StartDate == 0 {
		request.StartDate = time.Now().AddDate(0, 0, -1).Unix() * 1000
		log.Info("set start date to --> %v", request.StartDate)
	}

	if request.EndDate == 0 {
		request.EndDate = time.Now().Unix() * 1000
		log.Info("set end date to --> %v", request.EndDate)
	}

	//if request data newer that what on db replicate, get from primary db instead
	if request.EndDate > repoSales.GetMaxTimeSync() || os.Getenv("ENV") == "localhost" {
		log.Info("fetching salesRanking from primary db...")
		result, err := s.repoPrimary.FetchSalesRanking(request, user)
		response := make([]map[string]any, 0)
		for i, row := range result {
			//filtering subcategory
			if len(request.SubCategory) > 0 && !array.Contain(request.SubCategory, cast.ToInt(row["product_subcategory_id"])) {
				continue
			}
			result[i]["outlet_name"] = strings.Split(cast.ToString(row["outlet"]), ",")
			result[i]["shift_name"] = strings.Split(cast.ToString(row["shift"]), ",")
			result[i]["category"] = strings.Split(cast.ToString(row["category"]), ",")
			result[i]["sub_category"] = strings.Split(cast.ToString(row["sub_category"]), ",")
			result[i]["avg"] = 0
			if cast.ToInt(row["qty"]) > 0 {
				result[i]["avg"] = cast.ToInt(row["sub_total"]) / cast.ToInt(row["qty"])
			}
			response = append(response, result[i])
		}
		return map[string]any{"data": response}, err
	}

	//fetching data from bigquery doesnt use pagination, if request offset > 0 then return empty data
	if request.Offset > 0 {
		log.Info("salesRanking returning empty data.. offset is: %f ", request.Offset)
		return map[string]any{"data": []any{}}, nil
	}

	if len(request.Category) > 0 || len(request.SubCategory) > 0 {
		products, err := s.repoProduct.FetchProductByFilter(domain.ProductFilter{
			CategoryId:    array.TransformIntToInterface(request.Category),
			SubCategoryId: array.TransformIntToInterface(request.SubCategory),
		})

		log.Info("got product total: %d", len(products))
		if err == nil {
			productId := make([]any, 0)
			for _, row := range products {
				productId = append(productId, row["product_id"])
			}
			request.ProductId = productId
		}
	}

	log.Info("salesRanking, param: %v, user: %v", utils.SimplyToJson(request), user)
	result, err := s.repoReplica.FetchSalesRankingPlain(request, user)
	if log.IfError(err) {
		return nil, err
	}

	if len(result) == 0 {
		log.Info("salesRanking return no data")
		return map[string]any{"data": []any{}}, nil
	}

	outletIds := make(map[string]any)
	productIds := make([]any, 0)
	shiftIds := make(map[string]any)

	for _, row := range result {
		ids := strings.Split(cast.ToString(row["outlet_ids"]), ",")
		for _, id := range ids {
			outletIds[id] = true
		}

		ids = strings.Split(cast.ToString(row["shift_ids"]), ",")
		for _, id := range ids {
			shiftIds[id] = true
		}
		productIds = append(productIds, row["id"])
	}

	outletChan := make(chan map[string]map[string]any)
	shiftChan := make(chan map[string]map[string]any)
	productChan := make(chan map[string]map[string]any)

	go func(id []any) {
		data, _ := s.repoPrimary.FetchOutletByIds(id...)
		outletChan <- array.FlatMapArray(data, "outlet_id")
	}(array.FromMapKey(outletIds))

	go func(id []any) {
		data, _ := s.repoPrimary.FetchShiftByIds(id...)
		shiftChan <- array.FlatMapArray(data, "shift_id")
	}(array.FromMapKey(shiftIds))

	go func(id []any) {
		data, _ := s.repoPrimary.FetchProductByIds(id...)
		productChan <- array.FlatMapArray(data, "product_id")
	}(productIds)

	outlets := <-outletChan
	products := <-productChan
	shifts := <-shiftChan

	fmt.Println("prods => ", len(products))
	fmt.Println("shift => ", len(shifts))

	for i, row := range result {
		ids := strings.Split(cast.ToString(row["outlet_ids"]), ",")
		outletName := make([]string, 0)
		for _, id := range ids {
			outletName = append(outletName, cast.ToString(outlets[id]["name"]))
		}
		result[i]["outlet_name"] = outletName

		ids = strings.Split(cast.ToString(row["shift_ids"]), ",")
		shiftName := make([]string, 0)
		for _, id := range ids {
			shiftName = append(shiftName, cast.ToString(shifts[id]["name"]))
		}
		result[i]["shift_name"] = shiftName

		if data, ok := products[cast.ToString(row["id"])]; ok {
			result[i]["product_name"] = data["name"]
			result[i]["category"] = []any{data["category_name"]}
			result[i]["sub_category"] = []any{data["subcategory_name"]}
			result[i]["sku"] = data["sku"]
		}

		result[i]["avg"] = 0
		if cast.ToInt(row["qty"]) > 0 {
			result[i]["avg"] = cast.ToInt(row["sub_total"]) / cast.ToInt(row["qty"])
		}
	}

	return map[string]any{"data": result}, nil
}
