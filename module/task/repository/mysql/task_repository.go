package mysql

import (
	"database/sql"
	"strings"

	"gitlab.com/uniqdev/backend/api-report/core/db"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/models"
)

type taskRepository struct {
	mysql.Repository
}

func NewMysqlTaskRepository(db *sql.DB) domain.TaskRepository {
	return &taskRepository{mysql.Repository{Conn: db}}
}

func (t *taskRepository) FetchTaskWorkflowChanges(param *domain.TaskReportRequest) ([]models.BoardWorkflowEntity, error) {
	sql := `SELECT tw.* from tm_boards_workflow tw 
	join tm_boards tb on tb.id=tw.tm_boards_id
	where tb.admin_id=@adminId and tb.deleted_at is null
	$where
	order by tw.tm_boards_id, tw.id `

	var whereSql strings.Builder
	if param.CategoryId > 0 {
		whereSql.WriteString(" and tb.tm_master_category_id=@categoryId ")
	}
	if param.StartDate > 0 {
		whereSql.WriteString(" and tb.created_at >= @startDate ")
	}
	if param.EndDate > 0 {
		whereSql.WriteString(" and tb.created_at <= @endDate ")
	}

	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":    param.BusinessId,
		"categoryId": param.CategoryId,
		"where":      whereSql.String(),
		"startDate":  param.StartDate,
		"endDate":    param.EndDate,
	})

	var result []models.BoardWorkflowEntity
	err := t.Query(sql, params...).Model(&result)
	return result, err
}

// FetchWorkflow implements domain.TaskRepository.
func (t *taskRepository) FetchWorkflow(ids ...int) ([]models.MasterWorkflowEntity, error) {
	sql := `select * from tm_master_category_workflow where id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": ids,
	})

	var result []models.MasterWorkflowEntity
	err := t.Query(sql, params...).Model(&result)
	return result, err
}

// FetchBoardTimeAnalysis implements domain.TaskRepository.
func (t *taskRepository) FetchBoardTimeAnalysis(boardIds ...int) ([]models.TimeAnalysisBoardResponse, error) {
	sql := `SELECT tb.id, tb.name, tb.description, tb.status, tc.name as category_name, tb.tm_master_category_id as category_id
	from tm_boards tb 
	JOIN tm_master_category tc ON tb.tm_master_category_id = tc.id
	WHERE tb.id in @ids `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": boardIds,
	})

	var result []models.TimeAnalysisBoardResponse
	err := t.Query(sql, params...).Model(&result)
	return result, err
}

// FetchLastWorkflowBoard implements domain.TaskRepository.
func (t *taskRepository) FetchLastWorkflowBoard(boardIds ...int) ([]models.BoardWorkflowEntity, error) {
	sql := `SELECT tbw.id,tbw.tm_boards_id, tbw.tm_master_category_workflow_id, tbw.created_at
	from tm_boards_workflow tbw 
	join (SELECT max(id) as id, tm_boards_id from tm_boards_workflow where tm_boards_id in @ids group by tm_boards_id) 
	tbwg on tbwg.id=tbw.id `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"ids": boardIds,
	})

	var result []models.BoardWorkflowEntity
	err := t.Query(sql, params...).Model(&result)
	return result, err
}
