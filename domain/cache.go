package domain

import "time"

type CacheInterface interface {
	Get(key string) (string, error)
	GetMap(key string) (map[string]string, error)
	GetMapArray(keyPrefix string, ids ...interface{}) ([]map[string]string, error)

	Set(key string, value interface{}, expiration time.Duration) error
	SetMap(key string, value map[string]interface{}, expiration time.Duration) error
	SetMapBatch(keyPrefix, keyField string, values []map[string]interface{}, expiration time.Duration) error

	Delete(key string) error
}
