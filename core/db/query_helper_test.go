package db

import (
	"fmt"
	"reflect"
	"testing"
)

func TestMapParam(t *testing.T) {
	type args struct {
		sql    string
		params map[string]interface{}
	}

	sql1 := "select * from sales where admin_id = @adminId"
	sql1Want := "select * from sales where admin_id =  ? "
	sql1Parms := map[string]interface{}{"adminId": 10}
	sql1ParamWant := []interface{}{10}

	sql2 := "select * from sales where outlet_id = @outletId and admin_id = @adminId"
	sql2Want := "select * from sales where outlet_id =  ?  and admin_id =  ? "
	sql2Parms := map[string]interface{}{"adminId": 10, "outletId": 1}
	sql2ParamWant := []interface{}{1, 10}

	sql3 := "select * from sales where outlet_id = @outletId and admin_id = @adminId and id = @outletId"
	sql3Want := "select * from sales where outlet_id =  ?  and admin_id =  ?  and id =  ? "
	sql3Parms := map[string]interface{}{"adminId": 10, "outletId": 1}
	sql3ParamWant := []interface{}{1, 10, 1}

	sql4 := "select * from sales where admin_id in (@adminId)"
	sql4Want := "select * from sales where admin_id in (?,?,?)"
	sql4Parms := map[string]interface{}{"adminId": []int{10, 11, 12}}
	sql4ParamWant := []interface{}{10, 11, 12}

	sql5 := "select * from sales where admin_id in @adminId and outlet_id in (@outletIds)"
	sql5Want := "select * from sales where admin_id in (?,?,?) and outlet_id in (?,?,?)"
	sql5Parms := map[string]interface{}{"adminId": []int{10, 11, 12}, "outletIds": []int{1, 2, 3}}
	sql5ParamWant := []interface{}{10, 11, 12, 1, 2, 3}

	// sqlParmNil := map[string]interface{}{"adminId": nil, "outletIds": []int{1, 2, 3}}

	sql6 := "select * from products where id in @ids and copy_id in @ids"
	sql6Want := "select * from products where id in (?,?,?) and copy_id in (?,?,?)"
	sql6Params := map[string]interface{}{"ids": []int{10, 11, 12}}
	sql6ParamWant := []interface{}{10, 11, 12, 10, 11, 12}

	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		{name: "easy", args: args{sql: sql1, params: sql1Parms}, want: sql1Want, want1: sql1ParamWant},
		{name: "hard", args: args{sql: sql2, params: sql2Parms}, want: sql2Want, want1: sql2ParamWant},
		{name: "hard-l1", args: args{sql: sql3, params: sql3Parms}, want: sql3Want, want1: sql3ParamWant},
		{name: "hard-whereIn-l2", args: args{sql: sql4, params: sql4Parms}, want: sql4Want, want1: sql4ParamWant},
		{name: "hard-whereIn-l3", args: args{sql: sql5, params: sql5Parms}, want: sql5Want, want1: sql5ParamWant},
		// {name: "nil-value", args: args{sql: sql5, params: sqlParmNil}, want: sql5Want, want1: sql5ParamWant},
		{name: "same-key", args: args{sql: sql6, params: sql6Params}, want: sql6Want, want1: sql6ParamWant},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := MapParam(tt.args.sql, tt.args.params)
			fmt.Println("sql before after same: ", tt.args.sql)
			if got != tt.want {
				t.Errorf("MapParam() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("MapParam() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestWhereAnd(t *testing.T) {
	type args struct {
		data map[string]interface{}
	}
	tests := []struct {
		name  string
		args  args
		want  string
		want1 []interface{}
	}{
		// TODO: Add test cases.
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := WhereAnd(tt.args.data)
			if got != tt.want {
				t.Errorf("WhereAnd() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("WhereAnd() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}
