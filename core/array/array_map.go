package array

import (
	"fmt"
	"reflect"
	"runtime"
	"strings"

	"github.com/spf13/cast"
)

// FromMapKey returning keys from map as array
func FromMapKey(data map[string]any) []any {
	result := make([]any, 0)
	for k, _ := range data {
		result = append(result, k)
	}
	return result
}

func GetKeys(data any) []any {
	result := make([]any, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result = append(result, key.Interface())
		}
	} else {
		_, fn, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v GetKeys failed, data is not map, but %v \n", fn, line, reflect.TypeOf(data).Kind())
	}
	return result
}

func GetKeysFromMap[K comparable, V any](data map[K]V) []K {
	result := make([]K, 0, len(data))
	for key := range data {
		result = append(result, key)
	}
	return result
}

func MapKey[K comparable, V any](data map[K]V) []K {
	result := make([]K, 0)
	for key := range data {
		result = append(result, key)
	}
	return result
}

func FlatMapArray(data []map[string]any, key string) map[string]map[string]any {
	result := make(map[string]map[string]any)
	for _, row := range data {
		if row[key] == nil {
			continue
		}
		result[cast.ToString(row[key])] = row
	}
	return result
}

func Copy(data map[string]any) map[string]any {
	result := make(map[string]any)
	for k, v := range data {
		result[k] = v
	}
	return result
}

func CopyData(data any) any {
	result := make(map[string]any, 0)
	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result[key.String()] = v.MapIndex(key).Interface()
		}
	}
	return result
}

func GetSafe(data []map[string]any, index int) map[string]any {
	if len(data) > index {
		return data[index]
	}
	return map[string]any{}
}

func Join(separator string, data ...any) string {
	result := make([]string, 0)
	for _, row := range data {
		if str := cast.ToString(row); str != "" {
			result = append(result, str)
		}
	}
	return strings.Join(result, separator)
}

// return map with specific key only
func TakeOnly(data map[string]any, keys ...string) map[string]any {
	result := make(map[string]any)
	for _, key := range keys {
		result[key] = data[key]
	}
	return result
}

func TakeValues(data any) []any {
	result := make([]any, 0)
	// for _, v := range data {
	// 	result = append(result, v)
	// }

	if reflect.TypeOf(data).Kind() == reflect.Map {
		v := reflect.ValueOf(data)
		for _, key := range v.MapKeys() {
			result = append(result, v.MapIndex(key).Interface())
		}
	}
	return result
}

func GroupBy(data []map[string]any, groupKey string) map[string][]map[string]any {
	result := make(map[string][]map[string]any)
	for _, row := range data {
		if _, ok := result[cast.ToString(row[groupKey])]; !ok {
			result[cast.ToString(row[groupKey])] = make([]map[string]any, 0)
		}
		result[cast.ToString(row[groupKey])] = append(result[cast.ToString(row[groupKey])], row)
	}
	return result
}

func MergeMapInplace(data map[string]any, others ...map[string]any) {
	for _, dataMap := range others {
		for k, v := range dataMap {
			data[k] = v
		}
	}
}

func RemoveDuplicateIds(cacheData []map[string]string, keyField string, ids ...any) []any {
	idMap := make(map[string]bool)
	for _, row := range ids {
		idMap[cast.ToString(row)] = true
	}

	for _, cache := range cacheData {
		delete(idMap, cast.ToString(cache[keyField]))
	}

	return GetKeys(idMap)
}

func ChangeMapType(data []map[string]string) []map[string]any {
	result := make([]map[string]any, 0)
	for _, row := range data {
		rowData := make(map[string]any)
		for k, v := range row {
			rowData[k] = v
		}
		result = append(result, rowData)
	}
	return result
}
