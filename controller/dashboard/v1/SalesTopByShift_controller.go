package v1

import (
	"encoding/json"
	"fmt"
	"github.com/a8m/djson"
	"github.com/spf13/cast"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	"gitlab.com/uniqdev/backend/api-report/core/utils"
	"gitlab.com/uniqdev/backend/api-report/models"
	"net/http"
	"strings"
	"time"
)

func queryGetItemSoldByShift(adminId string, timeStart int64, timeEnd int64, outletIds []interface{}, itemIds []interface{}, orderBy interface{}, limit interface{}) string {
	rawSQL := ""

	//create where clause outlet_id IN (...)
	outletId := ""
	whereInClauseItemVoid := ""
	whereInClauseItemSold := ""
	if outletIds != nil {
		outletId = strings.Join(cast.ToStringSlice(outletIds), ",")
		whereInClauseItemVoid = " AND ssv.outlet_fkid IN ( "+outletId+")"
		whereInClauseItemSold = " AND s.outlet_fkid IN ("+outletId+")"
	}

	//create where clasuse product_id IN (...)
	itemId := ""
	itemSoldHavingInClause := ""
	if itemIds != nil {
		itemId = strings.Join(cast.ToStringSlice(itemIds),"','")
		itemSoldHavingInClause = " AND IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) IN ('"+itemId+"') "
	}

	//order query
	orderByQuery := ""
	if orderBy != nil && cast.ToString(orderBy) != "" {
		orderByQuery = "ORDER BY "+cast.ToString(orderBy)
	}

	//limit
	limitQuery := ""
	if limit != nil {
		limitQuery = "LIMIT "+ cast.ToString(limit)
	}

	//get item void
	queryItemVoid := db.GetSQLRaw("" +
		//queryItemVoid, err := db.QueryArray(""+
		"SELECT " +
		"IF(pdsv.variant_fkid IS NULL, pdsv.product_fkid, concat(pdsv.product_fkid,'-',pdsv.variant_fkid)) AS item_id," +
		"sum(sv.qty) AS qty," +
		"sum(sv.price) AS price," +
		"sum(sv.sub_total + sv.discount) AS subtotal_void " +
		"FROM sales_void sv " +
		"LEFT JOIN products_detail pdsv ON sv.product_detail_fkid = pdsv.product_detail_id " +
		"LEFT JOIN sales ssv ON sv.sales_fkid = ssv.sales_id " +
		"WHERE sv.time_created >= ? " +
		"AND sv.time_created <= ? " +
		"AND ssv.status = 'success' " +
		"AND ssv.data_status = 'on' " +
		"AND ssv.payment NOT IN ('COMPLIMENT','DUTY MEALS') " +
		whereInClauseItemVoid + " " +
		"GROUP BY pdsv.product_fkid, pdsv.variant_fkid", timeStart,timeEnd)
	//utils.CheckErr(err)
	//fmt.Println(queryItemVoid)

	//get item sold
	rawSQL = db.GetSQLRaw("SELECT " +
		"IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) AS item_id," +
		"IF(pd.variant_fkid IS NULL, p.name, concat(p.name,' (',v.variant_name,')')) AS item_name," +
		"sum(sd.qty) AS qty_buy," +
		"sum((sd.sub_total) - sd.discount) AS subtotal_buy," +
		"IFNULL(jsv.qty,0) AS qty_void," +
		"IFNULL(jsv.subtotal_void,0) AS subtotal_void," +
		"sum(sd.qty) - IFNULL(jsv.qty,0) AS qty," +
		"sum((sd.sub_total) - sd.discount) + IFNULL(jsv.subtotal_void,0) AS subtotal " +
		"FROM sales_detail sd " +
		"LEFT JOIN sales s ON s.sales_id = sd.sales_fkid " +
		"LEFT JOIN outlets o ON s.outlet_fkid = o.outlet_id " +
		"LEFT JOIN products_detail pd ON sd.product_detail_fkid = pd.product_detail_id " +
		"LEFT JOIN products p ON p.product_id = pd.product_fkid " +
		"LEFT JOIN products_detail_variant v ON pd.variant_fkid = v.variant_id " +
		"LEFT JOIN ("+queryItemVoid+") jsv ON jsv.item_id = IF(pd.variant_fkid IS NULL, pd.product_fkid, concat(pd.product_fkid,'-',pd.variant_fkid)) " +
		//"WHERE o.admin_fkid = ? " +
		//"AND s.time_created >= ? " +
		//"AND s.time_created <= ? " +
		"LEFT JOIN open_shift os ON os.open_shift_id=s.open_shift_fkid " +
		"WHERE o.admin_fkid=? " +
		"AND os.time_open >=? " +
		"AND os.time_open <=? " +
		"AND s.status = 'success' " +
		"AND s.data_status = 'on' " +
		"AND s.payment NOT IN ('COMPLIMENT','DUTY MEALS') " +
		whereInClauseItemSold +
		itemSoldHavingInClause +
		"GROUP BY pd.product_fkid, pd.variant_fkid, jsv.subtotal_void, jsv.qty " +
		orderByQuery + " " +
		limitQuery, adminId, timeStart,timeEnd)

	return rawSQL
}

func SalesTopByShift_map(adminId string, timeStart, timeEnd, timeStartPrev, timeEndPrev int64, outletIds []interface{}, limit interface{}) map[string]interface{} {

	//get top item on range data
	//-- qty
	chanTopQty := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, qty " +
			"FROM (" + queryGetItemSoldByShift(adminId, timeStart, timeEnd, outletIds, nil, "qty DESC, item_name ASC", limit) +") tb" )
		utils.CheckErr(err)
		chanTopQty <- query
	}()
	//-- nominal
	chanTopNominal := make(chan []map[string]interface{})
	go func() {
		query, err := db.QueryArray("SELECT item_id, item_name, subtotal " +
			"FROM (" + queryGetItemSoldByShift(adminId, timeStart, timeEnd, outletIds, nil, "subtotal DESC, qty DESC, item_name ASC", limit) +") tb")
		utils.CheckErr(err)
		chanTopNominal <- query
	}()
	//----- tampung
	topQtyResult := <-chanTopQty
	topNominalResult := <-chanTopNominal


	//catch item_id to get data on previous range (with go routine)
	//timeStartX := time.Now()
	//-- item_ids for qty
	chanItemIdsQty := make(chan []interface{})
	go func() {
		var itemIdsQty []interface{}
		for _, data := range topQtyResult {
			itemIdsQty = append(itemIdsQty, data["item_id"])
		}
		chanItemIdsQty <- itemIdsQty
	}()
	//-- item_ids for nominal
	chanItemIdsNominal := make(chan []interface{})
	go func() {
		var itemIdsNominal []interface{}
		for _, data := range topNominalResult {
			itemIdsNominal = append(itemIdsNominal, data["item_id"])
		}
		chanItemIdsNominal <- itemIdsNominal
	}()
	//----- tampung
	itemIdsQty := <-chanItemIdsQty
	itemIdsNominal := <-chanItemIdsNominal
	//fmt.Printf("go: catch item_id took : %v\n", time.Since(timeStartX))


	//get data on previous range by on range item_id
	//-- qty
	chanTopQtyPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, qty FROM ("+queryGetItemSoldByShift(adminId,timeStartPrev,timeEndPrev,outletIds,itemIdsQty,"", limit)+") tb")
		utils.CheckErr(err)
		chanTopQtyPrevious <- queryPrevious
	}()
	//-- nominal
	chanTopNominalPrevious := make(chan []map[string]interface{})
	go func() {
		queryPrevious, err := db.QueryArray("" +
			"SELECT item_id, item_name, subtotal FROM ("+queryGetItemSoldByShift(adminId,timeStartPrev,timeEndPrev,outletIds,itemIdsNominal,"", limit)+") tb")
		utils.CheckErr(err)
		chanTopNominalPrevious <- queryPrevious
	}()
	//----- tampung
	topQtyResultPrevious := <-chanTopQtyPrevious
	topNominalResultPrevious := <-chanTopNominalPrevious


	//simpan hasil data previous ke map
	timeStartBenchmark := time.Now()
	//v1 (go for)
	//*
	//-- qty
	chanTopQtyPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topQtyResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["qty"]
		}
		chanTopQtyPreviousMap <- temp
	}()
	//-- nominal
	chanTopNominalPreviousMap := make(chan map[string]interface{})
	go func() {
		temp := map[string]interface{}{}
		for _, data := range topNominalResultPrevious {
			id := cast.ToString(data["item_id"])
			temp[id] = data["subtotal"]
		}
		chanTopNominalPreviousMap <- temp
	}()
	//-----catch go return
	topQtyPreviousMap := <- chanTopQtyPreviousMap
	topNominalPreviousMap := <- chanTopNominalPreviousMap
	//*/

	fmt.Printf("item_id to map took : %v\n", time.Since(timeStartBenchmark))


	//re-mappind data untuk dijadikan json
	chanTopQtyJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topQtyResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topQtyPreviousMap[id] != nil {
				prev = cast.ToString(topQtyPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id": id,
				"item_name": data["item_name"],
				"now": data["qty"],
				"prev": prev,
			}
			temp = append(temp,temp2)

			//sort by time prev desc if now ==
			if i>0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopQtyJson <- temp
	}()

	chanTopNominalJson := make(chan []map[string]interface{})
	go func() {
		var temp []map[string]interface{}
		for i, data := range topNominalResult {
			id := cast.ToString(data["item_id"])
			prev := "0"
			if topNominalPreviousMap[id] != nil {
				prev = cast.ToString(topNominalPreviousMap[id])
			}

			//render map
			temp2 := map[string]interface{}{
				"item_id": id,
				"item_name": data["item_name"],
				"now": data["subtotal"],
				"prev": prev,
			}
			temp = append(temp,temp2)

			//sort by time prev desc if now ==
			if i>0 {
				tempBefore := temp[i-1]
				if cast.ToInt64(temp2["now"]) == cast.ToInt64(tempBefore["now"]) && cast.ToInt64(temp2["prev"]) > cast.ToInt64(tempBefore["prev"]) {
					temp[i-1] = temp2
					temp[i] = tempBefore
				}
			}
		}
		chanTopNominalJson <- temp
	}()

	//topQtyJson := <-chanTopQtyJson
	//topNominalJson := <-chanTopNominalJson
	topQtyJson := []map[string]interface{}{}
	if q1:=<-chanTopQtyJson;q1!=nil {
		fmt.Println("qty tidak kosong")
		topQtyJson = q1
	}

	topNominalJson := []map[string]interface{}{}
	if q2:=<-chanTopNominalJson;q2!=nil {
		fmt.Println("nom tidak kosong")
		topNominalJson = q2
	}



	//render semua data
	response := map[string]interface{}{
		"nominal": topNominalJson,
		"qty": topQtyJson,
	}

	return response
}

func SalesTopByShift_v1(ctx *fasthttp.RequestCtx) {
	limit := ctx.UserValue("limit")
	post, err1 := djson.DecodeObject(ctx.PostBody())
	utils.CheckErr(err1)

	//validation struct
	var myPost ValidationDashboardRequest
	_ = json.Unmarshal(ctx.PostBody(), &myPost)

	err := MyValidation(myPost)
	if len(err) > 0 {
		ctx.SetStatusCode(http.StatusBadRequest)
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  false,
			Code:    400,
			Data:    err,
		})
	}else{
		adminId := string(ctx.Request.Header.Peek("admin_id"))
		outletAccess := string(ctx.Request.Header.Peek("outlet_access"))

		//get post value
		timeStart := cast.ToInt64(myPost.TimeStart)
		timeEnd := cast.ToInt64(myPost.TimeEnd)
		timeDiff := cast.ToInt64(myPost.TimeDiff) //timezone
		timeStartPrevious := timeStart-1 - (timeEnd-timeStart)
		timeEndPrevious := timeStart-1
		outletIds := cast.ToSlice(post["outlet_ids"])
		if outletIds == nil {
			oa := strings.Split(outletAccess,",")
			names := make([]interface{}, len(oa))
			for i, v := range oa {
				names[i] = v
			}
			outletIds = names
		}

		//set legend
		timeStartTime := utils.MillisToTime(timeStart+timeDiff)
		timeEndTime := utils.MillisToTime(timeEnd+timeDiff)
		timeStartPreviousTime := utils.MillisToTime(timeStartPrevious + timeDiff)
		legendNowText := ""
		legendPreviousText := ""
		if timeStartTime.Format("02-01-2006") == timeEndTime.Format("02-01-2006") {
			legendNowText = timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartTime.AddDate(0,0,-1).Format("02-01-2006")
		}else{
			legendNowText = timeStartTime.Format("02-01-2006") + " to "+timeEndTime.Format("02-01-2006")
			legendPreviousText = timeStartPreviousTime.Format("02-01-2006") + " to "+timeStartTime.AddDate(0,0,-1).Format("02-01-2006")
		}


		//check min.date data yang bisa diambil
		role := string(ctx.Request.Header.Peek("role"))
		timeStart = utils.Privilege{Role: role, TimeDiff: timeDiff}.DashboardDateMin(timeStart)

		//proses pengambilan data
		//render semua data
		response := map[string]interface{}{
			"result": SalesTopByShift_map(adminId, timeStart, timeEnd, timeStartPrevious, timeEndPrevious, outletIds, limit),
			"legend": map[string]interface{}{
				"now": legendNowText,
				"prev": legendPreviousText,
			},
		}

		//output
		_ = json.NewEncoder(ctx).Encode(models.ApiResponse{
			Status:  true,
			Code:    200,
			Data:    response,
		})
	}
}