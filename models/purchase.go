package models

import (
	"fmt"
	"time"
)

type PurchaseProductEntity struct {
	PurchaseProductsID int     `json:"purchase_products_id,omitempty"`
	PurchaseFkid       int     `json:"purchase_fkid,omitempty"`
	UnitFkidNota       int     `json:"unit_fkid_nota,omitempty"`
	QtyNota            float32 `json:"qty_nota,omitempty"`
	PriceNota          float32 `json:"price_nota,omitempty"`
	UnitFkidStok       int     `json:"unit_fkid_stok,omitempty"`
	QtyStok            float32 `json:"qty_stok,omitempty"`
	PriceStok          float32 `json:"price_stok,omitempty"`
	Discount           int     `json:"discount,omitempty"`
	Total              float64 `json:"total,omitempty"`
	TotDis             float32 `json:"tot_dis,omitempty"`
	DataStatus         string  `json:"data_status,omitempty"`
	DataCreated        string  `json:"data_created,omitempty"`
	ProductsFkid       int     `json:"products_fkid,omitempty"`
	QtyRetur           float32 `json:"qty_retur,omitempty"`
	Retur              string  `json:"retur,omitempty"`
	GratuityFkid       int     `json:"gratuity_fkid,omitempty"`
	Tax                float32 `json:"tax,omitempty"`
	Depreciation       int     `json:"depreciation,omitempty"`
	Expired            string  `json:"expired,omitempty"`
	DiscType           string  `json:"disc_type,omitempty"`
	TaxType            string  `json:"tax_type,omitempty"`
	Keterangan         string  `json:"keterangan,omitempty"`
}

// purchase_confrim_id
type PurchaseProductStockIn struct {
	PurchaseConfrimId  int     `json:"purchase_confrim_id,omitempty"`
	PurchaseProductsID int     `json:"purchase_products_id,omitempty"`
	PriceStok          float32 `json:"price_stok,omitempty"`
	ProductsFkid       int     `json:"products_fkid,omitempty"`
	ConfirmAt          int64   `json:"confirm_at,omitempty"`
	QtyArive           float32 `json:"qty_arive,omitempty"`
}

func (p *PurchaseProductEntity) ToStockInProduct(qty float32) *StockInProduct {
	stockInProduct := &StockInProduct{
		ProductDetailId: p.ProductsFkid,
		Qty:             qty,
		Price:           p.PriceStok,
		CreatedAt:       time.Now().UnixNano() / int64(time.Millisecond),
		MetaData: map[string]interface{}{
			"purchase_products_id": p.PurchaseProductsID,
		},
	}
	return stockInProduct
}

func (p *PurchaseProductStockIn) ToStockInProduct(qty float32) *StockInProduct {
	createdAt := p.ConfirmAt
	if createdAt == 0 {
		fmt.Println("PurchaseProductStockIn#ToStockInProduct,ConfirmAt is zero, use current time...")
		createdAt = time.Now().UnixNano() / int64(time.Millisecond)
	}
	stockInProduct := &StockInProduct{
		ProductDetailId: p.ProductsFkid,
		Qty:             qty,
		Price:           p.PriceStok,
		CreatedAt:       createdAt,
		MetaData: map[string]interface{}{
			"purchase_products_id": p.PurchaseProductsID,
			"created_at":           time.Now().UnixNano() / int64(time.Millisecond),
		},
	}
	return stockInProduct
}
