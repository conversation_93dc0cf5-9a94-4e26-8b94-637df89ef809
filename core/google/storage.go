package google

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func UploadFile(filePath, destinationPath string) error {
	if storageClient == nil {
		return fmt.Errorf("storage client hast not been initialized")
	}
	if strings.TrimSpace(filePath) == "" || strings.TrimSpace(destinationPath) == "" {
		return fmt.Errorf("filepath or destination path can not be empty")
	}

	ctx := context.Background()
	fileDirs := strings.Split(destinationPath, "/")
	bucketName := fileDirs[0]
	fmt.Println("upload to bucket", bucketName)

	// Open local file.
	f, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("os.Open: %v", err)
	}
	defer f.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Second*50)
	defer cancel()

	fileName := filepath.Base(filePath)
	if filepath.Ext(destinationPath) == "" {
		fileDirs = append(fileDirs, fileName)
	}
	fileDestination := filepath.Join(fileDirs[1:]...)

	// Upload an object with storage.Writer.
	wc := storageClient.Bucket(bucketName).Object(fileDestination).NewWriter(ctx)
	if _, err = io.Copy(wc, f); err != nil {
		return fmt.Errorf("io.Copy: %v", err)
	}
	if err := wc.Close(); err != nil {
		return fmt.Errorf("Writer.Close: %v", err)
	}

	fmt.Println("uploaded... to", bucketName, fileDestination)

	return nil
}
