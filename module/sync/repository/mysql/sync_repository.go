package mysql

import (
	"database/sql"
	"fmt"

	"cloud.google.com/go/bigquery"
	"github.com/spf13/cast"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/db"
	mysql "gitlab.com/uniqdev/backend/api-report/core/mysql"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/dashboard/repository"
)

type syncRepository struct {
	db mysql.Repository
	bq bq.Repository
}

func NewMysqlSyncRepository(db *sql.DB, client *bigquery.Client) domain.SyncRepository {
	return &syncRepository{mysql.Repository{Conn: db}, bq.Repository{Client: client}}
}

func (s syncRepository) FetchLastSyncSalesFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(time_modified) as last_sync from %ssales", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_sync"]), err
}

func (s syncRepository) FetchSalesAfter(lastSync int64) ([]map[string]interface{}, error) {
	sql := "select * from sales where time_modified > ?"
	return db.QueryArray(sql, lastSync)
}

func (s syncRepository) FetchLastSyncSalesDetailFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(time_created) as last_sync from %ssales_detail", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_sync"]), err
}

func (s syncRepository) FetchSalesDetailAfter(lastSync int64) ([]map[string]interface{}, error) {
	sql := `select sd.*
from sales_detail sd
         join sales s on sd.sales_fkid = s.sales_id
where s.time_modified > ?`
	return db.QueryArray(sql, lastSync)
}

func (s syncRepository) FetchLastSyncOpenShiftFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(data_modified) as last_sync from %sopen_shift", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_sync"]), err
}

func (s syncRepository) FetchOpenShiftAfter(lastSync int64) ([]map[string]interface{}, error) {
	sql := `select * from open_shift where data_modified  > ?`
	return db.QueryArray(sql, lastSync)
}

func (s syncRepository) FetchLastSalesDetailIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_detail_id) as last_id from %ssales_detail", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesDetailAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select sd.*
from sales_detail sd where sd.sales_detail_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) FetchLastVoidIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_void_id) as last_id from %ssales_void", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesVoidAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select *
from sales_void where sales_void_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) FetchLastSalesTaxIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_tax_id) as last_id from %ssales_tax", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesTaxAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select *
from sales_tax where sales_tax_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) FetchLastSalesDetailTaxIdFromBigQuery() (int64, error) {

	sql := fmt.Sprintf("select max(sales_detail_tax_id) as last_id from %ssales_detail_tax", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesDetailTaxAfterId(id int64) ([]map[string]interface{}, error) {
	sql := `select * from sales_detail_tax where sales_detail_tax_id > ?`
	return db.QueryArray(sql, id)
}

func (s syncRepository) FetchLastSalesDetailDiscIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_detail_discount_id) as last_id from %ssales_detail_discount", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesDetailDiscAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select * from sales_detail_discount where sales_detail_discount_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) FetchLastSalesPromotionIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_promotion_id) as last_id from %ssales_promotion", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesPromotionAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select * from sales_promotion where sales_promotion_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) FetchLastSalesDetailPromotionIdFromBigQuery() (int64, error) {
	sql := fmt.Sprintf("select max(sales_detail_promotion_id) as last_id from %ssales_detail_promotion", s.bq.DbName())
	data, err := s.bq.Query(sql).Map()
	return cast.ToInt64(data["last_id"]), err
}

func (s syncRepository) FetchSalesDetailPromotionAfterId(lastId int64) ([]map[string]interface{}, error) {
	sql := `select * from sales_detail_promotion where sales_detail_promotion_id > ?`
	return db.QueryArray(sql, lastId)
}

func (s syncRepository) UpdateMaxTimeSync() {
	repository.SetMaxTime(s.bq.Client)
}
