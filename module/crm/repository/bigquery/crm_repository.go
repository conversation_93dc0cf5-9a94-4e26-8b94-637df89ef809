package bigquery

import (
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"gitlab.com/uniqdev/backend/api-report/core/bq"
	"gitlab.com/uniqdev/backend/api-report/core/utils/cast"
	domain "gitlab.com/uniqdev/backend/api-report/domain"
	"gitlab.com/uniqdev/backend/api-report/module/crm"
)

type crmRepository struct {
	bq         bq.Repository
	queryParam domain.RequestParam
}

func NewBigQueryCrmRepository(client *bigquery.Client) crm.Repository {
	return &crmRepository{bq: bq.Repository{Client: client}}
}

func (c crmRepository) QueryParam(param domain.RequestParam) crm.Repository {
	c.queryParam = param
	return c
}

func (c crmRepository) FetchCustomerList(user domain.User) ([]map[string]interface{}, error) {
	sql := `SELECT
	SUM(grand_total) AS grand_total,
	ANY_VALUE(customer_name) AS customer_name, ANY_VALUE(receipt_receiver) as receipt_receiver, 
	MIN(s.time_created) as date_transaction_min,
	MAX(s.time_created) as date_transaction_max,
	COUNT(*) as total_bill
  FROM
	` + c.bq.DbName() + `sales s 
	join ` + c.bq.DbName() + `outlets o on o.outlet_id=s.outlet_fkid
  WHERE
	status='Success'  
	AND LENGTH(customer_name) >= 3
	AND (s.member_fkid IS NULL or s.member_fkid = '')
	and o.admin_fkid = @adminId
	{{SEARCH}}
  GROUP BY
  {{GROUPBY}}
  `
	//COALESCE(receipt_receiver, customer_name)

	if strings.TrimSpace(c.queryParam.Search) != "" {
		c.queryParam.Search = strings.ToLower(c.queryParam.Search)
		c.queryParam.Search = " AND ( LOWER(s.customer_name) LIKE '%" + c.queryParam.Search + "%' OR LOWER(s.receipt_receiver) LIKE '%" + c.queryParam.Search + "%' ) "
	}

	if c.queryParam.DateStart > 0 && c.queryParam.DateEnd > 0 {
		c.queryParam.Search += fmt.Sprintf(" and s.time_created between @startDate and @endDate ")
	}

	if len(c.queryParam.OutletId) > 0 {
		c.queryParam.Search += fmt.Sprintf(" and s.outlet_fkid in UNNEST(@outletId) ")
	}

	if c.queryParam.GroupBy == "transaction" {
		sql = strings.Replace(sql, "{{GROUPBY}}", "s.sales_id", 1)
	} else {
		sql = strings.Replace(sql, "{{GROUPBY}}", "if(receipt_receiver is null or LENGTH(receipt_receiver) = 0, customer_name, receipt_receiver)", 1)
	}

	order := "customer_name ASC"
	if c.queryParam.Order.Column != "" {
		order = fmt.Sprintf("%s %s", c.queryParam.Order.Column, c.queryParam.Order.Direction)
	}
	sql += " ORDER BY " + order

	sql = strings.Replace(sql, "{{SEARCH}}", c.queryParam.Search, 1)

	if c.queryParam.IsCount {
		sql = fmt.Sprintf("select count(*) as cnt from (%v)", sql)
	} else {
		sql += c.queryParam.LimitQuery()
	}

	sqlParams := bq.MapParam(map[string]interface{}{
		"startDate": c.queryParam.DateStart,
		"endDate":   c.queryParam.DateEnd,
		"adminId":   cast.ToInt64(user.BusinessId),
		"outletId":  c.queryParam.OutletId,
	})

	return c.bq.Query(sql, sqlParams...).MapArray()
}

func (c crmRepository) FetchCustomerTransactionList(user domain.User) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmRepository) FetchSalesMember(user domain.User, request domain.RequestParam) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}

func (c crmRepository) FetchMemberRegisterRange(adminId int, param domain.RequestParam) ([]map[string]interface{}, error) {
	panic("not implemented") // TODO: Implement
}
